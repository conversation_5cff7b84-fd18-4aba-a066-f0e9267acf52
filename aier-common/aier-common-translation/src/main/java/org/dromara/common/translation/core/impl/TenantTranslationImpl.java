package org.dromara.common.translation.core.impl;

import lombok.AllArgsConstructor;
import org.dromara.common.core.service.TenantService;
import org.dromara.common.translation.annotation.TranslationType;
import org.dromara.common.translation.constant.TransConstant;
import org.dromara.common.translation.core.TranslationInterface;

/**
 * 租户翻译实现
 */
@AllArgsConstructor
@TranslationType(type = TransConstant.TENANT_ID_TO_NAME)
public class TenantTranslationImpl implements TranslationInterface<String> {

    private final TenantService tenantService;

    @Override
    public String translation(Object key, String other) {
        if (key instanceof String tenantId) {
            return tenantService.getTenantName(tenantId);
        }
        return null;
    }
}
