package org.dromara.common.translation.core.impl;

import lombok.AllArgsConstructor;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.utils.ip.AddressUtils;
import org.dromara.common.translation.annotation.TranslationType;
import org.dromara.common.translation.constant.TransConstant;
import org.dromara.common.translation.core.TranslationInterface;

/**
 * IP属地翻译实现
 */
@AllArgsConstructor
@TranslationType(type = TransConstant.IP_TO_LOCATION)
public class IPTranslationImpl implements TranslationInterface<String> {

    @Override
    public String translation(Object key, String other) {

        if (key instanceof String ip) {
            // 获取实际地址：中国|湖南省|长沙市|电信
            String realAddress = AddressUtils.getRealAddressByIP(ip);
            if (StringUtils.isEmpty(realAddress)) {
                return null;
            }
            String[] address = realAddress.split("\\|");
            // 检查地址数组的长度是否满足要求
            if (address.length >= 2) {
                return address[1]; // 只返回省份信息
            } else {
                return "未知"; // 或者其他适当的默认值
            }
        }
        return null;
    }
}
