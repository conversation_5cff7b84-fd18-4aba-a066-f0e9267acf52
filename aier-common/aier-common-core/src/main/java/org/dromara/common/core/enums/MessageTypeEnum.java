package org.dromara.common.core.enums;

import lombok.Getter;

/**
 * 待办投诉消息类型枚举.
 */
public enum MessageTypeEnum {

    /**
     * 评价回复消息类型
     */
    EVALUATION_RESPONSE("评价回复", "0"),

    /**
     * 处理结果消息类型
     */
    PROCESS_RESULT("处理结果", "1");

    @Getter
    private String description;
    @Getter
    private String code;

    MessageTypeEnum(String description, String code) {
        this.description = description;
        this.code = code;
    }

    /**
     * 根据编码获取消息类型枚举对象.
     *
     * @param code 编码值
     * @return 消息类型枚举对象
     * @throws IllegalArgumentException 如果提供的code无效
     */
    public static MessageTypeEnum ofCode(String code) {
        for (MessageTypeEnum type : values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid message type code: " + code);
    }
}
