package org.dromara.common.core.enums;

import lombok.Getter;

/**
 * app用户角色枚举
 */
@Getter
public enum AppUserRoleEnum {
    STAFF("员工"),
    SALES("销售"),
    BOSS("老板"),
    CUSTOMER("客户"),
    SALES_MANAGER("销售主管"),
    APP_SUPER_ADMIN("超级管理员"),
    ADMIN("管理员"),
    NURSE("护理"),
    USER("普通用户"),
    POSTPARTUM("产康"),
    CHEF("厨师"),
    MATERNITY_NANNY("月嫂"),
    ;

    private final String description;

    // 构造函数
    AppUserRoleEnum(String description) {
        this.description = description;
    }


    public static String getValueByKey(String key) {
        for (AppUserRoleEnum roleEnum : AppUserRoleEnum.values()) {
            if (roleEnum.name().equals(key)) {
                return roleEnum.description;
            }
        }
        return null;
    }
}
