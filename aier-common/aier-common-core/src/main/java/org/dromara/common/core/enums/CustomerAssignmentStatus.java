package org.dromara.common.core.enums;

import lombok.Getter;

/**
 * 客咨分配状态
 */
@Getter
public enum CustomerAssignmentStatus {
    ALLOCATED("ALLOCATED", "已分配"),
    NOT_ALLOCATED("NOT_ALLOCATED", "未分配");

    private final String code;
    private final String description;

    CustomerAssignmentStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    @Override
    public String toString() {
        return this.code;
    }
}
