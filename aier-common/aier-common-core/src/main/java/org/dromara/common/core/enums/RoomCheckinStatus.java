package org.dromara.common.core.enums;

import lombok.Getter;

/**
 * 房间入住状态枚举.
 */
@Getter
public enum RoomCheckinStatus {

    /**
     * 未入住状态
     */
    NOT_CHECKED_IN("0", "未入住"),

    /**
     * 待入住状态
     */
    PENDING_CHECKIN("1", "待入住"),

    /**
     * 已入住状态
     */
    CHECKED_IN("2", "已入住");

    private final String code;
    private final String description;

    RoomCheckinStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据编码获取入住状态枚举对象.
     *
     * @param code 编码值
     * @return 入住状态枚举对象
     * @throws IllegalArgumentException 如果提供的code无效
     */
    public static RoomCheckinStatus ofCode(String code) {
        for (RoomCheckinStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new IllegalArgumentException("Invalid check-in status code: " + code);
    }
}
