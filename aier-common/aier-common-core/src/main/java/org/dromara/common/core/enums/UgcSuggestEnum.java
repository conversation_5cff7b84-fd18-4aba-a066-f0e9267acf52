package org.dromara.common.core.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 审查状态 risky、pass、review三种值
 */
@Getter
@AllArgsConstructor
public enum UgcSuggestEnum {

    /**
     * 危险
     */
    RISKY("risky"),
    /**
     * 通过
     */
    PASS("pass"),
    /**
     * 复查
     */
    REVIEW("review"),
    /**
     * 待审核
     */
    PENDING("pending")
    ;

    private final String suggest;

    // 可以根据code获取对应的枚举值
    public static UgcSuggestEnum getBySuggest(String suggest) {
        for (UgcSuggestEnum ugcSuggestEnum : UgcSuggestEnum.values()) {
            if (ugcSuggestEnum.getSuggest().equals(suggest)) {
                return ugcSuggestEnum;
            }
        }
        return null;
    }
}
