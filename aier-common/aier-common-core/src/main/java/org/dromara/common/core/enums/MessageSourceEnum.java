package org.dromara.common.core.enums;

import lombok.Getter;

/**
 * 消息来源枚举.
 */
@Getter
public enum MessageSourceEnum {

    /**
     * 社区消息来源
     */
    COMMUNITY("1", "社区"),

    /**
     * 朋友圈消息来源
     */
    MOMENTS("2", "朋友圈");

    private final String code;
    private final String description;

    MessageSourceEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据编码获取消息来源枚举对象.
     *
     * @param code 编码值
     * @return 消息来源枚举对象
     * @throws IllegalArgumentException 如果提供的code无效
     */
    public static MessageSourceEnum ofCode(String code) {
        for (MessageSourceEnum source : values()) {
            if (source.getCode().equals(code)) {
                return source;
            }
        }
        throw new IllegalArgumentException("Invalid message source code: " + code);
    }
}
