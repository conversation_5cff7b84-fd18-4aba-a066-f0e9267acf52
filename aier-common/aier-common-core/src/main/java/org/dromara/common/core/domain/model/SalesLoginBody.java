package org.dromara.common.core.domain.model;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.hibernate.validator.constraints.Length;

import static org.dromara.common.core.constant.UserConstants.*;

/**
 * 销售端密码登录对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SalesLoginBody extends LoginBody {

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @Length(min = USERNAME_MIN_LENGTH, max = USERNAME_MAX_LENGTH, message = "{user.username.length.valid}")
    private String phone;

    /**
     * 用户密码
     */
    @NotBlank(message = "密码不能为空")
    @Length(min = PASSWORD_MIN_LENGTH, max = PASSWORD_MAX_LENGTH, message = "{user.password.length.valid}")
    private String password;

    /**
     * 微信登录code
     */
    private String code;

    /**
     * 微信小程序appid
     */
    private String appid;

}
