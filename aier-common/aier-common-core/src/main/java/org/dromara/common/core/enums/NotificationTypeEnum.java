package org.dromara.common.core.enums;

import lombok.Getter;

/**
 * 通知类型枚举.
 */
public enum NotificationTypeEnum {

    /**
     * 动态点赞
     */
    LIKE("1", "点赞"),

    /**
     * 评论点赞
     */
    LIKE_COMMENT("4", "评论点赞"),

    /**
     * 评论
     */
    COMMENT("2", "评论"),

    /**
     * 评论回复
     */
    REPLY("3", "回复");

    @Getter
    private String code;
    @Getter
    private String description;

    NotificationTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 根据编码获取通知类型枚举对象.
     *
     * @param code 编码值
     * @return 通知类型枚举对象
     * @throws IllegalArgumentException 如果提供的code无效
     */
    public static NotificationTypeEnum ofCode(String code) {
        for (NotificationTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Invalid notification type code: " + code);
    }
}
