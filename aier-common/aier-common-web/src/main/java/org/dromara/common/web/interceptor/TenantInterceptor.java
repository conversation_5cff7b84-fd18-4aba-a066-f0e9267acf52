package org.dromara.common.web.interceptor;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * 多租户拦截器
 * 拦截小程序端未登录的请求，动态设置为小程序端传入的租户id，请求结束清理线程内租户信息
 */
@Component
public class TenantInterceptor implements HandlerInterceptor {
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        if(isMpRequest(request) && !LoginHelper.isLogin()){
            String tenantId = request.getHeader("X-Tenant-ID");
            if (tenantId != null) {
                TenantHelper.setDynamic(tenantId);
            }
            return true;
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        if(isMpRequest(request)){
            TenantHelper.clearDynamic();
        }
    }

    public boolean isMpRequest (HttpServletRequest request) {
        String uri = request.getRequestURI();
        return uri.startsWith("/mp/");
    }
}