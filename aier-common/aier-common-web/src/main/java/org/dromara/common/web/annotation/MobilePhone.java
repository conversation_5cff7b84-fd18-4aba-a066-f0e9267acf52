package org.dromara.common.web.annotation;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;
import org.dromara.common.web.strict.MobilePhoneValidator;

import java.lang.annotation.Documented;
import java.lang.annotation.Retention;
import java.lang.annotation.Target;

import static java.lang.annotation.ElementType.FIELD;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

/**
 * 字符串必须是格式正确的手机号码。正确格式为：11位数字。
 * <p>
 * {@code null} 或 空字符串，是有效的（能够通过校验）。
 * <p>
 * 支持的类型：字符串
 *
 * <AUTHOR> @since 1.0
 */
@Target({FIELD})
@Retention(RUNTIME)
@Documented
@Constraint(validatedBy = MobilePhoneValidator.class)
public @interface MobilePhone {

    /**
     * @return the error message template
     */
    String message() default "手机号码，格式错误";

    /**
     * @return the groups the constraint belongs to
     */
    Class<?>[] groups() default {};

    /**
     * @return the payload associated to the constraint
     */
    Class<? extends Payload>[] payload() default {};

    /**
     * 手机号码的详细描述。
     * <p>
     * 用于用户提示中，当页面中存在多个手机号码时，帮助用户更好的区分是哪个手机号码填错了。
     */
    String description() default "手机号码";
}

