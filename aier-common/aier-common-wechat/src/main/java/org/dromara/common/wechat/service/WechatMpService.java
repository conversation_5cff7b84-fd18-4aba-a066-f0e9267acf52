package org.dromara.common.wechat.service;

import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;

/**
 * 微信公众号服务接口
 *
 * <AUTHOR>
 */
public interface WechatMpService {

    /**
     * 获取WxMpService实例
     *
     * @return WxMpService实例
     */
    WxMpService getWxMpService();

    /**
     * 根据appid获取对应的WxMpService
     *
     * @param appid 微信公众号appid
     * @return WxMpService实例
     */
    WxMpService getWxMpServiceByAppid(String appid);

    /**
     * 处理微信消息
     *
     * @param wxMessage 微信消息
     * @param appid 微信公众号appid
     * @return 响应消息
     */
    WxMpXmlOutMessage route(WxMpXmlMessage wxMessage, String appid);

    /**
     * 刷新WxMpService配置
     */
    void refreshWxMpService();
}
