package org.dromara.common.wechat.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 微信公众号配置实体
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("sys_wechat_mp_config")
public class WechatMpConfig {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 配置名称
     */
    private String name;

    /**
     * 微信公众号appid
     */
    private String appid;

    /**
     * 微信公众号secret
     */
    private String secret;

    /**
     * 微信公众号token
     */
    private String token;

    /**
     * 微信公众号aesKey
     */
    private String aesKey;

    /**
     * 备注
     */
    private String remark;

    /**
     * 状态（0正常 1停用）
     */
    private String status;
}
