package org.dromara.common.wechat.service;

import org.dromara.common.wechat.entity.WechatMiniProgram;

import java.util.List;

/**
 * 微信小程序配置服务接口
 *
 * <AUTHOR>
 */
public interface WechatMiniProgramService {

    /**
     * 查询微信小程序配置列表
     *
     * @param tenantId 租户ID
     * @return 微信小程序配置列表
     */
    List<WechatMiniProgram> queryList(String tenantId);

    /**
     * 查询微信小程序配置列表（忽略租户）
     *
     * @param status 状态
     * @return 微信小程序配置列表
     */
    List<WechatMiniProgram> queryListIgnoreTenant(String status);

    /**
     * 根据AppID查询微信小程序配置
     *
     * @param appid 微信小程序appid
     * @return 微信小程序配置
     */
    WechatMiniProgram getByAppid(String appid);

    /**
     * 保存微信小程序配置
     *
     * @param config 微信小程序配置
     * @return 结果
     */
    int insertConfig(WechatMiniProgram config);

    /**
     * 更新微信小程序配置
     *
     * @param config 微信小程序配置
     * @return 结果
     */
    int updateConfig(WechatMiniProgram config);

    /**
     * 删除微信小程序配置
     *
     * @param id 配置ID
     * @return 结果
     */
    int deleteConfig(Long id);
}
