


-- 1. 查询指定时间段内创建的用户的信息
SELECT
    user_id , status
FROM
    biz_wechat_user
WHERE
    tel IS NOT NULL
  AND last_login_time IS NOT NULL
  AND create_time BETWEEN '2024-07-06 10:00:00'
    AND '2024-07-07 23:59:59'


-- 2. 批量更新指定时间段内创建的用户的状态
UPDATE biz_wechat_user u
    JOIN (
    SELECT user_id
    FROM biz_wechat_user
    WHERE tel IS NOT NULL
    AND last_login_time IS NOT NULL
    AND create_time BETWEEN '2024-07-06 10:00:00' AND '2024-07-07 23:59:59'
    ) t ON u.user_id = t.user_id
    SET u.status = '1';

/**
  根据员工信息创建微信用户
 */

-- 根据用户名称获取微信用户id、用户名称、租户id
SELECT u.user_id, u.nickname, u.tenant_id FROM biz_wechat_user u WHERE u.tenant_id = '186411' AND u.nickname IN ('黄绍叶','刘冬','李培','丁付敏','周宗琼','张钰梅')

-- 获取指定房间的服务人员
SELECT service_staff_ids FROM biz_rooms WHERE tenant_id = '186411' AND room_number = '8888'

-- 根据员工id获取员工信息
SELECT * FROM biz_staff WHERE tenant_id = '186411' AND staff_id IN(44,39,41,35,34,48)

-- 根据微信用户名称更新员工表绑定用户id
UPDATE biz_staff s
    JOIN (SELECT u.user_id, u.nickname, u.tenant_id FROM biz_wechat_user u
    WHERE u.tenant_id = '186411' AND u.nickname IN ('黄绍叶','刘冬','李培','丁付敏','周宗琼','张钰梅')
    ) temp ON s.staff_name = temp.nickname and s.tenant_id = temp.tenant_id
    SET s.user_id = temp.user_id

-- 根据员工表创建微信用户
INSERT INTO biz_wechat_user (tenant_id,utype,tel,nickname,sex,avatar,`status`,create_time)
SELECT
    s.tenant_id,
    '1',
    s.staff_tel,
    s.staff_name,
    '1',
    s.staff_photos,
    '0',
    NOW()
FROM
    biz_staff s
WHERE
    s.tenant_id = '186411'
  AND s.staff_id IN (44, 39, 41, 35, 34, 48)

-- 查询指定时间内的手机号信息
SELECT
    tel as 电话 , create_time as 时间
FROM
    biz_wechat_user
WHERE
    tel IS NOT NULL
  AND create_time BETWEEN '2024-07-22 20:00:00'
    AND '2024-07-24 23:59:59'

-- 删除动态点赞用户记录 用户已经被删除 但是点赞记录还存在
DELETE pl
FROM `biz_feed_post_links` pl
LEFT JOIN `biz_wechat_user` u ON pl.user_id = u.user_id
WHERE u.user_id IS NULL;

-- 查询重复用户数据
SELECT tenant_id, tel, COUNT(*) as count
FROM `biz_wechat_user`
where tel is not null
GROUP BY tenant_id, tel
HAVING COUNT(*) > 1;

-- 更改租户
select * from `biz_employee` where phone = '13833083160' and tenant_id = '612935'
select * from `biz_employee` where tenant_id = '932828'

-- 管理端tenant_id = 612935

-- 销售端 tenant_id = 932828

select * from biz_customers where tenant_id = '612935'
update biz_customers set tenant_id = '932828' where tenant_id = '612935'

select * from `biz_customer_channel` where tenant_id = '612935'
update biz_customer_channel set tenant_id = '932828' where tenant_id = '612935'
select * from `biz_customer_contract` where tenant_id = '612935'
update biz_customer_contract set tenant_id = '932828' where tenant_id = '612935'

select * from `biz_employee` where tenant_id = '612935'
update biz_employee set tenant_id = '932828' where tenant_id = '612935'

select * from `biz_employee_room` where tenant_id = '612935'
update biz_employee_room set tenant_id = '932828' where tenant_id = '612935'

select * from `biz_packages` where tenant_id = '612935'
update biz_packages set tenant_id = '932828' where tenant_id = '612935'

select * from `biz_rooms` where tenant_id = '612935'
update biz_rooms set tenant_id = '932828' where tenant_id = '612935'

select * from `biz_room_reservations` where tenant_id = '612935'
update biz_room_reservations set tenant_id = '932828' where tenant_id = '612935'
select * from `biz_suites` where tenant_id = '612935'
update biz_suites set tenant_id = '932828' where tenant_id = '612935'
select * from `biz_wechat_user_role` where tenant_id = '612935'
update biz_wechat_user_role set tenant_id = '932828' where tenant_id = '612935'
select * from `biz_wechat_user` where tenant_id = '612935'
update biz_wechat_user set tenant_id = '932828' where tenant_id = '612935'

-- 清空某个租户的所有客户信息

-- select group_concat(c.user_id) from biz_customers c where c.user_id is not null and c.tenant_id = '543713'
-- 1851161392548155394,1851457070222544897
--
-- select * from biz_customer_contract cc where cc.tenant_id = '543713'
--
-- delete from biz_customer_contract where tenant_id = '543713'
--
-- delete from biz_room_occupations where tenant_id = '543713'
--
-- delete from biz_room_reservations where tenant_id = '543713'
--
-- delete from biz_room_occupation_fws where tenant_id = '543713'
--
-- delete from biz_customer_operation_logs where tenant_id = '543713'
--
-- delete from biz_customer_follow_up_logs where tenant_id = '543713'
--
-- delete from biz_customer_intent_leve_record where tenant_id = '543713'
--
-- delete from biz_sales_notification where tenant_id = '543713'
--
-- delete from biz_customers where tenant_id = '543713'