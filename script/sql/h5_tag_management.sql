-- H5模版标签管理系统数据库表结构
-- 创建时间: 2025-01-27
-- 说明: 为H5模版系统添加标签管理功能

-- 1. 创建标签表
CREATE TABLE l_tag (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    tag_name VARCHAR(50) NOT NULL COMMENT '标签名称',
    tag_desc VARCHAR(200) COMMENT '标签描述',
    tag_color VARCHAR(7) DEFAULT '#1890ff' COMMENT '标签颜色(十六进制)',
    sort_order INT DEFAULT 0 COMMENT '排序权重，数值越大越靠前',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志(0正常 2删除)',
    UNIQUE KEY uk_tag_name (tag_name) COMMENT '标签名称唯一索引',
    KEY idx_sort_order (sort_order) COMMENT '排序索引',
    KEY idx_create_time (create_time) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='H5模版标签表';

-- 2. 创建模版标签关联表
CREATE TABLE l_opus_tag (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    opus_id BIGINT NOT NULL COMMENT '模版ID，关联l_opus表',
    tag_id BIGINT NOT NULL COMMENT '标签ID，关联l_tag表',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_opus_tag (opus_id, tag_id) COMMENT '模版标签唯一索引',
    KEY idx_opus_id (opus_id) COMMENT '模版ID索引',
    KEY idx_tag_id (tag_id) COMMENT '标签ID索引',
    KEY idx_create_time (create_time) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='模版标签关联表';

-- 3. 插入初始标签数据（示例）
INSERT INTO l_tag (tag_name, tag_desc, tag_color, sort_order) VALUES
('商务风格', '适用于商务场景的H5模版', '#1890ff', 100),
('活动推广', '用于活动宣传推广的模版', '#52c41a', 90),
('产品展示', '展示产品特色的模版', '#fa8c16', 80),
('节日庆典', '节日主题相关模版', '#eb2f96', 70),
('企业宣传', '企业形象宣传模版', '#722ed1', 60),
('教育培训', '教育行业专用模版', '#13c2c2', 50),
('医疗健康', '医疗健康行业模版', '#f5222d', 40),
('时尚潮流', '时尚类主题模版', '#fa541c', 30),
('科技创新', '科技感强的模版', '#2f54eb', 20),
('简约清新', '简洁清新风格模版', '#52c41a', 10);

-- 4. 添加外键约束（可选，根据实际需求决定是否启用）
-- ALTER TABLE l_opus_tag ADD CONSTRAINT fk_opus_tag_opus FOREIGN KEY (opus_id) REFERENCES l_opus(id) ON DELETE CASCADE;
-- ALTER TABLE l_opus_tag ADD CONSTRAINT fk_opus_tag_tag FOREIGN KEY (tag_id) REFERENCES l_tag(id) ON DELETE CASCADE;

-- 5. 创建视图：标签使用统计
CREATE VIEW v_tag_usage_stats AS
SELECT 
    t.id,
    t.tag_name,
    t.tag_desc,
    t.tag_color,
    t.sort_order,
    COALESCE(ot.usage_count, 0) as usage_count,
    t.create_time,
    t.update_time
FROM l_tag t
LEFT JOIN (
    SELECT tag_id, COUNT(*) as usage_count
    FROM l_opus_tag
    GROUP BY tag_id
) ot ON t.id = ot.tag_id
WHERE t.del_flag = '0'
ORDER BY t.sort_order DESC, t.create_time DESC;

-- 6. 创建索引优化查询性能
CREATE INDEX idx_tag_name_like ON l_tag(tag_name);
CREATE INDEX idx_opus_tag_composite ON l_opus_tag(opus_id, tag_id, create_time);
