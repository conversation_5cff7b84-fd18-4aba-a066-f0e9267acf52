-- =============================================
-- AI知识库管理系统菜单权限配置
-- 作者: Dummy Puppy
-- 创建时间: 2025-01-27
-- 说明: 为AI知识库管理功能添加菜单和权限配置
-- =============================================

-- ----------------------------
-- 1. 菜单配置
-- ----------------------------

-- AI管理主菜单
INSERT INTO `sys_menu` VALUES 
(2000, 'AI管理', 0, 1, 'ai', NULL, NULL, 1, 0, 'M', '0', '0', '', 'robot', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', 'AI人工智能管理');

-- 知识库管理菜单
INSERT INTO `sys_menu` VALUES 
(2001, '知识库管理', 2000, 1, 'knowledge-base', 'ai/knowledge-base/index', NULL, 1, 0, 'C', '0', '0', 'ai:knowledgeBase:list', 'documentation', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '知识库管理菜单');

-- 知识库管理按钮权限
INSERT INTO `sys_menu` VALUES 
(2002, '知识库查询', 2001, 1, '', '', NULL, 1, 0, 'F', '0', '0', 'ai:knowledgeBase:query', '#', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '');

INSERT INTO `sys_menu` VALUES 
(2003, '知识库新增', 2001, 2, '', '', NULL, 1, 0, 'F', '0', '0', 'ai:knowledgeBase:add', '#', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '');

INSERT INTO `sys_menu` VALUES 
(2004, '知识库修改', 2001, 3, '', '', NULL, 1, 0, 'F', '0', '0', 'ai:knowledgeBase:edit', '#', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '');

INSERT INTO `sys_menu` VALUES 
(2005, '知识库删除', 2001, 4, '', '', NULL, 1, 0, 'F', '0', '0', 'ai:knowledgeBase:remove', '#', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '');

INSERT INTO `sys_menu` VALUES 
(2006, '知识库导出', 2001, 5, '', '', NULL, 1, 0, 'F', '0', '0', 'ai:knowledgeBase:export', '#', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '');

-- 文档管理菜单
INSERT INTO `sys_menu` VALUES 
(2010, '文档管理', 2000, 2, 'document', 'ai/document/index', NULL, 1, 0, 'C', '0', '0', 'ai:document:list', 'file-text', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '文档管理菜单');

-- 文档管理按钮权限
INSERT INTO `sys_menu` VALUES 
(2011, '文档查询', 2010, 1, '', '', NULL, 1, 0, 'F', '0', '0', 'ai:document:query', '#', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '');

INSERT INTO `sys_menu` VALUES 
(2012, '文档新增', 2010, 2, '', '', NULL, 1, 0, 'F', '0', '0', 'ai:document:add', '#', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '');

INSERT INTO `sys_menu` VALUES 
(2013, '文档修改', 2010, 3, '', '', NULL, 1, 0, 'F', '0', '0', 'ai:document:edit', '#', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '');

INSERT INTO `sys_menu` VALUES 
(2014, '文档删除', 2010, 4, '', '', NULL, 1, 0, 'F', '0', '0', 'ai:document:remove', '#', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '');

INSERT INTO `sys_menu` VALUES 
(2015, '文档上传', 2010, 5, '', '', NULL, 1, 0, 'F', '0', '0', 'ai:document:upload', '#', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '');

INSERT INTO `sys_menu` VALUES 
(2016, '文档导出', 2010, 6, '', '', NULL, 1, 0, 'F', '0', '0', 'ai:document:export', '#', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '');

-- 向量检索菜单
INSERT INTO `sys_menu` VALUES 
(2020, '向量检索', 2000, 3, 'vector-search', 'ai/vector-search/index', NULL, 1, 0, 'C', '0', '0', 'ai:vector:search', 'search', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '向量检索菜单');

-- 向量检索按钮权限
INSERT INTO `sys_menu` VALUES 
(2021, '向量查询', 2020, 1, '', '', NULL, 1, 0, 'F', '0', '0', 'ai:vector:query', '#', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '');

-- 租户隔离测试菜单（开发环境可选）
INSERT INTO `sys_menu` VALUES 
(2030, '租户隔离测试', 2000, 9, 'tenant-test', 'ai/tenant-test/index', NULL, 1, 0, 'C', '0', '0', 'ai:tenant:test', 'shield', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '租户隔离测试菜单');

-- ----------------------------
-- 2. 角色权限配置（为管理员角色添加AI模块权限）
-- ----------------------------

-- 为超级管理员角色添加AI模块所有权限
INSERT INTO `sys_role_menu` SELECT 1, menu_id FROM `sys_menu` WHERE menu_id BETWEEN 2000 AND 2099;

-- ----------------------------
-- 3. 字典配置
-- ----------------------------

-- 知识库状态字典
INSERT INTO `sys_dict_type` VALUES (100, '000000', '知识库状态', 'ai_knowledge_status', '0', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', 'AI知识库状态列表');

INSERT INTO `sys_dict_data` VALUES (1000, '000000', 1, '正常', '0', 'ai_knowledge_status', '', 'success', 'N', '0', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '知识库正常状态');
INSERT INTO `sys_dict_data` VALUES (1001, '000000', 2, '停用', '1', 'ai_knowledge_status', '', 'danger', 'N', '0', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '知识库停用状态');

-- 知识库类型字典
INSERT INTO `sys_dict_type` VALUES (101, '000000', '知识库类型', 'ai_knowledge_type', '0', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', 'AI知识库类型列表');

INSERT INTO `sys_dict_data` VALUES (1010, '000000', 1, '公共', 'PUBLIC', 'ai_knowledge_type', '', 'primary', 'N', '0', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '公共知识库');
INSERT INTO `sys_dict_data` VALUES (1011, '000000', 2, '私有', 'PRIVATE', 'ai_knowledge_type', '', 'info', 'N', '0', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '私有知识库');

-- 文档处理状态字典
INSERT INTO `sys_dict_type` VALUES (102, '000000', '文档处理状态', 'ai_process_status', '0', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', 'AI文档处理状态列表');

INSERT INTO `sys_dict_data` VALUES (1020, '000000', 1, '待处理', 'PENDING', 'ai_process_status', '', 'info', 'N', '0', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '文档待处理状态');
INSERT INTO `sys_dict_data` VALUES (1021, '000000', 2, '处理中', 'PROCESSING', 'ai_process_status', '', 'warning', 'N', '0', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '文档处理中状态');
INSERT INTO `sys_dict_data` VALUES (1022, '000000', 3, '成功', 'SUCCESS', 'ai_process_status', '', 'success', 'N', '0', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '文档处理成功状态');
INSERT INTO `sys_dict_data` VALUES (1023, '000000', 4, '失败', 'FAILED', 'ai_process_status', '', 'danger', 'N', '0', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '文档处理失败状态');

-- 向量化状态字典
INSERT INTO `sys_dict_type` VALUES (103, '000000', '向量化状态', 'ai_vector_status', '0', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', 'AI向量化状态列表');

INSERT INTO `sys_dict_data` VALUES (1030, '000000', 1, '待向量化', 'PENDING', 'ai_vector_status', '', 'info', 'N', '0', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '待向量化状态');
INSERT INTO `sys_dict_data` VALUES (1031, '000000', 2, '向量化中', 'PROCESSING', 'ai_vector_status', '', 'warning', 'N', '0', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '向量化中状态');
INSERT INTO `sys_dict_data` VALUES (1032, '000000', 3, '成功', 'SUCCESS', 'ai_vector_status', '', 'success', 'N', '0', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '向量化成功状态');
INSERT INTO `sys_dict_data` VALUES (1033, '000000', 4, '失败', 'FAILED', 'ai_vector_status', '', 'danger', 'N', '0', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '向量化失败状态');

-- 支持的文件类型字典
INSERT INTO `sys_dict_type` VALUES (104, '000000', '支持的文件类型', 'ai_file_type', '0', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', 'AI支持的文件类型列表');

INSERT INTO `sys_dict_data` VALUES (1040, '000000', 1, 'PDF文档', 'pdf', 'ai_file_type', '', 'primary', 'N', '0', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', 'PDF文档类型');
INSERT INTO `sys_dict_data` VALUES (1041, '000000', 2, 'Word文档', 'docx', 'ai_file_type', '', 'primary', 'N', '0', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', 'Word文档类型');
INSERT INTO `sys_dict_data` VALUES (1042, '000000', 3, '旧版Word', 'doc', 'ai_file_type', '', 'primary', 'N', '0', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '旧版Word文档类型');
INSERT INTO `sys_dict_data` VALUES (1043, '000000', 4, '文本文档', 'txt', 'ai_file_type', '', 'info', 'N', '0', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '纯文本文档类型');
INSERT INTO `sys_dict_data` VALUES (1044, '000000', 5, 'Markdown', 'md', 'ai_file_type', '', 'info', 'N', '0', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', 'Markdown文档类型');

-- ----------------------------
-- 4. 配置参数
-- ----------------------------

-- AI模块配置参数
INSERT INTO `sys_config` VALUES (100, '000000', 'AI模块开关', 'ai.module.enabled', 'true', 'Y', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '是否启用AI模块功能');
INSERT INTO `sys_config` VALUES (101, '000000', '文档上传大小限制', 'ai.document.max.size', '500MB', 'Y', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '单个文档上传大小限制');
INSERT INTO `sys_config` VALUES (102, '000000', '向量检索默认数量', 'ai.vector.search.default.size', '10', 'Y', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '向量检索默认返回结果数量');
INSERT INTO `sys_config` VALUES (103, '000000', '文档处理超时时间', 'ai.document.process.timeout', '300', 'Y', 103, '2025-01-27 00:00:00', 103, '2025-01-27 00:00:00', '文档处理超时时间（秒）');

-- ----------------------------
-- 说明
-- ----------------------------

/*
菜单ID分配说明：
- 2000-2099: AI管理模块菜单ID范围
- 2000: AI管理主菜单
- 2001-2009: 知识库管理相关菜单
- 2010-2019: 文档管理相关菜单
- 2020-2029: 向量检索相关菜单
- 2030-2039: 测试和工具菜单

权限标识说明：
- ai:knowledgeBase:*: 知识库管理权限
- ai:document:*: 文档管理权限
- ai:vector:*: 向量操作权限
- ai:tenant:*: 租户测试权限

字典类型说明：
- ai_knowledge_status: 知识库状态
- ai_knowledge_type: 知识库类型
- ai_process_status: 文档处理状态
- ai_vector_status: 向量化状态
- ai_file_type: 支持的文件类型
*/
