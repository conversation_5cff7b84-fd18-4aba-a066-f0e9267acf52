# 知识库和文档管理API对接文档

## 目录
- [API概览](#api概览)
- [认证和权限](#认证和权限)
- [知识库管理API](#知识库管理api)
- [文档管理API](#文档管理api)
- [数据模型定义](#数据模型定义)
- [错误处理](#错误处理)
- [前端调用示例](#前端调用示例)

## API概览

### 知识库管理API
**功能描述：** 提供知识库的完整生命周期管理，包括创建、查询、更新、删除等操作，支持多租户数据隔离。

**基础URL：** `/ai/knowledge-bases`

**主要功能：**
- 知识库的CRUD操作
- 分页和条件查询
- 租户级数据隔离
- 知识库统计信息管理
- 名称唯一性验证

### 文档管理API
**功能描述：** 提供文档的上传、处理、管理功能，支持多种文件格式，集成向量化处理。

**基础URL：** `/ai/documents`

**主要功能：**
- 文档的CRUD操作
- 文件上传和批量上传
- 文档内容预览
- 文档重新处理
- 支持的文件类型查询

## 认证和权限

### 认证方式
- **认证类型：** Bearer Token (JWT)
- **请求头：** `Authorization: Bearer <token>`

### 权限控制
系统使用基于角色的权限控制（RBAC），每个接口都有对应的权限码：

| 权限码 | 说明 |
|--------|------|
| `ai:knowledgeBase:list` | 查询知识库列表 |
| `ai:knowledgeBase:query` | 查询知识库详情 |
| `ai:knowledgeBase:add` | 新增知识库 |
| `ai:knowledgeBase:edit` | 修改知识库 |
| `ai:knowledgeBase:remove` | 删除知识库 |
| `ai:document:list` | 查询文档列表 |
| `ai:document:query` | 查询文档详情 |
| `ai:document:add` | 新增文档 |
| `ai:document:edit` | 修改文档 |
| `ai:document:remove` | 删除文档 |
| `ai:document:upload` | 上传文档 |

## 知识库管理API

### 1. 查询知识库分页列表

**接口地址：** `GET /ai/knowledge-bases/list`

**功能描述：** 分页查询知识库列表，支持条件筛选

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 每页大小，默认10 |
| knowledgeName | String | 否 | 知识库名称（模糊查询） |
| status | String | 否 | 状态（0正常 1停用） |
| knowledgeType | String | 否 | 类型（PUBLIC公共 PRIVATE私有） |
| keyword | String | 否 | 关键字搜索（名称或描述） |
| orderByColumn | String | 否 | 排序字段 |
| isAsc | String | 否 | 排序方向（asc/desc） |

**请求示例：**
```http
GET /ai/knowledge-bases/list?pageNum=1&pageSize=10&knowledgeName=测试&status=0
Authorization: Bearer <token>
```

**响应格式：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "total": 100,
    "rows": [
      {
        "knowledgeId": 1,
        "knowledgeName": "产品知识库",
        "description": "产品相关文档知识库",
        "status": "0",
        "documentCount": 25,
        "vectorCount": 1250,
        "knowledgeType": "PRIVATE",
        "sort": 1,
        "remark": "备注信息",
        "tenantId": "000000",
        "createBy": 1,
        "createTime": "2024-01-01 10:00:00",
        "updateBy": 1,
        "updateTime": "2024-01-01 10:00:00"
      }
    ]
  }
}
```

### 2. 查询知识库列表（不分页）

**接口地址：** `GET /ai/knowledge-bases/all`

**功能描述：** 查询所有知识库列表，不分页

**请求参数：** 同分页查询（除分页参数外）

**响应格式：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "knowledgeId": 1,
      "knowledgeName": "产品知识库",
      // ... 其他字段
    }
  ]
}
```

### 3. 获取当前租户的知识库列表

**接口地址：** `GET /ai/knowledge-bases/current-tenant`

**功能描述：** 获取当前登录用户租户下的所有知识库

**请求参数：** 无

**响应格式：** 同查询知识库列表

### 4. 获取知识库详细信息

**接口地址：** `GET /ai/knowledge-bases/{knowledgeId}`

**功能描述：** 根据知识库ID获取详细信息

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| knowledgeId | Long | 是 | 知识库ID |

**响应格式：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "knowledgeId": 1,
    "knowledgeName": "产品知识库",
    "description": "产品相关文档知识库",
    "status": "0",
    "documentCount": 25,
    "vectorCount": 1250,
    "knowledgeType": "PRIVATE",
    "sort": 1,
    "remark": "备注信息",
    "tenantId": "000000",
    "createBy": 1,
    "createTime": "2024-01-01 10:00:00",
    "updateBy": 1,
    "updateTime": "2024-01-01 10:00:00"
  }
}
```

### 5. 新增知识库

**接口地址：** `POST /ai/knowledge-bases`

**功能描述：** 创建新的知识库

**请求头：**
```
Content-Type: application/json
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "knowledgeName": "新知识库",
  "description": "知识库描述",
  "status": "0",
  "knowledgeType": "PRIVATE",
  "sort": 1,
  "remark": "备注信息"
}
```

**字段验证规则：**
| 字段名 | 验证规则 |
|--------|----------|
| knowledgeName | 必填，长度1-100字符 |
| description | 可选，最大500字符 |
| status | 可选，0或1 |
| knowledgeType | 可选，PUBLIC或PRIVATE |

**响应格式：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

### 6. 修改知识库

**接口地址：** `PUT /ai/knowledge-bases`

**功能描述：** 更新知识库信息

**请求体：**
```json
{
  "knowledgeId": 1,
  "knowledgeName": "更新后的知识库名称",
  "description": "更新后的描述",
  "status": "0",
  "knowledgeType": "PRIVATE",
  "sort": 1,
  "remark": "更新后的备注"
}
```

**字段验证规则：**
| 字段名 | 验证规则 |
|--------|----------|
| knowledgeId | 必填 |
| knowledgeName | 必填，长度1-100字符 |

**响应格式：** 同新增知识库

### 7. 删除知识库

**接口地址：** `DELETE /ai/knowledge-bases/{knowledgeIds}`

**功能描述：** 删除一个或多个知识库

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| knowledgeIds | String | 是 | 知识库ID，多个用逗号分隔 |

**请求示例：**
```http
DELETE /ai/knowledge-bases/1,2,3
Authorization: Bearer <token>
```

**响应格式：** 同新增知识库

### 8. 检查知识库名称是否唯一

**接口地址：** `GET /ai/knowledge-bases/check-name`

**功能描述：** 验证知识库名称是否已存在

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| knowledgeName | String | 是 | 知识库名称 |
| knowledgeId | Long | 否 | 知识库ID（编辑时传入） |

**响应格式：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": true
}
```

### 9. 刷新知识库统计信息

**接口地址：** `PUT /ai/knowledge-bases/{knowledgeId}/refresh-statistics`

**功能描述：** 重新计算知识库的文档数量和向量数量

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| knowledgeId | Long | 是 | 知识库ID |

**响应格式：** 同新增知识库

## 文档管理API

### 1. 查询文档分页列表

**接口地址：** `GET /ai/documents/list`

**功能描述：** 分页查询文档列表，支持条件筛选

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 每页大小，默认10 |
| knowledgeId | Long | 否 | 知识库ID |
| documentName | String | 否 | 文档名称（模糊查询） |
| fileType | String | 否 | 文件类型 |
| processStatus | String | 否 | 处理状态（PENDING/PROCESSING/SUCCESS/FAILED） |
| vectorStatus | String | 否 | 向量化状态（PENDING/PROCESSING/SUCCESS/FAILED） |
| status | String | 否 | 文档状态（0正常 1停用） |
| keyword | String | 否 | 关键字搜索（文档名称或摘要） |
| orderByColumn | String | 否 | 排序字段 |
| isAsc | String | 否 | 排序方向（asc/desc） |

**请求示例：**
```http
GET /ai/documents/list?pageNum=1&pageSize=10&knowledgeId=1&processStatus=SUCCESS
Authorization: Bearer <token>
```

**响应格式：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "total": 50,
    "rows": [
      {
        "documentId": 1,
        "knowledgeId": 1,
        "knowledgeName": "产品知识库",
        "documentName": "产品介绍文档",
        "originalFileName": "product_intro.pdf",
        "fileType": "pdf",
        "fileSize": 1024000,
        "filePath": "/uploads/documents/2024/01/product_intro.pdf",
        "summary": "产品功能介绍和使用说明",
        "processStatus": "SUCCESS",
        "vectorStatus": "SUCCESS",
        "chunkCount": 10,
        "vectorCount": 10,
        "errorMessage": null,
        "status": "0",
        "sort": 1,
        "remark": "重要文档",
        "tenantId": "000000",
        "createBy": 1,
        "createTime": "2024-01-01 10:00:00",
        "updateBy": 1,
        "updateTime": "2024-01-01 10:00:00"
      }
    ]
  }
}
```

### 2. 查询文档列表（不分页）

**接口地址：** `GET /ai/documents/all`

**功能描述：** 查询所有文档列表，不分页

**请求参数：** 同分页查询（除分页参数外）

**响应格式：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "documentId": 1,
      "knowledgeId": 1,
      "documentName": "产品介绍文档",
      // ... 其他字段
    }
  ]
}
```

### 3. 根据知识库ID查询文档列表

**接口地址：** `GET /ai/documents/knowledge-base/{knowledgeId}`

**功能描述：** 获取指定知识库下的所有文档

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| knowledgeId | Long | 是 | 知识库ID |

**响应格式：** 同查询文档列表

### 4. 获取文档详细信息

**接口地址：** `GET /ai/documents/{documentId}`

**功能描述：** 根据文档ID获取详细信息

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| documentId | Long | 是 | 文档ID |

**响应格式：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "documentId": 1,
    "knowledgeId": 1,
    "knowledgeName": "产品知识库",
    "documentName": "产品介绍文档",
    "originalFileName": "product_intro.pdf",
    "fileType": "pdf",
    "fileSize": 1024000,
    "filePath": "/uploads/documents/2024/01/product_intro.pdf",
    "summary": "产品功能介绍和使用说明",
    "processStatus": "SUCCESS",
    "vectorStatus": "SUCCESS",
    "chunkCount": 10,
    "vectorCount": 10,
    "errorMessage": null,
    "status": "0",
    "sort": 1,
    "remark": "重要文档",
    "tenantId": "000000",
    "createBy": 1,
    "createTime": "2024-01-01 10:00:00",
    "updateBy": 1,
    "updateTime": "2024-01-01 10:00:00"
  }
}
```

### 5. 获取文档内容预览

**接口地址：** `GET /ai/documents/{documentId}/preview`

**功能描述：** 获取文档内容的文本预览

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| documentId | Long | 是 | 文档ID |

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| maxLength | Integer | 否 | 最大长度，默认1000 |

**响应格式：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": "这是文档的文本内容预览，包含前1000个字符..."
}
```

### 6. 新增文档

**接口地址：** `POST /ai/documents`

**功能描述：** 手动创建文档记录

**请求头：**
```
Content-Type: application/json
Authorization: Bearer <token>
```

**请求体：**
```json
{
  "knowledgeId": 1,
  "documentName": "新文档",
  "originalFileName": "document.pdf",
  "fileType": "pdf",
  "fileSize": 1024000,
  "filePath": "/uploads/documents/2024/01/document.pdf",
  "content": "文档的文本内容",
  "summary": "文档摘要",
  "status": "0",
  "sort": 1,
  "remark": "备注信息"
}
```

**字段验证规则：**
| 字段名 | 验证规则 |
|--------|----------|
| knowledgeId | 必填 |
| documentName | 必填，长度1-200字符 |
| summary | 可选，最大1000字符 |
| remark | 可选，最大500字符 |

**响应格式：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": null
}
```

### 7. 修改文档

**接口地址：** `PUT /ai/documents`

**功能描述：** 更新文档信息

**请求体：**
```json
{
  "documentId": 1,
  "knowledgeId": 1,
  "documentName": "更新后的文档名称",
  "summary": "更新后的摘要",
  "status": "0",
  "sort": 1,
  "remark": "更新后的备注"
}
```

**字段验证规则：**
| 字段名 | 验证规则 |
|--------|----------|
| documentId | 必填 |
| knowledgeId | 必填 |
| documentName | 必填，长度1-200字符 |

**响应格式：** 同新增文档

### 8. 删除文档

**接口地址：** `DELETE /ai/documents/{documentIds}`

**功能描述：** 删除一个或多个文档

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| documentIds | String | 是 | 文档ID，多个用逗号分隔 |

**请求示例：**
```http
DELETE /ai/documents/1,2,3
Authorization: Bearer <token>
```

**响应格式：** 同新增文档

### 9. 上传文档

**接口地址：** `POST /ai/documents/upload`

**功能描述：** 上传单个文档文件

**请求头：**
```
Content-Type: multipart/form-data
Authorization: Bearer <token>
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| knowledgeId | Long | 是 | 知识库ID |
| file | File | 是 | 上传的文件 |

**请求示例：**
```javascript
const formData = new FormData();
formData.append('knowledgeId', '1');
formData.append('file', fileInput.files[0]);

fetch('/ai/documents/upload', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer <token>'
  },
  body: formData
});
```

**响应格式：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "documentId": 1,
    "knowledgeId": 1,
    "documentName": "产品介绍文档",
    "originalFileName": "product_intro.pdf",
    "fileType": "pdf",
    "fileSize": 1024000,
    "processStatus": "PENDING",
    "vectorStatus": "PENDING",
    // ... 其他字段
  }
}
```

### 10. 批量上传文档

**接口地址：** `POST /ai/documents/batch-upload`

**功能描述：** 批量上传多个文档文件

**请求头：**
```
Content-Type: multipart/form-data
Authorization: Bearer <token>
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| knowledgeId | Long | 是 | 知识库ID |
| files | File[] | 是 | 上传的文件列表 |

**响应格式：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "documentId": 1,
      "documentName": "文档1",
      // ... 其他字段
    },
    {
      "documentId": 2,
      "documentName": "文档2",
      // ... 其他字段
    }
  ]
}
```

### 11. 重新处理文档

**接口地址：** `PUT /ai/documents/{documentId}/reprocess`

**功能描述：** 重新处理文档，重新解析和向量化

**路径参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| documentId | Long | 是 | 文档ID |

**响应格式：** 同新增文档

### 12. 获取支持的文件类型

**接口地址：** `GET /ai/documents/supported-file-types`

**功能描述：** 获取系统支持的文件类型列表

**请求参数：** 无

**响应格式：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": ["pdf", "doc", "docx", "txt", "md", "html"]
}
```

### 13. 检查文件类型是否支持

**接口地址：** `GET /ai/documents/check-file-type`

**功能描述：** 检查指定文件名的文件类型是否支持

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| fileName | String | 是 | 文件名 |

**请求示例：**
```http
GET /ai/documents/check-file-type?fileName=document.pdf
Authorization: Bearer <token>
```

**响应格式：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": true
}
```

## 数据模型定义

### KnowledgeBaseVO（知识库视图对象）

| 字段名 | 类型 | 说明 |
|--------|------|------|
| knowledgeId | Long | 知识库ID |
| knowledgeName | String | 知识库名称 |
| description | String | 知识库描述 |
| status | String | 状态（0正常 1停用） |
| documentCount | Integer | 文档数量 |
| vectorCount | Long | 向量数量 |
| knowledgeType | String | 类型（PUBLIC公共 PRIVATE私有） |
| sort | Integer | 排序 |
| remark | String | 备注 |
| tenantId | String | 租户ID |
| createBy | Long | 创建者 |
| createTime | Date | 创建时间 |
| updateBy | Long | 更新者 |
| updateTime | Date | 更新时间 |

### KnowledgeBaseBo（知识库业务对象）

| 字段名 | 类型 | 必填 | 验证规则 | 说明 |
|--------|------|------|----------|------|
| knowledgeId | Long | 编辑时必填 | - | 知识库ID |
| knowledgeName | String | 是 | 长度1-100字符 | 知识库名称 |
| description | String | 否 | 最大500字符 | 知识库描述 |
| status | String | 否 | 0或1 | 状态 |
| knowledgeType | String | 否 | PUBLIC或PRIVATE | 类型 |
| sort | Integer | 否 | - | 排序 |
| remark | String | 否 | - | 备注 |

### KnowledgeBaseQuery（知识库查询对象）

| 字段名 | 类型 | 说明 |
|--------|------|------|
| pageNum | Integer | 页码 |
| pageSize | Integer | 每页大小 |
| knowledgeName | String | 知识库名称（模糊查询） |
| status | String | 状态 |
| knowledgeType | String | 类型 |
| keyword | String | 关键字搜索 |
| orderByColumn | String | 排序字段 |
| isAsc | String | 排序方向 |

### DocumentVO（文档视图对象）

| 字段名 | 类型 | 说明 |
|--------|------|------|
| documentId | Long | 文档ID |
| knowledgeId | Long | 知识库ID |
| knowledgeName | String | 知识库名称 |
| documentName | String | 文档名称 |
| originalFileName | String | 原始文件名 |
| fileType | String | 文件类型 |
| fileSize | Long | 文件大小（字节） |
| filePath | String | 文件存储路径 |
| summary | String | 文档摘要 |
| processStatus | String | 处理状态（PENDING/PROCESSING/SUCCESS/FAILED） |
| vectorStatus | String | 向量化状态（PENDING/PROCESSING/SUCCESS/FAILED） |
| chunkCount | Integer | 分块数量 |
| vectorCount | Integer | 向量数量 |
| errorMessage | String | 错误信息 |
| status | String | 文档状态（0正常 1停用） |
| sort | Integer | 排序 |
| remark | String | 备注 |
| tenantId | String | 租户ID |
| createBy | Long | 创建者 |
| createTime | Date | 创建时间 |
| updateBy | Long | 更新者 |
| updateTime | Date | 更新时间 |

### DocumentBo（文档业务对象）

| 字段名 | 类型 | 必填 | 验证规则 | 说明 |
|--------|------|------|----------|------|
| documentId | Long | 编辑时必填 | - | 文档ID |
| knowledgeId | Long | 是 | - | 知识库ID |
| documentName | String | 是 | 长度1-200字符 | 文档名称 |
| originalFileName | String | 否 | - | 原始文件名 |
| fileType | String | 否 | - | 文件类型 |
| fileSize | Long | 否 | - | 文件大小 |
| filePath | String | 否 | - | 文件存储路径 |
| content | String | 否 | - | 文档内容 |
| summary | String | 否 | 最大1000字符 | 文档摘要 |
| status | String | 否 | 0或1 | 文档状态 |
| sort | Integer | 否 | - | 排序 |
| remark | String | 否 | 最大500字符 | 备注 |

### DocumentQuery（文档查询对象）

| 字段名 | 类型 | 说明 |
|--------|------|------|
| pageNum | Integer | 页码 |
| pageSize | Integer | 每页大小 |
| knowledgeId | Long | 知识库ID |
| documentName | String | 文档名称（模糊查询） |
| fileType | String | 文件类型 |
| processStatus | String | 处理状态 |
| vectorStatus | String | 向量化状态 |
| status | String | 文档状态 |
| keyword | String | 关键字搜索 |
| orderByColumn | String | 排序字段 |
| isAsc | String | 排序方向 |

### 统一响应格式

#### 成功响应
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": <响应数据>
}
```

#### 分页响应
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "total": 100,
    "rows": [<数据列表>]
  }
}
```

#### 失败响应
```json
{
  "code": 500,
  "msg": "错误信息",
  "data": null
}
```

### 枚举值定义

#### 知识库状态（status）
- `0`: 正常
- `1`: 停用

#### 知识库类型（knowledgeType）
- `PUBLIC`: 公共知识库
- `PRIVATE`: 私有知识库

#### 文档处理状态（processStatus）
- `PENDING`: 待处理
- `PROCESSING`: 处理中
- `SUCCESS`: 处理成功
- `FAILED`: 处理失败

#### 向量化状态（vectorStatus）
- `PENDING`: 待向量化
- `PROCESSING`: 向量化中
- `SUCCESS`: 向量化成功
- `FAILED`: 向量化失败

#### 支持的文件类型
- `pdf`: PDF文档
- `doc`: Word文档（旧版）
- `docx`: Word文档（新版）
- `txt`: 纯文本文件
- `md`: Markdown文件
- `html`: HTML文件

## 错误处理

### 统一错误响应格式

所有API的错误响应都遵循统一格式：

```json
{
  "code": <错误码>,
  "msg": "<错误信息>",
  "data": null
}
```

### 常见HTTP状态码

| 状态码 | 说明 | 示例场景 |
|--------|------|----------|
| 200 | 操作成功 | 正常请求处理成功 |
| 400 | 请求参数错误 | 参数验证失败、格式错误 |
| 401 | 未授权 | Token无效或过期 |
| 403 | 权限不足 | 没有访问权限 |
| 404 | 资源不存在 | 知识库或文档不存在 |
| 500 | 服务器内部错误 | 系统异常、数据库错误 |

### 常见错误码和错误信息

#### 认证和权限相关
```json
{
  "code": 401,
  "msg": "认证失败，无法访问系统资源",
  "data": null
}
```

```json
{
  "code": 403,
  "msg": "没有访问权限，请联系管理员授权",
  "data": null
}
```

#### 参数验证相关
```json
{
  "code": 400,
  "msg": "知识库名称不能为空",
  "data": null
}
```

```json
{
  "code": 400,
  "msg": "知识库名称长度不能超过100个字符",
  "data": null
}
```

#### 业务逻辑相关
```json
{
  "code": 500,
  "msg": "知识库不存在",
  "data": null
}
```

```json
{
  "code": 500,
  "msg": "数据库中已存在该记录，请联系管理员确认",
  "data": null
}
```

#### 文件上传相关
```json
{
  "code": 500,
  "msg": "上传文件大小超过限制，请选择较小的文件",
  "data": null
}
```

```json
{
  "code": 500,
  "msg": "不支持的文件类型",
  "data": null
}
```

## 前端调用示例

### JavaScript/TypeScript 示例

#### 1. 基础API调用封装

```javascript
// api.js - API调用基础封装
class ApiClient {
  constructor(baseURL = '', token = '') {
    this.baseURL = baseURL;
    this.token = token;
  }

  async request(url, options = {}) {
    const config = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`,
        ...options.headers
      },
      ...options
    };

    try {
      const response = await fetch(`${this.baseURL}${url}`, config);
      const result = await response.json();

      if (result.code === 200) {
        return result.data;
      } else {
        throw new Error(result.msg || '请求失败');
      }
    } catch (error) {
      console.error('API请求错误:', error);
      throw error;
    }
  }

  // GET请求
  async get(url, params = {}) {
    const queryString = new URLSearchParams(params).toString();
    const fullUrl = queryString ? `${url}?${queryString}` : url;
    return this.request(fullUrl, { method: 'GET' });
  }

  // POST请求
  async post(url, data = {}) {
    return this.request(url, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }

  // PUT请求
  async put(url, data = {}) {
    return this.request(url, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }

  // DELETE请求
  async delete(url) {
    return this.request(url, { method: 'DELETE' });
  }

  // 文件上传
  async upload(url, formData) {
    return this.request(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.token}`
        // 不设置Content-Type，让浏览器自动设置multipart/form-data
      },
      body: formData
    });
  }
}

// 创建API客户端实例
const apiClient = new ApiClient('/api', localStorage.getItem('token'));
```

#### 2. 知识库管理API调用

```javascript
// knowledgeBaseApi.js - 知识库API调用
class KnowledgeBaseApi {
  constructor(apiClient) {
    this.api = apiClient;
  }

  // 分页查询知识库列表
  async getKnowledgeBaseList(params = {}) {
    return this.api.get('/ai/knowledge-bases/list', params);
  }

  // 获取所有知识库
  async getAllKnowledgeBases(params = {}) {
    return this.api.get('/ai/knowledge-bases/all', params);
  }

  // 获取当前租户知识库
  async getCurrentTenantKnowledgeBases() {
    return this.api.get('/ai/knowledge-bases/current-tenant');
  }

  // 获取知识库详情
  async getKnowledgeBaseDetail(knowledgeId) {
    return this.api.get(`/ai/knowledge-bases/${knowledgeId}`);
  }

  // 创建知识库
  async createKnowledgeBase(data) {
    return this.api.post('/ai/knowledge-bases', data);
  }

  // 更新知识库
  async updateKnowledgeBase(data) {
    return this.api.put('/ai/knowledge-bases', data);
  }

  // 删除知识库
  async deleteKnowledgeBases(knowledgeIds) {
    const ids = Array.isArray(knowledgeIds) ? knowledgeIds.join(',') : knowledgeIds;
    return this.api.delete(`/ai/knowledge-bases/${ids}`);
  }

  // 检查知识库名称唯一性
  async checkKnowledgeNameUnique(knowledgeName, knowledgeId = null) {
    const params = { knowledgeName };
    if (knowledgeId) {
      params.knowledgeId = knowledgeId;
    }
    return this.api.get('/ai/knowledge-bases/check-name', params);
  }

  // 刷新知识库统计信息
  async refreshStatistics(knowledgeId) {
    return this.api.put(`/ai/knowledge-bases/${knowledgeId}/refresh-statistics`);
  }
}

// 使用示例
const knowledgeBaseApi = new KnowledgeBaseApi(apiClient);

// 获取知识库列表
async function loadKnowledgeBaseList() {
  try {
    const result = await knowledgeBaseApi.getKnowledgeBaseList({
      pageNum: 1,
      pageSize: 10,
      status: '0'
    });
    console.log('知识库列表:', result);
    return result;
  } catch (error) {
    console.error('获取知识库列表失败:', error);
    throw error;
  }
}

// 创建知识库
async function createNewKnowledgeBase(formData) {
  try {
    // 先检查名称唯一性
    const isUnique = await knowledgeBaseApi.checkKnowledgeNameUnique(formData.knowledgeName);
    if (!isUnique) {
      throw new Error('知识库名称已存在');
    }

    // 创建知识库
    await knowledgeBaseApi.createKnowledgeBase(formData);
    console.log('知识库创建成功');

    // 刷新列表
    return loadKnowledgeBaseList();
  } catch (error) {
    console.error('创建知识库失败:', error);
    throw error;
  }
}
```

#### 3. 文档管理API调用

```javascript
// documentApi.js - 文档API调用
class DocumentApi {
  constructor(apiClient) {
    this.api = apiClient;
  }

  // 分页查询文档列表
  async getDocumentList(params = {}) {
    return this.api.get('/ai/documents/list', params);
  }

  // 根据知识库ID查询文档
  async getDocumentsByKnowledgeId(knowledgeId) {
    return this.api.get(`/ai/documents/knowledge-base/${knowledgeId}`);
  }

  // 获取文档详情
  async getDocumentDetail(documentId) {
    return this.api.get(`/ai/documents/${documentId}`);
  }

  // 获取文档预览
  async getDocumentPreview(documentId, maxLength = 1000) {
    return this.api.get(`/ai/documents/${documentId}/preview`, { maxLength });
  }

  // 创建文档
  async createDocument(data) {
    return this.api.post('/ai/documents', data);
  }

  // 更新文档
  async updateDocument(data) {
    return this.api.put('/ai/documents', data);
  }

  // 删除文档
  async deleteDocuments(documentIds) {
    const ids = Array.isArray(documentIds) ? documentIds.join(',') : documentIds;
    return this.api.delete(`/ai/documents/${ids}`);
  }

  // 上传单个文档
  async uploadDocument(knowledgeId, file) {
    const formData = new FormData();
    formData.append('knowledgeId', knowledgeId);
    formData.append('file', file);
    return this.api.upload('/ai/documents/upload', formData);
  }

  // 批量上传文档
  async batchUploadDocuments(knowledgeId, files) {
    const formData = new FormData();
    formData.append('knowledgeId', knowledgeId);
    files.forEach(file => {
      formData.append('files', file);
    });
    return this.api.upload('/ai/documents/batch-upload', formData);
  }

  // 重新处理文档
  async reprocessDocument(documentId) {
    return this.api.put(`/ai/documents/${documentId}/reprocess`);
  }

  // 获取支持的文件类型
  async getSupportedFileTypes() {
    return this.api.get('/ai/documents/supported-file-types');
  }

  // 检查文件类型是否支持
  async checkFileTypeSupported(fileName) {
    return this.api.get('/ai/documents/check-file-type', { fileName });
  }
}

// 使用示例
const documentApi = new DocumentApi(apiClient);
```

#### 4. 典型业务场景示例

```javascript
// 业务场景1：完整的知识库创建流程
async function createKnowledgeBaseWorkflow() {
  try {
    // 1. 获取当前租户的知识库列表，检查是否已达到限制
    const existingKnowledgeBases = await knowledgeBaseApi.getCurrentTenantKnowledgeBases();
    console.log('当前知识库数量:', existingKnowledgeBases.length);

    // 2. 创建新知识库
    const newKnowledgeBase = {
      knowledgeName: '产品知识库',
      description: '存储产品相关文档的知识库',
      knowledgeType: 'PRIVATE',
      status: '0',
      sort: 1
    };

    // 3. 检查名称唯一性
    const isUnique = await knowledgeBaseApi.checkKnowledgeNameUnique(newKnowledgeBase.knowledgeName);
    if (!isUnique) {
      throw new Error('知识库名称已存在，请使用其他名称');
    }

    // 4. 创建知识库
    await knowledgeBaseApi.createKnowledgeBase(newKnowledgeBase);
    console.log('知识库创建成功');

    // 5. 获取创建后的知识库列表
    const updatedList = await knowledgeBaseApi.getCurrentTenantKnowledgeBases();
    const createdKnowledgeBase = updatedList.find(kb => kb.knowledgeName === newKnowledgeBase.knowledgeName);

    return createdKnowledgeBase;
  } catch (error) {
    console.error('创建知识库流程失败:', error);
    throw error;
  }
}

// 业务场景2：文档上传和处理监控
async function uploadDocumentWithMonitoring(knowledgeId, file) {
  try {
    // 1. 检查文件类型是否支持
    const isSupported = await documentApi.checkFileTypeSupported(file.name);
    if (!isSupported) {
      throw new Error('不支持的文件类型');
    }

    // 2. 上传文档
    console.log('开始上传文档:', file.name);
    const uploadedDocument = await documentApi.uploadDocument(knowledgeId, file);
    console.log('文档上传成功:', uploadedDocument);

    // 3. 监控处理状态
    const documentId = uploadedDocument.documentId;
    await monitorDocumentProcessing(documentId);

    return uploadedDocument;
  } catch (error) {
    console.error('文档上传失败:', error);
    throw error;
  }
}

// 文档处理状态监控
async function monitorDocumentProcessing(documentId, maxAttempts = 30, interval = 2000) {
  let attempts = 0;

  while (attempts < maxAttempts) {
    try {
      const document = await documentApi.getDocumentDetail(documentId);

      console.log(`文档处理状态: ${document.processStatus}, 向量化状态: ${document.vectorStatus}`);

      // 检查是否处理完成
      if (document.processStatus === 'SUCCESS' && document.vectorStatus === 'SUCCESS') {
        console.log('文档处理完成');
        return document;
      }

      // 检查是否处理失败
      if (document.processStatus === 'FAILED' || document.vectorStatus === 'FAILED') {
        throw new Error(`文档处理失败: ${document.errorMessage || '未知错误'}`);
      }

      // 等待下次检查
      await new Promise(resolve => setTimeout(resolve, interval));
      attempts++;
    } catch (error) {
      console.error('监控文档处理状态失败:', error);
      throw error;
    }
  }

  throw new Error('文档处理超时');
}

// 业务场景3：批量文档上传
async function batchUploadDocuments(knowledgeId, files) {
  try {
    // 1. 获取支持的文件类型
    const supportedTypes = await documentApi.getSupportedFileTypes();
    console.log('支持的文件类型:', supportedTypes);

    // 2. 过滤支持的文件
    const validFiles = [];
    const invalidFiles = [];

    for (const file of files) {
      const fileExtension = file.name.split('.').pop().toLowerCase();
      if (supportedTypes.includes(fileExtension)) {
        validFiles.push(file);
      } else {
        invalidFiles.push(file);
      }
    }

    if (invalidFiles.length > 0) {
      console.warn('以下文件类型不支持，将被跳过:', invalidFiles.map(f => f.name));
    }

    if (validFiles.length === 0) {
      throw new Error('没有支持的文件可以上传');
    }

    // 3. 批量上传
    console.log(`开始批量上传 ${validFiles.length} 个文件`);
    const uploadedDocuments = await documentApi.batchUploadDocuments(knowledgeId, validFiles);
    console.log('批量上传完成:', uploadedDocuments);

    // 4. 监控所有文档的处理状态
    const monitoringPromises = uploadedDocuments.map(doc =>
      monitorDocumentProcessing(doc.documentId)
    );

    const processedDocuments = await Promise.allSettled(monitoringPromises);

    // 5. 统计处理结果
    const successCount = processedDocuments.filter(p => p.status === 'fulfilled').length;
    const failureCount = processedDocuments.filter(p => p.status === 'rejected').length;

    console.log(`批量处理完成: 成功 ${successCount} 个，失败 ${failureCount} 个`);

    return {
      uploaded: uploadedDocuments,
      processed: processedDocuments,
      summary: { successCount, failureCount }
    };
  } catch (error) {
    console.error('批量上传失败:', error);
    throw error;
  }
}

// 业务场景4：知识库管理面板
class KnowledgeBaseManager {
  constructor() {
    this.knowledgeBaseApi = new KnowledgeBaseApi(apiClient);
    this.documentApi = new DocumentApi(apiClient);
  }

  // 获取知识库概览信息
  async getKnowledgeBaseOverview(knowledgeId) {
    try {
      // 并行获取知识库信息和文档列表
      const [knowledgeBase, documents] = await Promise.all([
        this.knowledgeBaseApi.getKnowledgeBaseDetail(knowledgeId),
        this.documentApi.getDocumentsByKnowledgeId(knowledgeId)
      ]);

      // 统计文档状态
      const statusStats = documents.reduce((stats, doc) => {
        stats[doc.processStatus] = (stats[doc.processStatus] || 0) + 1;
        return stats;
      }, {});

      return {
        knowledgeBase,
        documents,
        statistics: {
          totalDocuments: documents.length,
          statusStats,
          totalVectors: documents.reduce((sum, doc) => sum + (doc.vectorCount || 0), 0)
        }
      };
    } catch (error) {
      console.error('获取知识库概览失败:', error);
      throw error;
    }
  }

  // 清理失败的文档
  async cleanupFailedDocuments(knowledgeId) {
    try {
      const documents = await this.documentApi.getDocumentsByKnowledgeId(knowledgeId);
      const failedDocuments = documents.filter(doc =>
        doc.processStatus === 'FAILED' || doc.vectorStatus === 'FAILED'
      );

      if (failedDocuments.length === 0) {
        console.log('没有失败的文档需要清理');
        return;
      }

      console.log(`发现 ${failedDocuments.length} 个失败的文档，开始清理`);

      const failedDocumentIds = failedDocuments.map(doc => doc.documentId);
      await this.documentApi.deleteDocuments(failedDocumentIds);

      console.log('失败文档清理完成');
    } catch (error) {
      console.error('清理失败文档时出错:', error);
      throw error;
    }
  }
}

// 使用示例
const knowledgeBaseManager = new KnowledgeBaseManager();

// 获取知识库概览
async function loadKnowledgeBaseOverview(knowledgeId) {
  try {
    const overview = await knowledgeBaseManager.getKnowledgeBaseOverview(knowledgeId);
    console.log('知识库概览:', overview);
    return overview;
  } catch (error) {
    console.error('加载知识库概览失败:', error);
  }
}
```

### Vue.js 组件示例

```vue
<!-- KnowledgeBaseList.vue -->
<template>
  <div class="knowledge-base-list">
    <div class="header">
      <h2>知识库管理</h2>
      <button @click="showCreateDialog = true" class="btn-primary">
        新建知识库
      </button>
    </div>

    <!-- 搜索和筛选 -->
    <div class="filters">
      <input
        v-model="searchParams.knowledgeName"
        placeholder="搜索知识库名称"
        @input="handleSearch"
      />
      <select v-model="searchParams.status" @change="handleSearch">
        <option value="">全部状态</option>
        <option value="0">正常</option>
        <option value="1">停用</option>
      </select>
    </div>

    <!-- 知识库列表 -->
    <div class="knowledge-base-grid">
      <div
        v-for="kb in knowledgeBases"
        :key="kb.knowledgeId"
        class="knowledge-base-card"
        @click="viewKnowledgeBase(kb.knowledgeId)"
      >
        <h3>{{ kb.knowledgeName }}</h3>
        <p>{{ kb.description }}</p>
        <div class="stats">
          <span>文档: {{ kb.documentCount }}</span>
          <span>向量: {{ kb.vectorCount }}</span>
        </div>
        <div class="actions">
          <button @click.stop="editKnowledgeBase(kb)">编辑</button>
          <button @click.stop="deleteKnowledgeBase(kb.knowledgeId)">删除</button>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination">
      <button
        :disabled="currentPage === 1"
        @click="changePage(currentPage - 1)"
      >
        上一页
      </button>
      <span>第 {{ currentPage }} 页，共 {{ totalPages }} 页</span>
      <button
        :disabled="currentPage === totalPages"
        @click="changePage(currentPage + 1)"
      >
        下一页
      </button>
    </div>

    <!-- 创建/编辑对话框 -->
    <div v-if="showCreateDialog" class="dialog-overlay">
      <div class="dialog">
        <h3>{{ editingKnowledgeBase ? '编辑知识库' : '新建知识库' }}</h3>
        <form @submit.prevent="saveKnowledgeBase">
          <div class="form-group">
            <label>知识库名称*</label>
            <input
              v-model="formData.knowledgeName"
              required
              maxlength="100"
            />
          </div>
          <div class="form-group">
            <label>描述</label>
            <textarea
              v-model="formData.description"
              maxlength="500"
            ></textarea>
          </div>
          <div class="form-group">
            <label>类型</label>
            <select v-model="formData.knowledgeType">
              <option value="PRIVATE">私有</option>
              <option value="PUBLIC">公共</option>
            </select>
          </div>
          <div class="form-actions">
            <button type="button" @click="closeDialog">取消</button>
            <button type="submit" :disabled="saving">
              {{ saving ? '保存中...' : '保存' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { KnowledgeBaseApi } from '@/api/knowledgeBaseApi';

export default {
  name: 'KnowledgeBaseList',
  data() {
    return {
      knowledgeBases: [],
      searchParams: {
        knowledgeName: '',
        status: '',
        pageNum: 1,
        pageSize: 12
      },
      currentPage: 1,
      totalPages: 1,
      total: 0,
      showCreateDialog: false,
      editingKnowledgeBase: null,
      formData: {
        knowledgeName: '',
        description: '',
        knowledgeType: 'PRIVATE',
        status: '0'
      },
      saving: false,
      loading: false
    };
  },
  async mounted() {
    await this.loadKnowledgeBases();
  },
  methods: {
    async loadKnowledgeBases() {
      try {
        this.loading = true;
        const result = await KnowledgeBaseApi.getKnowledgeBaseList(this.searchParams);
        this.knowledgeBases = result.rows;
        this.total = result.total;
        this.totalPages = Math.ceil(result.total / this.searchParams.pageSize);
      } catch (error) {
        this.$message.error('加载知识库列表失败: ' + error.message);
      } finally {
        this.loading = false;
      }
    },

    async handleSearch() {
      this.searchParams.pageNum = 1;
      this.currentPage = 1;
      await this.loadKnowledgeBases();
    },

    async changePage(page) {
      this.searchParams.pageNum = page;
      this.currentPage = page;
      await this.loadKnowledgeBases();
    },

    viewKnowledgeBase(knowledgeId) {
      this.$router.push(`/knowledge-base/${knowledgeId}`);
    },

    editKnowledgeBase(knowledgeBase) {
      this.editingKnowledgeBase = knowledgeBase;
      this.formData = { ...knowledgeBase };
      this.showCreateDialog = true;
    },

    async saveKnowledgeBase() {
      try {
        this.saving = true;

        if (this.editingKnowledgeBase) {
          await KnowledgeBaseApi.updateKnowledgeBase(this.formData);
          this.$message.success('知识库更新成功');
        } else {
          // 检查名称唯一性
          const isUnique = await KnowledgeBaseApi.checkKnowledgeNameUnique(
            this.formData.knowledgeName
          );
          if (!isUnique) {
            this.$message.error('知识库名称已存在');
            return;
          }

          await KnowledgeBaseApi.createKnowledgeBase(this.formData);
          this.$message.success('知识库创建成功');
        }

        this.closeDialog();
        await this.loadKnowledgeBases();
      } catch (error) {
        this.$message.error('保存失败: ' + error.message);
      } finally {
        this.saving = false;
      }
    },

    async deleteKnowledgeBase(knowledgeId) {
      if (!confirm('确定要删除这个知识库吗？此操作不可恢复。')) {
        return;
      }

      try {
        await KnowledgeBaseApi.deleteKnowledgeBases([knowledgeId]);
        this.$message.success('删除成功');
        await this.loadKnowledgeBases();
      } catch (error) {
        this.$message.error('删除失败: ' + error.message);
      }
    },

    closeDialog() {
      this.showCreateDialog = false;
      this.editingKnowledgeBase = null;
      this.formData = {
        knowledgeName: '',
        description: '',
        knowledgeType: 'PRIVATE',
        status: '0'
      };
    }
  }
};
</script>
```

### React Hook 示例

```jsx
// useKnowledgeBase.js - 知识库管理 Hook
import { useState, useEffect, useCallback } from 'react';
import { KnowledgeBaseApi } from '../api/knowledgeBaseApi';

export const useKnowledgeBase = () => {
  const [knowledgeBases, setKnowledgeBases] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  const loadKnowledgeBases = useCallback(async (params = {}) => {
    try {
      setLoading(true);
      setError(null);
      const result = await KnowledgeBaseApi.getKnowledgeBaseList(params);
      setKnowledgeBases(result.rows || result);
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  const createKnowledgeBase = useCallback(async (data) => {
    try {
      setLoading(true);
      await KnowledgeBaseApi.createKnowledgeBase(data);
      await loadKnowledgeBases(); // 重新加载列表
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [loadKnowledgeBases]);

  const updateKnowledgeBase = useCallback(async (data) => {
    try {
      setLoading(true);
      await KnowledgeBaseApi.updateKnowledgeBase(data);
      await loadKnowledgeBases(); // 重新加载列表
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [loadKnowledgeBases]);

  const deleteKnowledgeBase = useCallback(async (knowledgeIds) => {
    try {
      setLoading(true);
      await KnowledgeBaseApi.deleteKnowledgeBases(knowledgeIds);
      await loadKnowledgeBases(); // 重新加载列表
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, [loadKnowledgeBases]);

  return {
    knowledgeBases,
    loading,
    error,
    loadKnowledgeBases,
    createKnowledgeBase,
    updateKnowledgeBase,
    deleteKnowledgeBase
  };
};

// KnowledgeBaseList.jsx - React 组件示例
import React, { useState, useEffect } from 'react';
import { useKnowledgeBase } from '../hooks/useKnowledgeBase';

const KnowledgeBaseList = () => {
  const {
    knowledgeBases,
    loading,
    error,
    loadKnowledgeBases,
    createKnowledgeBase,
    deleteKnowledgeBase
  } = useKnowledgeBase();

  const [searchParams, setSearchParams] = useState({
    knowledgeName: '',
    status: '',
    pageNum: 1,
    pageSize: 10
  });

  useEffect(() => {
    loadKnowledgeBases(searchParams);
  }, [loadKnowledgeBases, searchParams]);

  const handleSearch = (newParams) => {
    setSearchParams(prev => ({
      ...prev,
      ...newParams,
      pageNum: 1 // 重置到第一页
    }));
  };

  const handleCreate = async (formData) => {
    try {
      await createKnowledgeBase(formData);
      alert('知识库创建成功');
    } catch (error) {
      alert('创建失败: ' + error.message);
    }
  };

  const handleDelete = async (knowledgeId) => {
    if (window.confirm('确定要删除这个知识库吗？')) {
      try {
        await deleteKnowledgeBase([knowledgeId]);
        alert('删除成功');
      } catch (error) {
        alert('删除失败: ' + error.message);
      }
    }
  };

  if (loading) return <div>加载中...</div>;
  if (error) return <div>错误: {error}</div>;

  return (
    <div className="knowledge-base-list">
      <h2>知识库管理</h2>

      {/* 搜索区域 */}
      <div className="search-area">
        <input
          type="text"
          placeholder="搜索知识库名称"
          value={searchParams.knowledgeName}
          onChange={(e) => handleSearch({ knowledgeName: e.target.value })}
        />
        <select
          value={searchParams.status}
          onChange={(e) => handleSearch({ status: e.target.value })}
        >
          <option value="">全部状态</option>
          <option value="0">正常</option>
          <option value="1">停用</option>
        </select>
      </div>

      {/* 知识库列表 */}
      <div className="knowledge-base-grid">
        {knowledgeBases.map(kb => (
          <div key={kb.knowledgeId} className="knowledge-base-card">
            <h3>{kb.knowledgeName}</h3>
            <p>{kb.description}</p>
            <div className="stats">
              <span>文档: {kb.documentCount}</span>
              <span>向量: {kb.vectorCount}</span>
            </div>
            <div className="actions">
              <button onClick={() => handleDelete(kb.knowledgeId)}>
                删除
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default KnowledgeBaseList;
```

## 总结

本文档提供了KnowledgeBaseController和DocumentController的完整API对接指南，包括：

1. **完整的接口规范**：涵盖所有API端点的详细说明
2. **数据模型定义**：清晰的字段说明和验证规则
3. **错误处理机制**：统一的错误响应格式和常见错误码
4. **实用的代码示例**：JavaScript/TypeScript、Vue.js、React等多种前端技术栈的示例
5. **业务场景演示**：典型的知识库和文档管理流程

前端开发人员可以根据此文档快速理解API接口，并参考示例代码进行集成开发。建议在实际开发中根据具体需求调整和扩展这些示例代码。
```
```

