package org.dromara.web.domain.vo;

import org.dromara.system.domain.vo.SysTenantVo;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

/**
 * 租户列表
 *
 * <AUTHOR> <PERSON>py
 */
@Data
@AutoMapper(target = SysTenantVo.class)
public class TenantListVo {

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 域名
     */
    private String domain;

}
