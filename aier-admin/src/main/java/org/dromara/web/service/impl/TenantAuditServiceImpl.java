package org.dromara.web.service.impl;

import cn.dev33.satoken.secure.BCrypt;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.Employee;
import org.dromara.biz.domain.MerchantApplication;
import org.dromara.biz.domain.TaskNode;
import org.dromara.biz.domain.WechatUser;
import org.dromara.biz.domain.bo.merchant.MerchantAuditBO;
import org.dromara.biz.service.IBizEmployeeService;
import org.dromara.biz.service.MerchantApplicationService;
import org.dromara.biz.service.TaskNodeService;
import org.dromara.biz.service.WechatUserService;
import org.dromara.common.core.enums.AppUserRoleEnum;
import org.dromara.common.core.enums.AuditStatus;
import org.dromara.common.core.enums.DynamicEnum;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.tenant.helper.TenantHelper;
import org.dromara.system.domain.SysTenant;
import org.dromara.system.domain.bo.SysTenantBo;
import org.dromara.system.service.ISysTenantService;
import org.dromara.web.service.TenantAuditService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class TenantAuditServiceImpl implements TenantAuditService {

    private final MerchantApplicationService merchantApplicationService;
    private final ISysTenantService tenantService;
    private final WechatUserService wechatUserService;
    private final IBizEmployeeService employeeService;
    private final TaskNodeService taskNodeService;

    @Override
    @RepeatSubmit
    @Transactional(rollbackFor = Exception.class)
    public Boolean audit(MerchantAuditBO merchantAuditBO) {
        return TenantHelper.ignore(()->{
            MerchantApplication merchantApplication = merchantApplicationService.getById(merchantAuditBO.getId());
            //判断手机号重复不让重复入驻
            LambdaQueryWrapper<MerchantApplication> lqw = Wrappers.lambdaQuery();
            lqw.eq(MerchantApplication::getContactPhone,merchantApplication.getContactPhone());
            lqw.eq(MerchantApplication::getStatus,1);
            Long count = merchantApplicationService.count(lqw);
            if(count > 0L){
                throw new ServiceException("当前手机号已生成租户，请更换手机号或者联系管理员。");
            }
            SysTenantBo bo = new SysTenantBo();
            bo.setContactPhone(merchantApplication.getContactPhone());
            bo.setAddress(merchantApplication.getAddress());
            bo.setPassword(merchantApplication.getPassword());
            bo.setContactUserName(merchantApplication.getContactName());
            bo.setCompanyName(merchantApplication.getMerchantName());
            bo.setUsername(merchantApplication.getContactPhone());
            bo.setPackageId(1836445580986400769L);
            SysTenant tenant = tenantService.createTenant(bo);
            bo.setTenantId(tenant.getTenantId());
            //新增员工  管理员
            WechatUser wechatUser = new WechatUser();
            wechatUser.setNickname(merchantApplication.getContactName());
            wechatUser.setTel(merchantApplication.getContactPhone());
            wechatUser.setStatus("0");
            wechatUser.setUtype("1");
            wechatUser.setTenantId(tenant.getTenantId());
            wechatUserService.save(wechatUser);
            Employee employee =  new Employee();
            employee.setPhone(merchantApplication.getContactPhone());
            employee.setName(merchantApplication.getContactName());
            employee.setRoleCode("BOSS");
            employee.setTenantId(tenant.getTenantId());
            employee.setDescription("审核通过系统内置账号");
            employee.setUserId(wechatUser.getUserId());
            employee.setDisable(false);
            employee.setAuditStatus(AuditStatus.APPROVED.getValue());
            employee.setPassword(BCrypt.hashpw(merchantApplication.getPassword()));
            employeeService.save(employee);

            //审核通过 新增默认标签
            List<TaskNode> taskNodeList = new ArrayList<>();
            //护理
            TaskNode taskNode  = new TaskNode();
            taskNode.setNodeName("默认标签");
            taskNode.setNurseType(DynamicEnum.HLFW.name());
            taskNode.setNurseRole(AppUserRoleEnum.NURSE.name());
            taskNode.setTenantId(tenant.getTenantId());
            taskNodeList.add(taskNode);
            //产康
            taskNode  = new TaskNode();
            taskNode.setNodeName("默认标签");
            taskNode.setNurseType(DynamicEnum.CHKF.name());
            taskNode.setNurseRole(AppUserRoleEnum.POSTPARTUM.name());
            taskNode.setTenantId(tenant.getTenantId());
            taskNodeList.add(taskNode);
            //厨师
            taskNode  = new TaskNode();
            taskNode.setNodeName("默认标签");
            taskNode.setNurseType(DynamicEnum.YZSS.name());
            taskNode.setNurseRole(AppUserRoleEnum.CHEF.name());
            taskNode.setTenantId(tenant.getTenantId());
            taskNodeList.add(taskNode);
            taskNodeService.saveBatch(taskNodeList);

            LambdaUpdateWrapper<MerchantApplication> luw = Wrappers.lambdaUpdate();
            luw.eq(MerchantApplication::getId, merchantAuditBO.getId());
            luw.set(MerchantApplication::getStatus, merchantAuditBO.getStatus());
            luw.set(MerchantApplication::getTenantId, tenant.getTenantId());
            return merchantApplicationService.update(luw);
        });
    }
}
