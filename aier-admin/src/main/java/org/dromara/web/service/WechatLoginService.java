package org.dromara.web.service;

import cn.hutool.core.collection.CollUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.WechatRole;
import org.dromara.biz.domain.WechatUser;
import org.dromara.biz.domain.vo.WechatUserVO;
import org.dromara.biz.service.WechatPermissionService;
import org.dromara.biz.service.WechatUserService;
import org.dromara.common.core.domain.dto.RoleDTO;
import org.dromara.common.core.domain.model.WechatLoginUser;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.ServletUtils;
import org.dromara.common.mybatis.helper.DataPermissionHelper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * 微信小程序登录校验方法
 */
@RequiredArgsConstructor
@Slf4j
@Service
public class WechatLoginService {

    private final WechatPermissionService wechatPermissionService;
    private final WechatUserService wechatUserService;

    /**
     * 构建微信小程序登录用户
     */
    public WechatLoginUser buildLoginUser(WechatUserVO user) {
        Long userId = user.getUserId();
        Set<String> menuPermission = wechatPermissionService.getMenuPermission(userId);
        Set<String> rolePermission = wechatPermissionService.getRolePermission(userId);
        List<WechatRole> roleList = user.getRoles();
        List<RoleDTO> roles = null;
        if(CollUtil.isNotEmpty(roleList)){
             roles = roleList.stream().map(v -> {
                RoleDTO role = new RoleDTO();
                role.setRoleName(v.getName());
                role.setRoleKey(v.getCode());
                role.setRoleId(v.getId());
                return role;
            }).toList();
        }

        WechatLoginUser loginUser = new WechatLoginUser();
        loginUser.setTenantId(user.getTenantId());
        loginUser.setUserId(user.getUserId());
        loginUser.setNickname(user.getNickname());
        loginUser.setUserType("mini_app");
        loginUser.setOpenid(user.getOpenid());
        loginUser.setUnionid(user.getUnionid());
        loginUser.setSessionKey(null);
        loginUser.setRolePermission(rolePermission);
        loginUser.setMenuPermission(menuPermission);
        loginUser.setRoles(roles);
        return loginUser;
    }

    /**
     * 记录微信用户登录信息
     *
     * @param userId 用户ID
     */
    public void recordLoginInfo(Long userId) {
        String ip = ServletUtils.getClientIP();
        WechatUser wechatUser = new WechatUser();
        wechatUser.setUserId(userId);
        wechatUser.setLastLoginIp(ip);
        wechatUser.setLastLoginTime(DateUtils.getNowDate());
        DataPermissionHelper.ignore(() -> wechatUserService.updateById(wechatUser));
    }
}
