package org.dromara;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 启动程序
 *
 * <AUTHOR> <PERSON>py
 */

@SpringBootApplication
@EnableScheduling
public class AierMerchantApplication {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(AierMerchantApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  艾尔母婴商家管理平台启动成功   ლ(´ڡ`ლ)ﾞ");
    }

}
