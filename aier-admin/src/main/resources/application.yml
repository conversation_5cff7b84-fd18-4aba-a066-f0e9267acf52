# 项目相关配置
ruoyi:
  # 名称
  name: AIER-MERCHANT-PRO
  # 版本
  version: ${revision}
  # 版权年份
  copyrightYear: 2024

captcha:
  enable: false
  # 页面 <参数设置> 可开启关闭 验证码校验
  # 验证码类型 math 数组计算 char 字符验证
  type: MATH
  # line 线段干扰 circle 圆圈干扰 shear 扭曲干扰
  category: CIRCLE
  # 数字验证码位数
  numberLength: 1
  # 字符验证码长度
  charLength: 4

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  # undertow 配置
  undertow:
    # HTTP post内容的最大大小。当值为-1时，默认值为大小是无限的
    max-http-post-size: -1
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    # 每块buffer的空间大小,越小的空间被利用越充分
    buffer-size: 512
    # 是否分配的直接内存
    direct-buffers: true
    threads:
      # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
      io: 8
      # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
      worker: 256

# 日志配置
logging:
  level:
    org.dromara: @logging.level@
    org.springframework: warn
    org.mybatis.spring.mapper: error
  config: classpath:logback-plus.xml

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  application:
    name: ${ruoyi.name}
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: @profiles.active@
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 500MB
      # 设置总上传的文件大小
      max-request-size: 1000MB
  mvc:
    # 设置静态资源路径 防止所有请求都去查静态资源
    static-path-pattern: /static/**
    format:
      date-time: yyyy-MM-dd HH:mm:ss
  jackson:
    # 日期格式化
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      # 格式化输出
      indent_output: false
      # 忽略无法转换的对象
      fail_on_empty_beans: false
    deserialization:
      # 允许对象忽略json中不存在的属性
      fail_on_unknown_properties: false
  ai:
    dashscope:
      api-key: sk-8c96335fe74c4b4aae92bd84f0ef8cab
#      base-url: https://dashscope.aliyuncs.com/compatible-mode/v1/
      chat:
        options:
          model: qwen-max

    # Milvus 向量存储配置
    vectorstore:
      milvus:
        client:
          host: ${MILVUS_HOST:**************}
          port: ${MILVUS_PORT:19530}
          username: ${MILVUS_USERNAME:root}
          password: ${MILVUS_PASSWORD:milvus}
        databaseStrategy:
          type: TENANT_ID # 可选值：FIXED/TENANT_ID
          default-name: system_core # 系统级集合存储库
        databaseName: ${MILVUS_DATABASE_NAME:default}
        collectionName: ${MILVUS_COLLECTION_NAME:vector_store}
        embeddingDimension: 1536
        indexType: IVF_FLAT
        metricType: COSINE
        initialize-schema: true  # 启用自动schema初始化

    # 智能文档处理配置
    document-processing:
      # 智能分割器配置
      smart-splitter:
        target-chunk-size: 600          # 目标块大小（字符数）
        min-chunk-size: 100             # 最小块大小（字符数）
        max-chunk-size: 1200            # 最大块大小（字符数）
        overlap-ratio: 0.15             # 重叠比例（15%）
        min-overlap-size: 20            # 最小重叠大小（字符数）
        enable-semantic-splitting: true # 启用语义感知分割
        enable-structural-splitting: true # 启用结构化分割

      # 文档清洗配置
      cleaning:
        enabled: true                   # 启用文档清洗
        remove-header-footer: true      # 去除页眉页脚
        remove-duplicates: true         # 去除重复内容
        normalize-format: true          # 格式规范化
        remove-meaningless-sequences: true # 去除无意义字符序列
        min-quality-score: 30.0         # 最小内容质量分数阈值
        min-valid-content-length: 5     # 最小有效内容长度

      # 元数据增强配置
      metadata:
        enabled: true                   # 启用元数据增强
        extract-keywords: true          # 提取关键词
        max-keywords: 10                # 关键词提取数量限制
        generate-summary: true          # 生成摘要
        max-summary-length: 100         # 摘要最大长度
        detect-language: true           # 语言检测
        analyze-content-type: true      # 分析内容类型
        calculate-quality-metrics: true # 计算质量指标

      # 质量控制配置
      quality:
        enabled: true                   # 启用质量控制
        min-readability-score: 30.0     # 最小可读性分数
        min-information-density: 20.0   # 最小信息密度
        min-completeness-score: 30.0    # 最小完整性分数
        min-overall-quality-score: 40.0 # 最小综合质量分数
        auto-filter-low-quality: true   # 自动过滤低质量内容

      # 文档类型特殊配置
      document-types:
        pdf:
          custom-chunk-size: 800        # PDF特殊块大小
          enable-special-cleaning: true # 启用PDF特殊清洗
          splitting-strategy: "page-aware" # 页面感知分割策略
        docx:
          custom-chunk-size: 700        # Word特殊块大小
          enable-special-cleaning: true # 启用Word特殊清洗
          splitting-strategy: "paragraph-aware" # 段落感知分割策略
        md:
          custom-chunk-size: 600        # Markdown特殊块大小
          splitting-strategy: "heading-aware" # 标题感知分割策略
        txt:
          custom-chunk-size: 500        # 文本特殊块大小
          splitting-strategy: "sentence-aware" # 句子感知分割策略

# Sa-Token配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: Authorization
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: false
  # jwt秘钥
  jwt-secret-key: abcdefghijklmnopqrstuvwxyz
  is-print: false

# security配置
security:
  # 排除路径
  excludes:
    # 静态资源
    - /*.html
    - /**/*.html
    - /**/*.css
    - /**/*.js
    # 公共路径
    - /favicon.ico
    - /error
    # swagger 文档配置
    - /*/api-docs
    - /*/api-docs/**
    # actuator 监控配置
    - /actuator
    - /actuator/**

# 多租户配置
tenant:
  # 是否开启
  enable: true
  # 排除表
  excludes:
    - sys_menu
    - sys_tenant
    - sys_tenant_package
    - sys_role_dept
    - sys_role_menu
    - sys_user_post
    - sys_user_role
    - sys_client
    - sys_oss_config
    - biz_topics
    - biz_audits
    - biz_pregnancy_tracker
    - biz_questions
    - biz_wechat_mini_program
    - biz_tenant_mini_program
    - sys_dict_type
    - sys_dict_data
    - biz_default_customers
    - biz_default_staffs
    - biz_default_customers_staff
    - biz_default_feed_posts
    - biz_default_nodes
    - biz_customer_tag
    - biz_customer_tag_consultation
    - biz_customer_tag_expert
    - biz_customer_tag_professional
    - biz_customer_tag_solution
    - biz_merchant_application
    - biz_wechat_role
    - biz_wechat_inviation
    - biz_wechat_perms
    - biz_wechat_role_perms
    - biz_invitation_template
    - biz_user_guide_record
    - biz_template_style
    - l_opus

# MyBatisPlus配置
# https://baomidou.com/config/
mybatis-plus:
  # 不支持多包, 如有需要可在注解配置 或 提升扫包等级
  # 例如 com.**.**.mapper
  mapperPackage: org.dromara.**.mapper
  # 对应的 XML 文件位置
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: org.dromara.**.domain
  type-handlers-package: org.dromara.common.mybatis.handler.type
  global-config:
    banner: false
    dbConfig:
      # 主键类型
      # AUTO 自增 NONE 空 INPUT 用户输入 ASSIGN_ID 雪花 ASSIGN_UUID 唯一 UUID
      # 如需改为自增 需要将数据库表全部设置为自增
      idType: ASSIGN_ID

# 数据加密
mybatis-encryptor:
  # 是否开启加密
  enable: false
  # 默认加密算法
  algorithm: BASE64
  # 编码方式 BASE64/HEX。默认BASE64
  encode: BASE64
  # 安全秘钥 对称算法的秘钥 如：AES，SM4
  password:
  # 公私钥 非对称算法的公私钥 如：SM2，RSA
  publicKey:
  privateKey:

# api接口加密
api-decrypt:
  # 是否开启全局接口加密
  enabled: true
  # AES 加密头标识
  headerFlag: encrypt-key
  # 响应加密公钥 非对称算法的公私钥 如：SM2，RSA 使用者请自行更换
  # 对应前端解密私钥 MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAmc3CuPiGL/LcIIm7zryCEIbl1SPzBkr75E2VMtxegyZ1lYRD+7TZGAPkvIsBcaMs6Nsy0L78n2qh+lIZMpLH8wIDAQABAkEAk82Mhz0tlv6IVCyIcw/s3f0E+WLmtPFyR9/WtV3Y5aaejUkU60JpX4m5xNR2VaqOLTZAYjW8Wy0aXr3zYIhhQQIhAMfqR9oFdYw1J9SsNc+CrhugAvKTi0+BF6VoL6psWhvbAiEAxPPNTmrkmrXwdm/pQQu3UOQmc2vCZ5tiKpW10CgJi8kCIFGkL6utxw93Ncj4exE/gPLvKcT+1Emnoox+O9kRXss5AiAMtYLJDaLEzPrAWcZeeSgSIzbL+ecokmFKSDDcRske6QIgSMkHedwND1olF8vlKsJUGK3BcdtM8w4Xq7BpSBwsloE=
  publicKey: MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAJsk8N3SAF3mSV/gwKwD3AZUJ0+2//qvI2zXKSZYPT3DplXGKZF83+wuPwV9+P9OtErcTHhk4aKhNwv6PgrxyxECAwEAAQ==
  # 请求解密私钥 非对称算法的公私钥 如：SM2，RSA 使用者请自行更换
  # 对应前端加密公钥 MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==
  privateKey: MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEA3Z0uvqNfSMojO6x+Becfb3W0Ek3CHGfaLMGsQybCIFPnxq/9YommLVSqSvCWKQac6PzqgY8qi327LRh8wO9ptwIDAQABAkBjiORk1UXHkJpQhAG6LvtseUYebnQ/g9l2/qmbI35PO+omemxtFcvIojbIDGJtm6cHEYsF5K7epVsIQfyYNOmJAiEA9szwNorSaOBkJWjbsuOPoJ1836sTF8gAlAvjsd2FF+0CIQDl3+er2dyuhlT3oXpV7PiGE8gJT+G6Nx2BLuL63CqLswIgKTG96qyzQDQsIx5khXB8WcfPvxmz/yUwC4YMhHTAMWUCIQDav6h5i7eKSQDAKWT+MaQKCvb3jZGzn9Eu5oxOxoaiKwIgGgqSqqMum8xqOwVWxvgmEVBRaCzC/CSZt6GmEVNaxw0=

springdoc:
  api-docs:
    # 是否开启接口文档
    enabled: true
#  swagger-ui:
#    # 持久化认证数据
#    persistAuthorization: true
  info:
    # 标题
    title: '标题：${ruoyi.name}多租户管理系统_接口文档'
    # 描述
    description: '描述：用于管理集团旗下公司的人员信息,具体包括XXX,XXX模块...'
    # 版本
    version: '版本号: ${ruoyi.version}'
    # 作者信息
    contact:
      name: DummyPuppy
      email: <EMAIL>
      url:
  components:
    # 鉴权方式配置
    security-schemes:
      apiKey:
        type: APIKEY
        in: HEADER
        name: ${sa-token.token-name}
  #这里定义了两个分组，可定义多个，也可以不定义
  group-configs:
    - group: common
      packages-to-scan: org.dromara.web
    - group: system
      packages-to-scan: org.dromara.system
    - group: platform
      paths-to-match: /platform/**
    - group: mp
      paths-to-match: /mp/**

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice,/platform/club_activity/save,/platform/club_activity/update,/platform/package
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*,/platform/*,/mp/*

# 全局线程池相关配置
thread-pool:
  # 是否开启线程池
  enabled: false
  # 队列最大长度
  queueCapacity: 128
  # 线程池维护线程所允许的空闲时间
  keepAliveSeconds: 300

--- # 分布式锁 lock4j 全局配置
lock4j:
  # 获取分布式锁超时时间，默认为 3000 毫秒
  acquire-timeout: 3000
  # 分布式锁的超时时间，默认为 30 秒
  expire: 30000

--- # Actuator 监控端点的配置项
management:
  endpoints:
    web:
      exposure:
        include: '*'
  endpoint:
    health:
      show-details: ALWAYS
    logfile:
      external-file: ./logs/sys-console.log

--- # websocket
websocket:
  # 如果关闭 需要和前端开关一起关闭
  enabled: true
  # 路径
  path: /resource/websocket
  # 设置访问源地址
  allowedOrigins: '*'
