package org.dromara.h5edit.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.h5edit.entity.Font;
import org.dromara.h5edit.repository.FontMapper;
import org.dromara.h5edit.service.FontService;
import org.dromara.h5edit.vo.FontResult;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【l_font(请帖字体表)】的数据库操作Service实现
* @createDate 2025-01-22 16:12:16
*/
@Service
public class FontServiceImpl extends ServiceImpl<FontMapper, Font>
    implements FontService{

    @Override
    public List<FontResult> getList() {
        return lambdaQuery()
                .eq(Font::getDelFlag, "0")
                .orderByDesc(Font::getCreateTime)
                .list()
                .stream()
                .map(font->{
                    FontResult fontResult = new FontResult();
                    fontResult.setName(font.getNickName());
                    fontResult.setUrl(font.getUrl());
                    fontResult.setKey(font.getName());
                    fontResult.setText("文本示例");
                    fontResult.setExample(font.getLogo());
                    return fontResult;
                })
                .toList();
    }

    @Override
    public void saveFont(String fileName, String url, String charset) {
        Font font = new Font();
        font.setNickName(fileName);
        font.setUrl(url);
        font.setLogo(charset);
        save(font);
    }
}




