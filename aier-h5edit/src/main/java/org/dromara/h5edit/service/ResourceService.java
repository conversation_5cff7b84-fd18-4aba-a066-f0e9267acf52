package org.dromara.h5edit.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.h5edit.dto.CropDTO;
import org.dromara.h5edit.entity.Resource;
import org.dromara.h5edit.vo.FontResult;
import org.dromara.h5edit.vo.MusicResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【l_resource】的数据库操作Service
* @createDate 2025-01-18 22:05:55
*/
public interface ResourceService extends IService<Resource> {

    Resource uploadImage(MultipartFile file);

    void init();

    List<String> getUserList(Long userId);

    String cropImage(CropDTO cropDTO);

    Resource uploadPublic(MultipartFile file);

    List<String> getList();

    FontResult uploadFont(MultipartFile file);

    /**
     * 上传音乐文件
     * @param file 文件
     */
    MusicResult uploadMusic(MultipartFile file);
    /**
     * 上传音乐文件
     * @param file 文件
     */
    MusicResult uploadMusic(MultipartFile file, String name, String author, String length);
}
