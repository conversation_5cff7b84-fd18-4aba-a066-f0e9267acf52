package org.dromara.h5edit.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.h5edit.entity.Font;
import org.dromara.h5edit.vo.FontResult;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【l_font(请帖字体表)】的数据库操作Service
* @createDate 2025-01-22 16:12:16
*/
public interface FontService extends IService<Font> {

    List<FontResult> getList();

    void saveFont(String fileName, String url, String charset);
}
