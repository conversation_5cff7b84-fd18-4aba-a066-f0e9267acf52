package org.dromara.h5edit.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.h5edit.entity.Music;
import org.dromara.h5edit.qo.MusicQO;
import org.dromara.h5edit.vo.MusicResult;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【l_music】的数据库操作Service
* @createDate 2025-01-21 15:25:29
*/
public interface MusicService extends IService<Music> {

    void saveMusic(String name, String url, String author, String length);

    List<MusicResult> getList(MusicQO query);

    TableDataInfo<MusicResult> queryPage(MusicQO query);
}
