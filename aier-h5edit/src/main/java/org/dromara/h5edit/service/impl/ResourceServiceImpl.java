package org.dromara.h5edit.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.constant.CacheNames;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.oss.constant.OssConstant;
import org.dromara.common.oss.core.OssClient;
import org.dromara.common.oss.entity.UploadResult;
import org.dromara.common.oss.exception.OssException;
import org.dromara.common.oss.factory.OssFactory;
import org.dromara.common.redis.utils.CacheUtils;
import org.dromara.common.redis.utils.RedisUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.h5edit.dto.CropDTO;
import org.dromara.h5edit.entity.Resource;
import org.dromara.h5edit.repository.ResourceMapper;
import org.dromara.h5edit.service.MusicService;
import org.dromara.h5edit.service.ResourceService;
import org.dromara.h5edit.vo.FontResult;
import org.dromara.h5edit.vo.MusicResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;

/**
* <AUTHOR>
* @description 针对表【l_resource】的数据库操作Service实现
* @createDate 2025-01-18 22:05:55
*/
@Service
@RequiredArgsConstructor
public class ResourceServiceImpl extends ServiceImpl<ResourceMapper, Resource>
    implements ResourceService{

    private final MusicService musicService;

    @Override
    public void init() {
        Map<String, Map<String, String>> config = baseMapper.getOssConfig();
        config.forEach((k, v) -> {
            if ("0".equals(v.get("status"))) {
                RedisUtils.setCacheObject(OssConstant.DEFAULT_CONFIG_KEY, k);
            }
            CacheUtils.put(CacheNames.SYS_OSS_CONFIG, k, JsonUtils.toJsonString(v));
        });
    }

    @Override
    public List<String> getUserList(Long userId) {
        return lambdaQuery().eq(Resource::getUserId, userId)
                .orderByDesc(Resource::getCreateTime).list()
                .stream().map(Resource::getUrl).toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String cropImage(CropDTO cropDTO) {
        File tempFile = null;
        try {
            String imageUrl = cropDTO.getUrl();
            int x = cropDTO.getX();
            int y = cropDTO.getY();
            int w = cropDTO.getWidth();
            int h = cropDTO.getHeight();
            String originalFileName = StringUtils.substringAfterLast(imageUrl, "/");
            String suffix = StringUtils.substringAfterLast(originalFileName, ".");
            if (!isImageFormat(suffix)) throw new OssException("不支持的图片格式");
            BufferedImage originalImage = downloadImage(imageUrl);
            if (originalImage == null) throw new OssException("无法读取图片");
            validateCropParameters(originalImage, x, y, w, h);
            BufferedImage croppedImage = originalImage.getSubimage(x, y, w, h);
            tempFile = File.createTempFile("cropped_", "." + suffix);
            ImageIO.write(croppedImage, suffix, tempFile);
            UploadResult uploadResult = upload(FileUtil.readBytes(tempFile), suffix, "image/" + suffix);
            return uploadResult.getUrl();
        } catch (Exception e) {
            log.error("处理网络图片失败", e);
            throw new OssException("处理失败: " + e.getMessage());
        } finally {
            // 清理临时文件
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }

    /**
     * 下载网络图片
     */
    private BufferedImage downloadImage(String imageUrl) throws IOException {
        URL url = new URL(imageUrl);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestProperty("User-Agent", "Mozilla/5.0");
        try (InputStream is = conn.getInputStream()) {
            return ImageIO.read(is);
        }
    }

    /**
     * 验证图片格式
     */
    private boolean isImageFormat(String suffix) {
        if (StringUtils.isEmpty(suffix)) {
            return false;
        }
        Set<String> supportedFormats = new HashSet<>(Arrays.asList(
                "jpg", "jpeg", "png", "gif", "bmp", "webp"
        ));
        return supportedFormats.contains(suffix.toLowerCase());
    }

    /**
     * 验证裁剪参数
     */
    private void validateCropParameters(BufferedImage image, int x, int y, int width, int height) {
        if (x < 0 || y < 0 || width <= 0 || height <= 0) {
            throw new OssException("裁剪参数无效");
        }

        if (x + width > image.getWidth() || y + height > image.getHeight()) {
            throw new OssException("裁剪区域超出图片范围");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Resource uploadPublic(@NotNull MultipartFile file) {
        return uploadImage(file, "public");
    }

    @Override
    public List<String> getList() {
        return lambdaQuery().eq(Resource::getType, "public")
                .orderByDesc(Resource::getCreateTime)
                .list().stream().map(Resource::getUrl).toList();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Resource uploadImage(@NotNull MultipartFile file) {
        return uploadImage(file, "private");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public FontResult uploadFont(MultipartFile file){
        try {
            if (ObjectUtil.isNull(file)) throw new OssException("File is null");
            String originalFileName = file.getOriginalFilename();
            String suffix = StringUtils.substringAfterLast(originalFileName, ".");
            UploadResult uploadResult = upload(file.getBytes(), suffix, file.getContentType());
            return null;
        }catch (Exception e){
            throw new OssException("Failed to upload file: " + e.getMessage());
        }
    }

    @Override
    public MusicResult uploadMusic(MultipartFile file) {
        try {
            if (ObjectUtil.isNull(file)) throw new OssException("File is null");
            String originalFileName = file.getOriginalFilename();
            String suffix = StringUtils.substringAfterLast(originalFileName, ".");
            UploadResult uploadResult = upload(file.getBytes(), suffix, file.getContentType());
            musicService.saveMusic(originalFileName, uploadResult.getUrl(), null, null);
            MusicResult musicResult = new MusicResult();
            musicResult.setName(originalFileName);
            musicResult.setUrl(uploadResult.getUrl());
            return musicResult;
        } catch (Exception e){
            throw new OssException("Failed to upload file: " + e.getMessage());
        }
    }

    @Override
    public MusicResult uploadMusic(MultipartFile file, String name, String author, String length) {
        try {
            if (ObjectUtil.isNull(file)) throw new OssException("File is null");
            String originalFileName = file.getOriginalFilename();
            String suffix = StringUtils.substringAfterLast(originalFileName, ".");
            UploadResult uploadResult = upload(file.getBytes(), suffix, file.getContentType());
            musicService.saveMusic(name, uploadResult.getUrl(), author, length);
            MusicResult musicResult = new MusicResult();
            musicResult.setName(originalFileName);
            musicResult.setUrl(uploadResult.getUrl());
            return musicResult;
        } catch (Exception e){
            throw new OssException("Failed to upload file: " + e.getMessage());
        }
    }

    private Resource uploadImage(@NotNull MultipartFile file, String type) {
        try {
            if (ObjectUtil.isNull(file)) throw new OssException("File is null");
            String originalFileName = file.getOriginalFilename();
            String suffix = StringUtils.substringAfterLast(originalFileName, ".");
            UploadResult uploadResult = upload(file.getBytes(), suffix, file.getContentType());
            Resource resource = new Resource();
            resource.setUrl(uploadResult.getUrl());
            resource.setFileName(uploadResult.getFilename());
            resource.setFileSuffix(suffix);
            resource.setOriginalName(originalFileName);
            resource.setType(type);
            resource.setCreateTime(DateUtil.date());
            resource.setUserId(LoginHelper.getUserId());
            save(resource);
            return resource;
        } catch (Exception e) {
            throw new OssException("Failed to upload file: " + e.getMessage());
        }
    }

    private UploadResult upload(@NotNull byte[] bytes, String suffix, String contentType){
        OssClient storage = OssFactory.instance();
        return storage.uploadSuffix(bytes, "." + suffix, contentType);
    }
}




