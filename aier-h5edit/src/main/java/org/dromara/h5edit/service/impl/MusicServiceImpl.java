package org.dromara.h5edit.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.h5edit.entity.Music;
import org.dromara.h5edit.qo.MusicQO;
import org.dromara.h5edit.repository.MusicMapper;
import org.dromara.h5edit.service.MusicService;
import org.dromara.h5edit.vo.MusicResult;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【l_music】的数据库操作Service实现
* @createDate 2025-01-21 15:25:29
*/
@Service
public class MusicServiceImpl extends ServiceImpl<MusicMapper, Music>
    implements MusicService{

    @Override
    public void saveMusic(String name, String url, String author, String length) {
        Music music = new Music();
        music.setUserId(LoginHelper.getUserId());
        music.setName(name);
        music.setUrl(url);
        music.setAuthor(author);
        music.setCreateTime(DateUtil.date());
        music.setDelFlag("0");
        music.setLength(length);
        save(music);
    }

    @Override
    public List<MusicResult> getList(MusicQO query) {
        return lambdaQuery()
                .like(StrUtil.isNotBlank(query.getKeyword()), Music::getName, query.getKeyword())
                .eq(Music::getDelFlag, "0")
                .orderByDesc(Music::getCreateTime)
                .list()
                .stream()
                .map(music->{
                    MusicResult musicResult = new MusicResult();
                    musicResult.setName(music.getName());
                    musicResult.setUrl(music.getUrl());
                    return musicResult;
                })
                .toList();
    }

    @Override
    public TableDataInfo<MusicResult> queryPage(MusicQO query) {
        IPage<Music> page = lambdaQuery()
                .like(StrUtil.isNotBlank(query.getKeyword()), Music::getName, query.getKeyword())
                .eq(Music::getDelFlag, "0")
                .orderByDesc(Music::getCreateTime)
                .page(query.build());

        List<MusicResult> musicResults = page.getRecords().stream()
                .map(music -> {
                    MusicResult musicResult = new MusicResult();
                    musicResult.setName(music.getName());
                    musicResult.setUrl(music.getUrl());
                    return musicResult;
                })
                .toList();

        return TableDataInfo.build(musicResults, page.getTotal());
    }
}




