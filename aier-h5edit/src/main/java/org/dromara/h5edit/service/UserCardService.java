package org.dromara.h5edit.service;

import org.dromara.h5edit.dto.*;
import org.dromara.h5edit.vo.UserCardResult;

import java.util.List;

public interface UserCardService {
    /**
     * 查询用户创建的请帖详情
     * @param cardId 请帖id
     * @param tenantId 租户id
     */
    UserCardResult getUserCardDetail(Long cardId, String tenantId);

    Boolean saveUserCard(UserCardSaveDTO card);

    Boolean getEditStatus(Long id, String tenantId);

    Long createCard(Long id, Long userId, String tenantId);

    Boolean delete(Long id);

    String collectUserCard(UserCardResult userCard);

    void saveCardReplay(CardReplayDTO cardReplayDTO);

    CardSettingDTO getCardSetting(Long cardId, String tenantId);

    Boolean saveCardSetting(CardSettingDTO cardSetting);

    CardInvitationDTO getCardInvitation(Long cardId);

    /**
     * 保存请帖留言
     * @param cardMessageDTO 留言
     */
    void saveCardMessage(CardMessageDTO cardMessageDTO);

    /**
     * 查询请帖留言列表
     * @param cardId 请帖id
     * @param tenantId 租户id
     */
    List<CardMessageDTO> getCardMessageList(Long cardId, String tenantId);
}
