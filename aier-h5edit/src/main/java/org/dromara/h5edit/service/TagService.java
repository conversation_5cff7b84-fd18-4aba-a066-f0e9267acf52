package org.dromara.h5edit.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.h5edit.dto.TagDTO;
import org.dromara.h5edit.entity.Tag;
import org.dromara.h5edit.qo.TagQO;
import org.dromara.h5edit.vo.TagVO;

import java.util.List;

/**
 * 标签服务接口
 * <AUTHOR> Puppy
 * @description 针对表【l_tag(H5模版标签表)】的数据库操作Service
 * @since 2025-01-27
 */
public interface TagService extends IService<Tag> {

    /**
     * 分页查询标签列表
     * @param query 查询参数
     * @return 分页结果
     */
    TableDataInfo<TagVO> queryPage(TagQO query);

    /**
     * 新增标签
     * @param tagDTO 标签信息
     * @return 标签ID
     */
    Long addTag(TagDTO tagDTO);

    /**
     * 更新标签
     * @param tagDTO 标签信息
     * @return 是否成功
     */
    Boolean updateTag(TagDTO tagDTO);

    /**
     * 删除标签
     * @param tagId 标签ID
     * @return 是否成功
     */
    Boolean deleteTag(Long tagId);

    /**
     * 获取所有标签（用于下拉选择）
     * @return 标签列表
     */
    List<TagVO> getAllTags();
}
