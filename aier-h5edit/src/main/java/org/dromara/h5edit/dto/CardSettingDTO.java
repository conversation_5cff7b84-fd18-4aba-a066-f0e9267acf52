package org.dromara.h5edit.dto;

import lombok.Data;

import java.util.Date;

/**
 * 请帖功能设置DTO
 */
@Data
public class CardSettingDTO {

    /**
     * 请帖id
     */
    private Long cardId;

    /**
     * 是否开启认证
     */
    private Boolean squareAuth;

    /**
     * 是否开启弹幕功能
     */
    private Boolean danmu;

    /**
     * 是否开启礼物功能
     */
    private Boolean gift;

    /**
     * 是否开启礼金功能
     */
    private Boolean gold;

    /**
     * 是否开启留言功能
     */
    private Boolean wish;

    /**
     * 动画速度 1正常速度
     */
    private Integer speed;

    /**
     * 标题
     */
    private String title;

    /**
     * 
     */
    private Date createTime;

    /**
     * 
     */
    private Date updateTime;

    /**
     * 
     */
    private String tenantId;
}