package org.dromara.h5edit.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.Date;

/**
 * 赴宴回复dto
 */
@Data
public class CardReplayDTO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 赴宴人姓名
     */
    @NotBlank(message = "赴宴人姓名不能为空")
    private String replayName;

    /**
     * 租户id
     */
    @NotBlank(message = "租户id不能为空")
    private String tenantId;

    /**
     * 赴宴人数
     */
    @NotBlank(message = "赴宴人数不能为空")
    private String replayNum;

    /**
     * 赴宴状态 (attending: 赴宴, tentative: 待定, declined: 有事)
     */
    @NotBlank(message = "赴宴状态不能为空")
    private String replayStatus;

    /**
     * 请帖id
     */
    @NotNull(message = "请帖id不能为空")
    private Long cardId;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
}
