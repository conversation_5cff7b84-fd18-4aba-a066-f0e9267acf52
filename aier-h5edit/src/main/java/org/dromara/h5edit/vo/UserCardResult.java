package org.dromara.h5edit.vo;

import lombok.Data;
import org.dromara.h5edit.dto.CardSettingDTO;

import java.util.Date;

@Data
public class UserCardResult {

    /**
     * cardId
     */
    private Long id;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 用户id 对应小程序的用户体系
     */
    private Long userId;

    /**
     * h5模版id 创建请帖时的h5模版id
     */
    private Long templateId;

    /**
     * 是否分享
     */
    private Boolean isShare;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 请帖功能设置
     */
    private CardSettingDTO cardSetting = new CardSettingDTO();

    /**
     * 删除标识
     */
    private String delFlag;
}
