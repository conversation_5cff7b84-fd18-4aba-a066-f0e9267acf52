package org.dromara.h5edit.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.h5edit.entity.Tag;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 标签视图对象
 * <AUTHOR>
 * @since 2025-01-27
 */
@Data
@AutoMapper(target = Tag.class)
public class TagVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 标签ID
     */
    private Long id;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 标签描述
     */
    private String tagDesc;

    /**
     * 标签颜色(十六进制)
     */
    private String tagColor;

    /**
     * 排序权重，数值越大越靠前
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 使用次数（统计信息）
     */
    private Integer usageCount;
}
