package org.dromara.h5edit.qo;

import lombok.Data;

import java.util.List;

@Data
public class OpusQO {

    private String title;

    private Integer pageIndex;

    private Integer pageSize = 1000;

    private Integer type;

    /**
     * 标签ID列表（用于按标签筛选模版）
     */
    private List<Long> tagIds;

    /**
     * 标签匹配模式：true=AND查询（模版必须包含所有指定标签），false=OR查询（模版包含任意一个指定标签）
     */
    private Boolean tagMatchAll = false;
}
