package org.dromara.h5edit.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 
 * @TableName l_music
 */
@TableName(value ="l_music")
@Data
public class Music {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 资源链接
     */
    private String url;

    /**
     * 名称
     */
    private String name;

    /**
     * 作者
     */
    private String author;

    /**
     * 时长
     */
    private String length;

    /**
     * 
     */
    private Long userId;

    /**
     * 
     */
    private Date createTime;

    @TableLogic
    private String delFlag;
}