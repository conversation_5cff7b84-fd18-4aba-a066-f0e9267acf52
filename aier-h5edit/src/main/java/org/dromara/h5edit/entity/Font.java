package org.dromara.h5edit.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 请帖字体表
 * @TableName l_font
 */
@TableName(value ="l_font")
@Data
public class Font {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 字体名称
     */
    private String name;

    /**
     * 中文名称
     */
    private String nickName;

    /**
     * logo
     */
    private String logo;

    /**
     * 字体文件url
     */
    private String url;

    /**
     * 字体文件大小
     */
    private Long size;

    /**
     * 默认行高
     */
    private Integer defaultLineHeight;

    /**
     * 字体大小
     */
    private Integer fontSize;

    /**
     * 
     */
    private Date createTime;

    /**
     * 
     */
    private Date updateTime;

    @TableLogic
    private String delFlag;
}