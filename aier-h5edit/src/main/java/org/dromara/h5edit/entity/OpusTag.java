package org.dromara.h5edit.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * 模版标签关联实体类
 * @TableName l_opus_tag
 * <AUTHOR> <PERSON>uppy
 * @since 2025-01-27
 */
@TableName(value = "l_opus_tag")
@Data
public class OpusTag {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 模版ID，关联l_opus表
     */
    private Long opusId;

    /**
     * 标签ID，关联l_tag表
     */
    private Long tagId;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;
}
