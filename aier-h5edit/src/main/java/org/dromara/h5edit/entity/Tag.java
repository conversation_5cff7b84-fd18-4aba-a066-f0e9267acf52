package org.dromara.h5edit.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * H5模版标签实体类
 * @TableName l_tag
 * <AUTHOR> <PERSON>uppy
 * @since 2025-01-27
 */
@TableName(value = "l_tag")
@Data
public class Tag {
    
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 标签描述
     */
    private String tagDesc;

    /**
     * 标签颜色(十六进制)
     */
    private String tagColor;

    /**
     * 排序权重，数值越大越靠前
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 删除标志(0正常 2删除)
     */
    @TableLogic
    private String delFlag;
}
