package org.dromara.h5edit.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName l_log
 */
@TableName(value ="l_log")
@Data
public class Log {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 
     */
    private Integer opusId;

    /**
     * 
     */
    private String ip;

    /**
     * 
     */
    private String ua;

    /**
     * 
     */
    private Date addTime;
}