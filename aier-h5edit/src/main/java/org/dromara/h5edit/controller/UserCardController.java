package org.dromara.h5edit.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.h5edit.dto.*;
import org.dromara.h5edit.service.UserCardService;
import org.dromara.h5edit.vo.UserCardResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 小程序端用户创建的请帖相关接口
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/user/card")
@SaIgnore
public class UserCardController {

    private final UserCardService userCardService;

    /**
     * 获取用户创建的h5模版详情
     */
    @GetMapping("/getUserCardDetail")
    public R<UserCardResult> getUserCardDetail(@RequestParam("cardId") Long cardId, @RequestParam("tenantId") String tenantId) {
        return R.ok(userCardService.getUserCardDetail(cardId,tenantId));
    }

    /**
     * 保存请帖功能设置
     */
    @PostMapping("/saveSetting")
    @RepeatSubmit(interval = 1000)
    public R<Boolean> saveCardSetting(@RequestBody CardSettingDTO cardSetting) {
        return R.ok(userCardService.saveCardSetting(cardSetting));
    }

    /**
     * 查询用户请帖功能设置信息
     */
    @GetMapping("/getSetting")
    public R<CardSettingDTO> getCardSetting(@RequestParam("cardId") Long cardId, @RequestParam("tenantId") String tenantId) {
        return R.ok(userCardService.getCardSetting(cardId, tenantId));
    }

    /**
     * 查询请帖邀请人和分享信息
     */
    @GetMapping("/getInvitation")
    public R<CardInvitationDTO> getInvitation(@RequestParam("cardId") Long cardId) {
        return R.ok(userCardService.getCardInvitation(cardId));
    }

    /**
     * 保存用户的h5模版
     */
    @PostMapping("/saveUserCard")
    public R<Boolean> saveUserCard(@RequestBody @Validated UserCardSaveDTO card) {
        return R.ok(userCardService.saveUserCard(card));
    }

    /**
     * 赴宴回复
     */
    @PostMapping("/replay")
    public R<Boolean> saveCardReplay(@RequestBody @Validated CardReplayDTO cardReplayDTO) {
        userCardService.saveCardReplay(cardReplayDTO);
        return R.ok(true);
    }

    /**
     * 留言
     */
    @PostMapping("/message")
    public R<Boolean> saveCardMessage(@RequestBody @Validated CardMessageDTO cardMessageDTO){
        userCardService.saveCardMessage(cardMessageDTO);
        return R.ok(true);
    }

    /**
     * 查询请帖留言列表
     */
    @GetMapping("/getCardMessageList")
    public R<List<CardMessageDTO>> getCardMessageList(@RequestParam("cardId") Long cardId, @RequestParam("tenantId") String tenantId) {
        return R.ok(userCardService.getCardMessageList(cardId, tenantId));
    }
}
