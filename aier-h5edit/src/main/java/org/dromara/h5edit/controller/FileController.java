package org.dromara.h5edit.controller;

import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.h5edit.dto.CropDTO;
import org.dromara.h5edit.entity.Resource;
import org.dromara.h5edit.service.ResourceService;
import org.dromara.h5edit.vo.FontResult;
import org.dromara.h5edit.vo.MusicResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件上传相关接口
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/file")
public class FileController {

    private final ResourceService resourceService;

    /**
     * 上传图片文件
     * @param file 文件
     */
    @PostMapping("/upload")
    public R<Resource> upload(@RequestParam("upFile") MultipartFile file) {
        return R.ok(resourceService.uploadImage(file));
    }

    /**
     * 上传字体文件
     * @param file 文件
     */
    @PostMapping("/uploadFont")
    public R<FontResult> uploadFont(@RequestParam("upFile") MultipartFile file) {
        return R.ok(resourceService.uploadFont(file));
    }

    /**
     * 上传音乐文件
     * @param file 文件
     */
    @PostMapping("/uploadMusic")
    public R<MusicResult> uploadMusic(@RequestParam("upFile") MultipartFile file,
                                      @RequestParam(value = "name", required = false) String name,
                                      @RequestParam(value = "author", required = false) String author,
                                      @RequestParam(value = "length", required = false) String length) {
        return R.ok(resourceService.uploadMusic(file, name, author, length));
    }

    /**
     * 上传公开资源文件
     * @param file 文件
     */
    @PostMapping("/uploadPublic")
    public R<Resource> uploadPublic(@RequestParam("upFile") MultipartFile file) {
        return R.ok(resourceService.uploadPublic(file));
    }

    /**
     * 查询公开资源文件
     */
    @GetMapping("/getList")
    public R<List<String>> getList() {
        return R.ok(resourceService.getList());
    }

    /**
     * 查询自己上传的资源
     */
    @GetMapping("/getMyList")
    public R<List<String>> getMyList() {
        Long userId = LoginHelper.getUserId();
        return R.ok(resourceService.getUserList(userId));
    }

    /**
     * 服务端裁剪图片
     * @param cropDTO 裁剪参数
     */
    @PostMapping("/cropImage")
    public R<String> cropImage(@RequestBody CropDTO cropDTO){
        return R.ok("",resourceService.cropImage(cropDTO));
    }
}