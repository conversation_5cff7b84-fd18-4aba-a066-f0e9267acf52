package org.dromara.h5edit.controller;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.SecureUtil;
import io.swagger.v3.oas.annotations.Hidden;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.api.WxMpService;
import org.dromara.common.core.domain.R;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
/**
 * 微信公众号消息订阅
 */
@RestController
@AllArgsConstructor
@RequestMapping("/wx_mp/portal/{appid}")
@Slf4j
@SaIgnore
@Hidden
public class WxMpPortalController {

    private final WxMpService wxService;

    /**
     * 获取wxconfig
     */
    @GetMapping("/config")
    public R<Map<String, String>> getConfig(@PathVariable String appid, @RequestParam("url") String url) {
        try {
            if (!this.wxService.switchover(appid)) {
                throw new IllegalArgumentException(String.format("未找到对应appid=[%s]的配置，请核实！", appid));
            }
            String ticket = wxService.getJsapiTicket();
            String noncestr = RandomUtil.randomString(10);//随机字符串
            String timestamp = String.valueOf(System.currentTimeMillis());//时间戳
            String str = "jsapi_ticket="+ticket+"&noncestr="+noncestr+"&timestamp="+timestamp+"&url="+url;
            String signature = SecureUtil.sha1(str);
            Map<String,String> map = new HashMap();
            map.put("timestamp",timestamp);
            map.put("ticket",ticket);
            map.put("noncestr",noncestr);
            map.put("signature",signature);
            map.put("appid",appid);
            return R.ok(map);
        }catch (Exception e) {
            log.error(e.getMessage(), e);
            return R.fail(e.getMessage());
        }
    }
}
