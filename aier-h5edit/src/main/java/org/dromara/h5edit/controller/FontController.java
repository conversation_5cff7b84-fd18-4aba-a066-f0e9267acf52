package org.dromara.h5edit.controller;

import lombok.RequiredArgsConstructor;
import org.dromara.common.core.domain.R;
import org.dromara.h5edit.service.FontService;
import org.dromara.h5edit.vo.FontResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 字体资源相关接口
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/font")
public class FontController {

    private final FontService fontService;

    /**
     * 查询字体资源列表
     */
    @GetMapping("/getList")
    public R<List<FontResult>> getList() {
        return R.ok(fontService.getList());
    }
}
