<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.h5edit.repository.UserCardMapper">

    <resultMap id="BaseResultMap" type="org.dromara.h5edit.vo.UserCardResult">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="user_id" property="userId"/>
        <result column="del_flag" property="delFlag"/>
        <result column="template_id" property="templateId"/>
        <result column="is_share" property="isShare"/>
        <result column="tenant_id" property="tenantId"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, title, content, create_time, update_time, user_id, del_flag, template_id, is_share, tenant_id
    </sql>

    <insert id="insert" parameterType="org.dromara.h5edit.vo.UserCardResult" useGeneratedKeys="true" keyProperty="id">
        insert into biz_card
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="title != null">title,</if>
            <if test="content != null">content,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="userId != null">user_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="templateId != null">template_id,</if>
            <if test="isShare != null">is_share,</if>
            <if test="tenantId != null">tenant_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="title != null">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="userId != null">#{userId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="templateId != null">#{templateId},</if>
            <if test="isShare != null">#{isShare},</if>
            <if test="tenantId != null">#{tenantId},</if>
        </trim>
    </insert>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into biz_card (title, content, create_time, update_time, user_id, del_flag, template_id, is_share, tenant_id)
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.title}, #{item.content}, #{item.createTime}, #{item.updateTime},
            #{item.userId}, #{item.delFlag}, #{item.templateId}, #{item.isShare}, #{item.tenantId}
            )
        </foreach>
    </insert>
    <insert id="collectUserCard" parameterType="org.dromara.h5edit.vo.UserCardResult">
        insert into biz_card_collect
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="templateId != null">template_id,</if>
            <if test="tenantId != null">tenant_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="templateId != null">#{templateId},</if>
            <if test="tenantId != null">#{tenantId},</if>
        </trim>
    </insert>

    <delete id="deleteById" parameterType="java.lang.Long">
        delete from biz_card where id = #{id}
    </delete>
    <delete id="cancelCollectUserCard" parameterType="org.dromara.h5edit.vo.UserCardResult">
        delete from biz_card_collect
        <where>
            <if test="userId != null">and user_id = #{userId}</if>
            <if test="templateId != null">and template_id = #{templateId}</if>
            <if test="tenantId != null">and tenant_id = #{tenantId}</if>
            <if test="id != null">and id = #{id}</if>
        </where>
    </delete>

    <update id="updateById" parameterType="org.dromara.h5edit.vo.UserCardResult">
        update biz_card
        <set>
            <if test="title != null">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="templateId != null">template_id = #{templateId},</if>
            <if test="isShare != null">is_share = #{isShare},</if>
            <if test="tenantId != null">tenant_id = #{tenantId},</if>
        </set>
        where id = #{id}
    </update>

    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_card
        where id = #{id}
    </select>

    <select id="selectList" parameterType="org.dromara.h5edit.vo.UserCardResult" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_card
        <where>
            <if test="title != null and title != ''">and title like concat('%', #{title}, '%')</if>
            <if test="content != null and content != ''">and content like concat('%', #{content}, '%')</if>
            <if test="userId != null">and user_id = #{userId}</if>
            <if test="delFlag != null and delFlag != ''">and del_flag = #{delFlag}</if>
            <if test="templateId != null">and template_id = #{templateId}</if>
            <if test="isShare != null">and is_share = #{isShare}</if>
            <if test="tenantId != null">and tenant_id = #{tenantId}</if>
            <if test="id != null">and id = #{id}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectByUserId" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from biz_card
        where user_id = #{userId}
        order by create_time desc
    </select>

    <select id="count" parameterType="org.dromara.h5edit.vo.UserCardResult" resultType="java.lang.Integer">
        select count(1) from biz_card
        <where>
            <if test="title != null and title != ''">and title like concat('%', #{title}, '%')</if>
            <if test="content != null and content != ''">and content like concat('%', #{content}, '%')</if>
            <if test="userId != null">and user_id = #{userId}</if>
            <if test="delFlag != null and delFlag != ''">and del_flag = #{delFlag}</if>
            <if test="templateId != null">and template_id = #{templateId}</if>
            <if test="isShare != null">and is_share = #{isShare}</if>
        </where>
    </select>
    <select id="getCollect" resultType="java.lang.Integer"
            parameterType="org.dromara.h5edit.vo.UserCardResult">
        select count(1) from biz_card_collect
        <where>
            <if test="id != null">and id = #{id}</if>
            <if test="userId != null">and user_id = #{userId}</if>
            <if test="templateId != null">and template_id = #{templateId}</if>
        </where>
    </select>

    <insert id="saveCardReplay" parameterType="org.dromara.h5edit.dto.CardReplayDTO" useGeneratedKeys="true"
            keyProperty="id">
        INSERT INTO biz_card_replay (card_id,
                                     replay_name,
                                     tenant_id,
                                     replay_num,
                                     create_time,
                                     update_time,
                                     replay_status)
        VALUES (#{cardId},
                #{replayName},
                #{tenantId},
                #{replayNum},
                #{createTime},
                #{updateTime},
                #{replayStatus})
    </insert>

    <insert id="saveCardMessage" parameterType="org.dromara.h5edit.dto.CardMessageDTO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO biz_card_message (card_id,
                                      tenant_id,
                                      message,
                                      name,
                                      create_time,
                                      update_time)
        VALUES (#{cardId},
                #{tenantId},
                #{message},
                #{name},
                #{createTime},
                #{updateTime})
    </insert>

    <select id="getCardSetting" resultType="org.dromara.h5edit.dto.CardSettingDTO">
        select * from biz_card_setting where card_id = #{cardId} and tenant_id = #{tenantId}
    </select>

    <select id="getCardMessageList" resultType="org.dromara.h5edit.dto.CardMessageDTO">
        select
            id,
            card_id,
            tenant_id,
            message,
            name,
            create_time,
            update_time
        from
            biz_card_message
        where
            card_id = #{cardId} and tenant_id = #{tenantId}
    </select>

    <update id="saveCardSetting" parameterType="org.dromara.h5edit.dto.CardSettingDTO">
        UPDATE biz_card_setting
        <set>
            <if test="squareAuth != null">
                square_auth = #{squareAuth},
            </if>
            <if test="danmu != null">
                danmu = #{danmu},
            </if>
            <if test="gift != null">
                gift = #{gift},
            </if>
            <if test="gold != null">
                gold = #{gold},
            </if>
            <if test="wish != null">
                wish = #{wish},
            </if>
            <if test="speed != null">
                speed = #{speed},
            </if>
            <if test="title != null">
                title = #{title},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId},
            </if>
        </set>
        WHERE card_id = #{cardId}
    </update>

    <select id="getCardInvitation" resultType="org.dromara.h5edit.dto.CardInvitationDTO">
        select * from biz_card_invitations where card_id = #{cardId}
    </select>

</mapper>
