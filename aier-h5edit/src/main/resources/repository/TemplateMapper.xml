<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.h5edit.repository.TemplateMapper">

    <resultMap id="BaseResultMap" type="org.dromara.h5edit.entity.Template">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="opusId" column="opus_id" jdbcType="INTEGER"/>
            <result property="addTime" column="add_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,opus_id,add_time
    </sql>
</mapper>
