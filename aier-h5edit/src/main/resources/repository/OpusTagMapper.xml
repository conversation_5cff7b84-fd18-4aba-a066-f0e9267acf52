<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.h5edit.repository.OpusTagMapper">

    <resultMap id="BaseResultMap" type="org.dromara.h5edit.entity.OpusTag">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="opusId" column="opus_id" jdbcType="BIGINT"/>
        <result property="tagId" column="tag_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, opus_id, tag_id, create_time
    </sql>

    <!-- 批量插入模版标签关联 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO l_opus_tag (opus_id, tag_id, create_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.opusId}, #{item.tagId}, #{item.createTime})
        </foreach>
    </insert>

    <!-- 根据模版ID删除所有标签关联 -->
    <delete id="deleteByOpusId">
        DELETE FROM l_opus_tag WHERE opus_id = #{opusId}
    </delete>

    <!-- 根据标签ID删除所有模版关联 -->
    <delete id="deleteByTagId">
        DELETE FROM l_opus_tag WHERE tag_id = #{tagId}
    </delete>

    <!-- 根据模版ID和标签ID列表删除关联 -->
    <delete id="deleteByOpusIdAndTagIds">
        DELETE FROM l_opus_tag 
        WHERE opus_id = #{opusId}
        <if test="tagIds != null and tagIds.size() > 0">
            AND tag_id IN
            <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
                #{tagId}
            </foreach>
        </if>
    </delete>

    <!-- 根据模版ID查询标签ID列表 -->
    <select id="selectTagIdsByOpusId" resultType="java.lang.Long">
        SELECT tag_id FROM l_opus_tag WHERE opus_id = #{opusId}
    </select>

    <!-- 根据标签ID查询模版ID列表 -->
    <select id="selectOpusIdsByTagId" resultType="java.lang.Long">
        SELECT opus_id FROM l_opus_tag WHERE tag_id = #{tagId}
    </select>

    <!-- 根据标签ID列表查询模版ID列表（支持AND/OR查询） -->
    <select id="selectOpusIdsByTagIds" resultType="java.lang.Long">
        <choose>
            <!-- AND查询：模版必须包含所有指定标签 -->
            <when test="matchAll == true">
                SELECT opus_id
                FROM l_opus_tag
                WHERE tag_id IN
                <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
                    #{tagId}
                </foreach>
                GROUP BY opus_id
                HAVING COUNT(DISTINCT tag_id) = #{tagIds.size()}
            </when>
            <!-- OR查询：模版包含任意一个指定标签 -->
            <otherwise>
                SELECT DISTINCT opus_id
                FROM l_opus_tag
                WHERE tag_id IN
                <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
                    #{tagId}
                </foreach>
            </otherwise>
        </choose>
    </select>

</mapper>
