.
├── aier-admin
│   ├── logs
│   ├── src
│   │   ├── main
│   │   │   ├── java
│   │   │   │   └── org
│   │   │   │       └── dromara
│   │   │   │           └── web
│   │   │   │               ├── controller
│   │   │   │               ├── domain
│   │   │   │               │   └── vo
│   │   │   │               ├── listener
│   │   │   │               └── service
│   │   │   │                   └── impl
│   │   │   └── resources
│   │   │       └── i18n
│   │   └── test
│   │       └── java
│   │           └── org
│   │               └── dromara
│   │                   └── test
│   └── target
│       ├── classes
│       │   ├── META-INF
│       │   │   └── mps
│       │   ├── i18n
│       │   ├── io
│       │   │   └── github
│       │   │       └── linpeilie
│       │   └── org
│       │       └── dromara
│       │           ├── system
│       │           │   └── domain
│       │           │       └── vo
│       │           └── web
│       │               ├── controller
│       │               ├── domain
│       │               │   └── vo
│       │               ├── listener
│       │               └── service
│       │                   └── impl
│       ├── generated-sources
│       │   └── annotations
│       │       ├── io
│       │       │   └── github
│       │       │       └── linpeilie
│       │       └── org
│       │           └── dromara
│       │               ├── system
│       │               │   └── domain
│       │               │       └── vo
│       │               └── web
│       │                   └── domain
│       │                       └── vo
│       ├── generated-test-sources
│       │   └── test-annotations
│       ├── jib-cache
│       │   ├── layers
│       │   │   ├── 03bfaa43b9723aa66c7cefa65c2ae62b035bc2fa381340d5bd7ad0f54db88606
│       │   │   ├── 100a6fb460fa21485b940102ab5cfde674bf8ef81b699435c7881199247ccd29
│       │   │   ├── 5309875bddb99236c136377d1d8b4badeb7dba352576d203938ec700283d1339
│       │   │   ├── c1665b7871a9945ecc93345c4a888326f43b70769025f5b6656e370c7c24b1e6
│       │   │   └── e539e9e55997e4423d0e9701759246023bd461798319158760348ec05bae4e9e
│       │   ├── selectors
│       │   └── tmp
│       ├── maven-archiver
│       ├── maven-status
│       │   └── maven-compiler-plugin
│       │       ├── compile
│       │       │   └── default-compile
│       │       └── testCompile
│       │           └── default-testCompile
│       └── test-classes
│           └── org
│               └── dromara
│                   └── test
├── aier-app
│   ├── src
│   │   ├── main
│   │   │   ├── java
│   │   │   │   └── com
│   │   │   │       └── macro
│   │   │   │           └── aier
│   │   │   │               └── app
│   │   │   └── resources
│   │   └── test
│   │       └── java
│   │           └── com
│   │               └── macro
│   │                   └── aier
│   │                       └── app
│   └── target
│       ├── classes
│       │   └── com
│       │       └── macro
│       │           └── aier
│       │               └── app
│       ├── generated-sources
│       │   └── annotations
│       ├── generated-test-sources
│       │   └── test-annotations
│       ├── jib-cache
│       │   ├── layers
│       │   │   ├── 03b6eea0e77fee5792bdd68d87d882d79a6006b66cae77dcf006f94c1b707c37
│       │   │   ├── 491bf2fe3fd2ec4603fb1c17c679f211bbfa349874b4c3aac4858563972b3967
│       │   │   ├── ab5556c9dfd434d40f3510113198618d8587756cb097da0d900309ef7256288e
│       │   │   └── f0badbeb8e325f9335683555572831822fe90689c63696c9101c2c960585c04b
│       │   ├── selectors
│       │   └── tmp
│       ├── maven-archiver
│       ├── maven-status
│       │   └── maven-compiler-plugin
│       │       ├── compile
│       │       │   └── default-compile
│       │       └── testCompile
│       │           └── default-testCompile
│       └── test-classes
│           └── com
│               └── macro
│                   └── aier
│                       └── app
├── aier-common
│   ├── aier-common-bom
│   ├── aier-common-core
│   │   ├── src
│   │   │   └── main
│   │   │       ├── java
│   │   │       │   └── org
│   │   │       │       └── dromara
│   │   │       │           └── common
│   │   │       │               └── core
│   │   │       │                   ├── config
│   │   │       │                   │   └── properties
│   │   │       │                   ├── constant
│   │   │       │                   ├── domain
│   │   │       │                   │   ├── dto
│   │   │       │                   │   └── model
│   │   │       │                   ├── enums
│   │   │       │                   ├── exception
│   │   │       │                   │   ├── base
│   │   │       │                   │   ├── file
│   │   │       │                   │   └── user
│   │   │       │                   ├── factory
│   │   │       │                   ├── service
│   │   │       │                   ├── utils
│   │   │       │                   │   ├── file
│   │   │       │                   │   ├── ip
│   │   │       │                   │   ├── reflect
│   │   │       │                   │   └── sql
│   │   │       │                   ├── validate
│   │   │       │                   └── xss
│   │   │       └── resources
│   │   │           └── META-INF
│   │   │               └── spring
│   │   └── target
│   │       ├── classes
│   │       │   ├── META-INF
│   │       │   │   └── spring
│   │       │   └── org
│   │       │       └── dromara
│   │       │           └── common
│   │       │               └── core
│   │       │                   ├── config
│   │       │                   │   └── properties
│   │       │                   ├── constant
│   │       │                   ├── domain
│   │       │                   │   ├── dto
│   │       │                   │   └── model
│   │       │                   ├── enums
│   │       │                   ├── exception
│   │       │                   │   ├── base
│   │       │                   │   ├── file
│   │       │                   │   └── user
│   │       │                   ├── factory
│   │       │                   ├── service
│   │       │                   ├── utils
│   │       │                   │   ├── file
│   │       │                   │   ├── ip
│   │       │                   │   ├── reflect
│   │       │                   │   └── sql
│   │       │                   ├── validate
│   │       │                   └── xss
│   │       ├── generated-sources
│   │       │   └── annotations
│   │       ├── maven-archiver
│   │       └── maven-status
│   │           └── maven-compiler-plugin
│   │               └── compile
│   │                   └── default-compile
│   ├── aier-common-doc
│   │   ├── src
│   │   │   └── main
│   │   │       ├── java
│   │   │       │   └── org
│   │   │       │       └── dromara
│   │   │       │           └── common
│   │   │       │               └── doc
│   │   │       │                   ├── config
│   │   │       │                   │   └── properties
│   │   │       │                   └── handler
│   │   │       └── resources
│   │   │           └── META-INF
│   │   │               └── spring
│   │   └── target
│   │       ├── classes
│   │       │   ├── META-INF
│   │       │   │   └── spring
│   │       │   └── org
│   │       │       └── dromara
│   │       │           └── common
│   │       │               └── doc
│   │       │                   ├── config
│   │       │                   │   └── properties
│   │       │                   └── handler
│   │       ├── generated-sources
│   │       │   └── annotations
│   │       ├── maven-archiver
│   │       └── maven-status
│   │           └── maven-compiler-plugin
│   │               └── compile
│   │                   └── default-compile
│   ├── aier-common-encrypt
│   │   ├── src
│   │   │   └── main
│   │   │       ├── java
│   │   │       │   └── org
│   │   │       │       └── dromara
│   │   │       │           └── common
│   │   │       │               └── encrypt
│   │   │       │                   ├── annotation
│   │   │       │                   ├── config
│   │   │       │                   ├── core
│   │   │       │                   │   └── encryptor
│   │   │       │                   ├── enumd
│   │   │       │                   ├── filter
│   │   │       │                   ├── interceptor
│   │   │       │                   ├── properties
│   │   │       │                   └── utils
│   │   │       └── resources
│   │   │           └── META-INF
│   │   │               └── spring
│   │   └── target
│   │       ├── classes
│   │       │   ├── META-INF
│   │       │   │   └── spring
│   │       │   └── org
│   │       │       └── dromara
│   │       │           └── common
│   │       │               └── encrypt
│   │       │                   ├── annotation
│   │       │                   ├── config
│   │       │                   ├── core
│   │       │                   │   └── encryptor
│   │       │                   ├── enumd
│   │       │                   ├── filter
│   │       │                   ├── interceptor
│   │       │                   ├── properties
│   │       │                   └── utils
│   │       ├── generated-sources
│   │       │   └── annotations
│   │       ├── maven-archiver
│   │       └── maven-status
│   │           └── maven-compiler-plugin
│   │               └── compile
│   │                   └── default-compile
│   ├── aier-common-excel
│   │   ├── src
│   │   │   └── main
│   │   │       └── java
│   │   │           └── org
│   │   │               └── dromara
│   │   │                   └── common
│   │   │                       └── excel
│   │   │                           ├── annotation
│   │   │                           ├── convert
│   │   │                           ├── core
│   │   │                           └── utils
│   │   └── target
│   │       ├── classes
│   │       │   └── org
│   │       │       └── dromara
│   │       │           └── common
│   │       │               └── excel
│   │       │                   ├── annotation
│   │       │                   ├── convert
│   │       │                   ├── core
│   │       │                   └── utils
│   │       ├── generated-sources
│   │       │   └── annotations
│   │       ├── maven-archiver
│   │       └── maven-status
│   │           └── maven-compiler-plugin
│   │               └── compile
│   │                   └── default-compile
│   ├── aier-common-idempotent
│   │   ├── src
│   │   │   └── main
│   │   │       ├── java
│   │   │       │   └── org
│   │   │       │       └── dromara
│   │   │       │           └── common
│   │   │       │               └── idempotent
│   │   │       │                   ├── annotation
│   │   │       │                   ├── aspectj
│   │   │       │                   └── config
│   │   │       └── resources
│   │   │           └── META-INF
│   │   │               └── spring
│   │   └── target
│   │       ├── classes
│   │       │   ├── META-INF
│   │       │   │   └── spring
│   │       │   └── org
│   │       │       └── dromara
│   │       │           └── common
│   │       │               └── idempotent
│   │       │                   ├── annotation
│   │       │                   ├── aspectj
│   │       │                   └── config
│   │       ├── generated-sources
│   │       │   └── annotations
│   │       ├── maven-archiver
│   │       └── maven-status
│   │           └── maven-compiler-plugin
│   │               └── compile
│   │                   └── default-compile
│   ├── aier-common-json
│   │   ├── src
│   │   │   └── main
│   │   │       ├── java
│   │   │       │   └── org
│   │   │       │       └── dromara
│   │   │       │           └── common
│   │   │       │               └── json
│   │   │       │                   ├── config
│   │   │       │                   ├── handler
│   │   │       │                   └── utils
│   │   │       └── resources
│   │   │           └── META-INF
│   │   │               └── spring
│   │   └── target
│   │       ├── classes
│   │       │   ├── META-INF
│   │       │   │   └── spring
│   │       │   └── org
│   │       │       └── dromara
│   │       │           └── common
│   │       │               └── json
│   │       │                   ├── config
│   │       │                   ├── handler
│   │       │                   └── utils
│   │       ├── generated-sources
│   │       │   └── annotations
│   │       ├── maven-archiver
│   │       └── maven-status
│   │           └── maven-compiler-plugin
│   │               └── compile
│   │                   └── default-compile
│   ├── aier-common-log
│   │   ├── src
│   │   │   └── main
│   │   │       ├── java
│   │   │       │   └── org
│   │   │       │       └── dromara
│   │   │       │           └── common
│   │   │       │               └── log
│   │   │       │                   ├── annotation
│   │   │       │                   ├── aspect
│   │   │       │                   ├── enums
│   │   │       │                   └── event
│   │   │       └── resources
│   │   │           └── META-INF
│   │   │               └── spring
│   │   └── target
│   │       ├── classes
│   │       │   ├── META-INF
│   │       │   │   └── spring
│   │       │   └── org
│   │       │       └── dromara
│   │       │           └── common
│   │       │               └── log
│   │       │                   ├── annotation
│   │       │                   ├── aspect
│   │       │                   ├── enums
│   │       │                   └── event
│   │       ├── generated-sources
│   │       │   └── annotations
│   │       ├── maven-archiver
│   │       └── maven-status
│   │           └── maven-compiler-plugin
│   │               └── compile
│   │                   └── default-compile
│   ├── aier-common-mail
│   │   ├── src
│   │   │   └── main
│   │   │       ├── java
│   │   │       │   └── org
│   │   │       │       └── dromara
│   │   │       │           └── common
│   │   │       │               └── mail
│   │   │       │                   ├── config
│   │   │       │                   │   └── properties
│   │   │       │                   └── utils
│   │   │       └── resources
│   │   │           └── META-INF
│   │   │               └── spring
│   │   └── target
│   │       ├── classes
│   │       │   ├── META-INF
│   │       │   │   └── spring
│   │       │   └── org
│   │       │       └── dromara
│   │       │           └── common
│   │       │               └── mail
│   │       │                   ├── config
│   │       │                   │   └── properties
│   │       │                   └── utils
│   │       ├── generated-sources
│   │       │   └── annotations
│   │       ├── maven-archiver
│   │       └── maven-status
│   │           └── maven-compiler-plugin
│   │               └── compile
│   │                   └── default-compile
│   ├── aier-common-mybatis
│   │   ├── src
│   │   │   └── main
│   │   │       ├── java
│   │   │       │   └── org
│   │   │       │       └── dromara
│   │   │       │           └── common
│   │   │       │               └── mybatis
│   │   │       │                   ├── annotation
│   │   │       │                   ├── config
│   │   │       │                   ├── core
│   │   │       │                   │   ├── domain
│   │   │       │                   │   ├── mapper
│   │   │       │                   │   └── page
│   │   │       │                   ├── enums
│   │   │       │                   ├── handler
│   │   │       │                   │   └── type
│   │   │       │                   ├── helper
│   │   │       │                   └── interceptor
│   │   │       └── resources
│   │   │           └── META-INF
│   │   │               └── spring
│   │   └── target
│   │       ├── classes
│   │       │   ├── META-INF
│   │       │   │   └── spring
│   │       │   └── org
│   │       │       └── dromara
│   │       │           └── common
│   │       │               └── mybatis
│   │       │                   ├── annotation
│   │       │                   ├── config
│   │       │                   ├── core
│   │       │                   │   ├── domain
│   │       │                   │   ├── mapper
│   │       │                   │   └── page
│   │       │                   ├── enums
│   │       │                   ├── handler
│   │       │                   │   └── type
│   │       │                   ├── helper
│   │       │                   └── interceptor
│   │       ├── generated-sources
│   │       │   └── annotations
│   │       ├── maven-archiver
│   │       └── maven-status
│   │           └── maven-compiler-plugin
│   │               └── compile
│   │                   └── default-compile
│   ├── aier-common-oss
│   │   ├── src
│   │   │   └── main
│   │   │       └── java
│   │   │           └── org
│   │   │               └── dromara
│   │   │                   └── common
│   │   │                       └── oss
│   │   │                           ├── constant
│   │   │                           ├── core
│   │   │                           ├── entity
│   │   │                           ├── enumd
│   │   │                           ├── exception
│   │   │                           ├── factory
│   │   │                           └── properties
│   │   └── target
│   │       ├── classes
│   │       │   └── org
│   │       │       └── dromara
│   │       │           └── common
│   │       │               └── oss
│   │       │                   ├── constant
│   │       │                   ├── core
│   │       │                   ├── entity
│   │       │                   ├── enumd
│   │       │                   ├── exception
│   │       │                   ├── factory
│   │       │                   └── properties
│   │       ├── generated-sources
│   │       │   └── annotations
│   │       ├── maven-archiver
│   │       └── maven-status
│   │           └── maven-compiler-plugin
│   │               └── compile
│   │                   └── default-compile
│   ├── aier-common-ratelimiter
│   │   ├── src
│   │   │   └── main
│   │   │       ├── java
│   │   │       │   └── org
│   │   │       │       └── dromara
│   │   │       │           └── common
│   │   │       │               └── ratelimiter
│   │   │       │                   ├── annotation
│   │   │       │                   ├── aspectj
│   │   │       │                   ├── config
│   │   │       │                   └── enums
│   │   │       └── resources
│   │   │           └── META-INF
│   │   │               └── spring
│   │   └── target
│   │       ├── classes
│   │       │   ├── META-INF
│   │       │   │   └── spring
│   │       │   └── org
│   │       │       └── dromara
│   │       │           └── common
│   │       │               └── ratelimiter
│   │       │                   ├── annotation
│   │       │                   ├── aspectj
│   │       │                   ├── config
│   │       │                   └── enums
│   │       ├── generated-sources
│   │       │   └── annotations
│   │       ├── maven-archiver
│   │       └── maven-status
│   │           └── maven-compiler-plugin
│   │               └── compile
│   │                   └── default-compile
│   ├── aier-common-redis
│   │   ├── src
│   │   │   └── main
│   │   │       ├── java
│   │   │       │   └── org
│   │   │       │       └── dromara
│   │   │       │           └── common
│   │   │       │               └── redis
│   │   │       │                   ├── config
│   │   │       │                   │   └── properties
│   │   │       │                   ├── handler
│   │   │       │                   ├── manager
│   │   │       │                   └── utils
│   │   │       └── resources
│   │   │           └── META-INF
│   │   │               └── spring
│   │   └── target
│   │       ├── classes
│   │       │   ├── META-INF
│   │       │   │   └── spring
│   │       │   └── org
│   │       │       └── dromara
│   │       │           └── common
│   │       │               └── redis
│   │       │                   ├── config
│   │       │                   │   └── properties
│   │       │                   ├── handler
│   │       │                   ├── manager
│   │       │                   └── utils
│   │       ├── generated-sources
│   │       │   └── annotations
│   │       ├── maven-archiver
│   │       └── maven-status
│   │           └── maven-compiler-plugin
│   │               └── compile
│   │                   └── default-compile
│   ├── aier-common-satoken
│   │   ├── src
│   │   │   └── main
│   │   │       ├── java
│   │   │       │   └── org
│   │   │       │       └── dromara
│   │   │       │           └── common
│   │   │       │               └── satoken
│   │   │       │                   ├── config
│   │   │       │                   ├── core
│   │   │       │                   │   ├── dao
│   │   │       │                   │   └── service
│   │   │       │                   └── utils
│   │   │       └── resources
│   │   │           └── META-INF
│   │   │               └── spring
│   │   └── target
│   │       ├── classes
│   │       │   ├── META-INF
│   │       │   │   └── spring
│   │       │   └── org
│   │       │       └── dromara
│   │       │           └── common
│   │       │               └── satoken
│   │       │                   ├── config
│   │       │                   ├── core
│   │       │                   │   ├── dao
│   │       │                   │   └── service
│   │       │                   └── utils
│   │       ├── generated-sources
│   │       │   └── annotations
│   │       ├── maven-archiver
│   │       └── maven-status
│   │           └── maven-compiler-plugin
│   │               └── compile
│   │                   └── default-compile
│   ├── aier-common-security
│   │   ├── src
│   │   │   └── main
│   │   │       ├── java
│   │   │       │   └── org
│   │   │       │       └── dromara
│   │   │       │           └── common
│   │   │       │               └── security
│   │   │       │                   ├── config
│   │   │       │                   │   └── properties
│   │   │       │                   └── handler
│   │   │       └── resources
│   │   │           └── META-INF
│   │   │               └── spring
│   │   └── target
│   │       ├── classes
│   │       │   ├── META-INF
│   │       │   │   └── spring
│   │       │   └── org
│   │       │       └── dromara
│   │       │           └── common
│   │       │               └── security
│   │       │                   ├── config
│   │       │                   │   └── properties
│   │       │                   └── handler
│   │       ├── generated-sources
│   │       │   └── annotations
│   │       ├── maven-archiver
│   │       └── maven-status
│   │           └── maven-compiler-plugin
│   │               └── compile
│   │                   └── default-compile
│   ├── aier-common-sensitive
│   │   ├── src
│   │   │   └── main
│   │   │       └── java
│   │   │           └── org
│   │   │               └── dromara
│   │   │                   └── common
│   │   │                       └── sensitive
│   │   │                           ├── annotation
│   │   │                           ├── core
│   │   │                           └── handler
│   │   └── target
│   │       ├── classes
│   │       │   └── org
│   │       │       └── dromara
│   │       │           └── common
│   │       │               └── sensitive
│   │       │                   ├── annotation
│   │       │                   ├── core
│   │       │                   └── handler
│   │       ├── generated-sources
│   │       │   └── annotations
│   │       ├── maven-archiver
│   │       └── maven-status
│   │           └── maven-compiler-plugin
│   │               └── compile
│   │                   └── default-compile
│   ├── aier-common-sms
│   │   ├── src
│   │   │   └── main
│   │   │       ├── java
│   │   │       │   └── org
│   │   │       │       └── dromara
│   │   │       │           └── common
│   │   │       │               └── sms
│   │   │       │                   └── config
│   │   │       └── resources
│   │   │           └── META-INF
│   │   │               └── spring
│   │   └── target
│   │       ├── classes
│   │       │   ├── META-INF
│   │       │   │   └── spring
│   │       │   └── org
│   │       │       └── dromara
│   │       │           └── common
│   │       │               └── sms
│   │       │                   └── config
│   │       ├── generated-sources
│   │       │   └── annotations
│   │       ├── maven-archiver
│   │       └── maven-status
│   │           └── maven-compiler-plugin
│   │               └── compile
│   │                   └── default-compile
│   ├── aier-common-social
│   │   ├── src
│   │   │   └── main
│   │   │       ├── java
│   │   │       │   └── org
│   │   │       │       └── dromara
│   │   │       │           └── common
│   │   │       │               └── social
│   │   │       │                   ├── config
│   │   │       │                   │   └── properties
│   │   │       │                   ├── maxkey
│   │   │       │                   └── utils
│   │   │       └── resources
│   │   │           └── META-INF
│   │   │               └── spring
│   │   └── target
│   │       ├── classes
│   │       │   ├── META-INF
│   │       │   │   └── spring
│   │       │   └── org
│   │       │       └── dromara
│   │       │           └── common
│   │       │               └── social
│   │       │                   ├── config
│   │       │                   │   └── properties
│   │       │                   ├── maxkey
│   │       │                   └── utils
│   │       ├── generated-sources
│   │       │   └── annotations
│   │       ├── maven-archiver
│   │       └── maven-status
│   │           └── maven-compiler-plugin
│   │               └── compile
│   │                   └── default-compile
│   ├── aier-common-tenant
│   │   ├── src
│   │   │   └── main
│   │   │       ├── java
│   │   │       │   └── org
│   │   │       │       └── dromara
│   │   │       │           └── common
│   │   │       │               └── tenant
│   │   │       │                   ├── config
│   │   │       │                   ├── core
│   │   │       │                   ├── exception
│   │   │       │                   ├── handle
│   │   │       │                   ├── helper
│   │   │       │                   ├── manager
│   │   │       │                   └── properties
│   │   │       └── resources
│   │   │           └── META-INF
│   │   │               └── spring
│   │   └── target
│   │       ├── classes
│   │       │   ├── META-INF
│   │       │   │   └── spring
│   │       │   └── org
│   │       │       └── dromara
│   │       │           └── common
│   │       │               └── tenant
│   │       │                   ├── config
│   │       │                   ├── core
│   │       │                   ├── exception
│   │       │                   ├── handle
│   │       │                   ├── helper
│   │       │                   ├── manager
│   │       │                   └── properties
│   │       ├── generated-sources
│   │       │   └── annotations
│   │       ├── maven-archiver
│   │       └── maven-status
│   │           └── maven-compiler-plugin
│   │               └── compile
│   │                   └── default-compile
│   ├── aier-common-translation
│   │   ├── src
│   │   │   └── main
│   │   │       ├── java
│   │   │       │   └── org
│   │   │       │       └── dromara
│   │   │       │           └── common
│   │   │       │               └── translation
│   │   │       │                   ├── annotation
│   │   │       │                   ├── config
│   │   │       │                   ├── constant
│   │   │       │                   └── core
│   │   │       │                       ├── handler
│   │   │       │                       └── impl
│   │   │       └── resources
│   │   │           └── META-INF
│   │   │               └── spring
│   │   └── target
│   │       ├── classes
│   │       │   ├── META-INF
│   │       │   │   └── spring
│   │       │   └── org
│   │       │       └── dromara
│   │       │           └── common
│   │       │               └── translation
│   │       │                   ├── annotation
│   │       │                   ├── config
│   │       │                   ├── constant
│   │       │                   └── core
│   │       │                       ├── handler
│   │       │                       └── impl
│   │       ├── generated-sources
│   │       │   └── annotations
│   │       ├── maven-archiver
│   │       └── maven-status
│   │           └── maven-compiler-plugin
│   │               └── compile
│   │                   └── default-compile
│   ├── aier-common-web
│   │   ├── src
│   │   │   └── main
│   │   │       ├── java
│   │   │       │   └── org
│   │   │       │       └── dromara
│   │   │       │           └── common
│   │   │       │               └── web
│   │   │       │                   ├── annotation
│   │   │       │                   ├── config
│   │   │       │                   │   └── properties
│   │   │       │                   ├── constant
│   │   │       │                   ├── core
│   │   │       │                   ├── enums
│   │   │       │                   ├── filter
│   │   │       │                   ├── interceptor
│   │   │       │                   ├── strict
│   │   │       │                   └── utils
│   │   │       └── resources
│   │   │           └── META-INF
│   │   │               └── spring
│   │   └── target
│   │       ├── classes
│   │       │   ├── META-INF
│   │       │   │   └── spring
│   │       │   └── org
│   │       │       └── dromara
│   │       │           └── common
│   │       │               └── web
│   │       │                   ├── annotation
│   │       │                   ├── config
│   │       │                   │   └── properties
│   │       │                   ├── constant
│   │       │                   ├── core
│   │       │                   ├── enums
│   │       │                   ├── filter
│   │       │                   ├── interceptor
│   │       │                   ├── strict
│   │       │                   └── utils
│   │       ├── generated-sources
│   │       │   └── annotations
│   │       ├── maven-archiver
│   │       └── maven-status
│   │           └── maven-compiler-plugin
│   │               └── compile
│   │                   └── default-compile
│   └── aier-common-websocket
│       ├── src
│       │   └── main
│       │       ├── java
│       │       │   └── org
│       │       │       └── dromara
│       │       │           └── common
│       │       │               └── websocket
│       │       │                   ├── config
│       │       │                   │   └── properties
│       │       │                   ├── constant
│       │       │                   ├── dto
│       │       │                   ├── handler
│       │       │                   ├── holder
│       │       │                   ├── interceptor
│       │       │                   ├── listener
│       │       │                   └── utils
│       │       └── resources
│       │           └── META-INF
│       │               └── spring
│       └── target
│           ├── classes
│           │   ├── META-INF
│           │   │   └── spring
│           │   └── org
│           │       └── dromara
│           │           └── common
│           │               └── websocket
│           │                   ├── config
│           │                   │   └── properties
│           │                   ├── constant
│           │                   ├── dto
│           │                   ├── handler
│           │                   ├── holder
│           │                   ├── interceptor
│           │                   ├── listener
│           │                   └── utils
│           ├── generated-sources
│           │   └── annotations
│           ├── maven-archiver
│           └── maven-status
│               └── maven-compiler-plugin
│                   └── compile
│                       └── default-compile
├── aier-extend
│   └── aier-monitor-admin
│       ├── src
│       │   └── main
│       │       ├── java
│       │       │   └── org
│       │       │       └── dromara
│       │       │           └── monitor
│       │       │               └── admin
│       │       │                   ├── config
│       │       │                   └── notifier
│       │       └── resources
│       └── target
│           ├── classes
│           │   └── org
│           │       └── dromara
│           │           └── monitor
│           │               └── admin
│           │                   ├── config
│           │                   └── notifier
│           ├── generated-sources
│           │   └── annotations
│           ├── maven-archiver
│           └── maven-status
│               └── maven-compiler-plugin
│                   └── compile
│                       └── default-compile
├── aier-h5edit
│   ├── src
│   │   └── main
│   │       ├── java
│   │       │   └── org
│   │       │       └── dromara
│   │       │           └── h5edit
│   │       │               ├── component
│   │       │               │   └── action
│   │       │               ├── config
│   │       │               ├── controller
│   │       │               ├── dto
│   │       │               ├── entity
│   │       │               ├── qo
│   │       │               ├── repository
│   │       │               ├── runner
│   │       │               ├── service
│   │       │               │   └── impl
│   │       │               └── vo
│   │       └── resources
│   │           └── repository
│   └── target
│       ├── classes
│       │   ├── org
│       │   │   └── dromara
│       │   │       └── h5edit
│       │   │           ├── component
│       │   │           │   └── action
│       │   │           ├── config
│       │   │           ├── controller
│       │   │           ├── dto
│       │   │           ├── entity
│       │   │           ├── qo
│       │   │           ├── repository
│       │   │           ├── runner
│       │   │           ├── service
│       │   │           │   └── impl
│       │   │           └── vo
│       │   └── repository
│       ├── generated-sources
│       │   └── annotations
│       ├── jib-cache
│       │   ├── layers
│       │   │   ├── 09888aa1586c8bca3d87706df60fb059c87a92322f58bd67041c30e0db476be3
│       │   │   ├── 4c8b7b7483a07a224fde672d483e76d5c2901ddc0fe1a5a16d02db9b1a11fdc5
│       │   │   ├── 4ef818c6c64c7f72f4a0b4dfd844c2dca7d65732b01021fe9c23f9b27a0dd178
│       │   │   ├── e09498e37a67d0c603c9e8193e765a1488fe905187b9b381525303b211ddfdcf
│       │   │   └── fe6de37e98b0d5bbe3cc3814f1935bb6be3ec6a96e61b2e0d3ec65e83b5e4b9e
│       │   ├── selectors
│       │   └── tmp
│       ├── maven-archiver
│       └── maven-status
│           └── maven-compiler-plugin
│               └── compile
│                   └── default-compile
├── aier-modules
│   ├── aier-biz-platform
│   │   ├── src
│   │   │   └── main
│   │   │       ├── java
│   │   │       │   └── org
│   │   │       │       └── dromara
│   │   │       │           └── biz
│   │   │       │               ├── buildr
│   │   │       │               │   ├── ma
│   │   │       │               │   └── mp
│   │   │       │               ├── component
│   │   │       │               │   ├── event
│   │   │       │               │   └── handler
│   │   │       │               │       └── mp
│   │   │       │               ├── config
│   │   │       │               │   └── aes
│   │   │       │               ├── controller
│   │   │       │               │   ├── mp
│   │   │       │               │   ├── pc
│   │   │       │               │   └── wework
│   │   │       │               ├── domain
│   │   │       │               │   ├── base
│   │   │       │               │   ├── bo
│   │   │       │               │   │   ├── customer
│   │   │       │               │   │   ├── diary
│   │   │       │               │   │   ├── employee
│   │   │       │               │   │   ├── feedPost
│   │   │       │               │   │   ├── invitation
│   │   │       │               │   │   ├── merchant
│   │   │       │               │   │   ├── message
│   │   │       │               │   │   ├── staff
│   │   │       │               │   │   └── wechat
│   │   │       │               │   ├── cs
│   │   │       │               │   ├── event
│   │   │       │               │   ├── params
│   │   │       │               │   │   └── customer
│   │   │       │               │   ├── query
│   │   │       │               │   │   ├── card
│   │   │       │               │   │   ├── club
│   │   │       │               │   │   ├── customer
│   │   │       │               │   │   ├── diary
│   │   │       │               │   │   ├── employee
│   │   │       │               │   │   ├── invitation
│   │   │       │               │   │   ├── merchant
│   │   │       │               │   │   ├── node
│   │   │       │               │   │   └── room
│   │   │       │               │   ├── vo
│   │   │       │               │   │   ├── auth
│   │   │       │               │   │   ├── card
│   │   │       │               │   │   ├── club
│   │   │       │               │   │   ├── customer
│   │   │       │               │   │   ├── employee
│   │   │       │               │   │   ├── feedpost
│   │   │       │               │   │   ├── invitation
│   │   │       │               │   │   ├── nurse
│   │   │       │               │   │   └── room
│   │   │       │               │   └── wework
│   │   │       │               ├── dto
│   │   │       │               │   ├── cs
│   │   │       │               │   └── customerservice
│   │   │       │               ├── handler
│   │   │       │               │   ├── exception
│   │   │       │               │   └── mp
│   │   │       │               ├── listener
│   │   │       │               ├── mapper
│   │   │       │               │   └── cs
│   │   │       │               ├── service
│   │   │       │               │   └── impl
│   │   │       │               └── task
│   │   │       └── resources
│   │   │           └── mapper
│   │   │               └── biz
│   │   └── target
│   │       ├── classes
│   │       │   ├── META-INF
│   │       │   │   └── mps
│   │       │   ├── io
│   │       │   │   └── github
│   │       │   │       └── linpeilie
│   │       │   ├── mapper
│   │       │   │   └── biz
│   │       │   └── org
│   │       │       └── dromara
│   │       │           └── biz
│   │       │               ├── buildr
│   │       │               │   ├── ma
│   │       │               │   └── mp
│   │       │               ├── component
│   │       │               │   ├── event
│   │       │               │   └── handler
│   │       │               │       └── mp
│   │       │               ├── config
│   │       │               │   └── aes
│   │       │               ├── controller
│   │       │               │   ├── mp
│   │       │               │   ├── pc
│   │       │               │   └── wework
│   │       │               ├── domain
│   │       │               │   ├── base
│   │       │               │   ├── bo
│   │       │               │   │   ├── customer
│   │       │               │   │   ├── diary
│   │       │               │   │   ├── employee
│   │       │               │   │   ├── feedPost
│   │       │               │   │   ├── merchant
│   │       │               │   │   ├── message
│   │       │               │   │   ├── staff
│   │       │               │   │   └── wechat
│   │       │               │   ├── event
│   │       │               │   ├── params
│   │       │               │   │   └── customer
│   │       │               │   ├── query
│   │       │               │   │   ├── card
│   │       │               │   │   ├── club
│   │       │               │   │   ├── customer
│   │       │               │   │   ├── diary
│   │       │               │   │   ├── employee
│   │       │               │   │   ├── invitation
│   │       │               │   │   ├── merchant
│   │       │               │   │   ├── node
│   │       │               │   │   └── room
│   │       │               │   ├── vo
│   │       │               │   │   ├── auth
│   │       │               │   │   ├── card
│   │       │               │   │   ├── club
│   │       │               │   │   ├── customer
│   │       │               │   │   ├── employee
│   │       │               │   │   ├── feedpost
│   │       │               │   │   ├── invitation
│   │       │               │   │   ├── nurse
│   │       │               │   │   └── room
│   │       │               │   └── wework
│   │       │               ├── dto
│   │       │               │   └── customerservice
│   │       │               ├── listener
│   │       │               ├── mapper
│   │       │               ├── service
│   │       │               │   └── impl
│   │       │               └── task
│   │       ├── generated-sources
│   │       │   └── annotations
│   │       │       ├── io
│   │       │       │   └── github
│   │       │       │       └── linpeilie
│   │       │       └── org
│   │       │           └── dromara
│   │       │               └── biz
│   │       │                   └── domain
│   │       │                       ├── bo
│   │       │                       │   ├── customer
│   │       │                       │   ├── diary
│   │       │                       │   ├── employee
│   │       │                       │   └── merchant
│   │       │                       └── vo
│   │       │                           ├── club
│   │       │                           ├── customer
│   │       │                           ├── invitation
│   │       │                           ├── nurse
│   │       │                           └── room
│   │       ├── maven-archiver
│   │       └── maven-status
│   │           └── maven-compiler-plugin
│   │               └── compile
│   │                   └── default-compile
│   ├── aier-customer-service
│   │   ├── src
│   │   │   ├── main
│   │   │   │   ├── java
│   │   │   │   │   └── org
│   │   │   │   │       └── dromara
│   │   │   │   │           └── cs
│   │   │   │   │               ├── component
│   │   │   │   │               ├── config
│   │   │   │   │               ├── controller
│   │   │   │   │               ├── dto
│   │   │   │   │               ├── entity
│   │   │   │   │               ├── mapper
│   │   │   │   │               ├── service
│   │   │   │   │               │   └── impl
│   │   │   │   │               └── websocket
│   │   │   │   └── resources
│   │   │   │       └── mapper
│   │   │   └── test
│   │   │       └── java
│   │   └── target
│   │       ├── classes
│   │       │   ├── mapper
│   │       │   └── org
│   │       │       └── dromara
│   │       │           └── cs
│   │       │               ├── config
│   │       │               ├── controller
│   │       │               ├── dto
│   │       │               ├── entity
│   │       │               ├── mapper
│   │       │               ├── service
│   │       │               │   └── impl
│   │       │               └── websocket
│   │       ├── generated-sources
│   │       │   └── annotations
│   │       ├── generated-test-sources
│   │       │   └── test-annotations
│   │       ├── maven-archiver
│   │       ├── maven-status
│   │       │   └── maven-compiler-plugin
│   │       │       ├── compile
│   │       │       │   └── default-compile
│   │       │       └── testCompile
│   │       │           └── default-testCompile
│   │       └── test-classes
│   └── aier-system
│       ├── src
│       │   └── main
│       │       ├── java
│       │       │   └── org
│       │       │       └── dromara
│       │       │           └── system
│       │       │               ├── controller
│       │       │               │   ├── monitor
│       │       │               │   └── system
│       │       │               ├── domain
│       │       │               │   ├── bo
│       │       │               │   └── vo
│       │       │               ├── listener
│       │       │               ├── mapper
│       │       │               ├── runner
│       │       │               └── service
│       │       │                   └── impl
│       │       └── resources
│       │           └── mapper
│       │               └── system
│       └── target
│           ├── classes
│           │   ├── META-INF
│           │   │   └── mps
│           │   ├── io
│           │   │   └── github
│           │   │       └── linpeilie
│           │   ├── mapper
│           │   │   └── system
│           │   └── org
│           │       └── dromara
│           │           ├── common
│           │           │   └── log
│           │           │       └── event
│           │           └── system
│           │               ├── controller
│           │               │   ├── monitor
│           │               │   └── system
│           │               ├── domain
│           │               │   ├── bo
│           │               │   └── vo
│           │               ├── listener
│           │               ├── mapper
│           │               ├── runner
│           │               └── service
│           │                   └── impl
│           ├── generated-sources
│           │   └── annotations
│           │       ├── io
│           │       │   └── github
│           │       │       └── linpeilie
│           │       └── org
│           │           └── dromara
│           │               ├── common
│           │               │   └── log
│           │               │       └── event
│           │               └── system
│           │                   └── domain
│           │                       ├── bo
│           │                       └── vo
│           ├── maven-archiver
│           └── maven-status
│               └── maven-compiler-plugin
│                   └── compile
│                       └── default-compile
├── logs
└── script
    ├── docker
    │   ├── nginx
    │   │   └── conf
    │   └── redis
    │       ├── conf
    │       └── data
    └── sql

1271 directories
