package org.dromara.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.biz.domain.Employee;
import org.dromara.biz.domain.Rooms;
import org.dromara.biz.domain.vo.employee.EmployeeVO;

import java.util.List;

/**
 * <p>
 * 用户人员表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
public interface BizEmployeeMapper extends BaseMapper<Employee> {

    Page<EmployeeVO> selectCustomPage(Page<Employee> page,
                                      @Param(Constants.WRAPPER) QueryWrapper<Employee> wrapper);

    EmployeeVO detail(@Param(Constants.WRAPPER) QueryWrapper<Employee> lqw);

    List<EmployeeVO> selectCustomList(@Param(Constants.WRAPPER) QueryWrapper<Employee> qw);

    /**
     * 获取员工绑定房间
     */
    List<Rooms> getEmployeeRoomsByUserId(Long userId);
}
