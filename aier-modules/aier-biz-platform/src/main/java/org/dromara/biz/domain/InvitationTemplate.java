package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 请柬模版表
 * @TableName biz_invitation_template
 */
@TableName(value ="biz_invitation_template")
@Data
public class InvitationTemplate implements Serializable {
    /**
     * id
     */
    @TableId
    private Long id;

    /**
     * 请柬地址
     */
    private String invitationUrl;

    /**
     * 请柬名称
     */
    private String invitationName;

    /**
     * 收藏人数
     */
    private Integer collectNum;

    /**
     * 请柬使用人数
     */
    private Integer invitationNum;

    /**
     * 小图地址
     */
    private String photoUrl;

    /**
     * 请柬类型 0：长图 1：翻页 2:海报
     */
    private Integer invitationType;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 分类
     */
    private String category;

    /**
     * 是否启用
     */
    private Boolean enable;

    /**
     * 图片数量
     */
    private Integer imageNum;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
