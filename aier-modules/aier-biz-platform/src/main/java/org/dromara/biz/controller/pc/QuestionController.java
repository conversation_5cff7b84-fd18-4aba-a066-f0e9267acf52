package org.dromara.biz.controller.pc;

import lombok.RequiredArgsConstructor;
import org.dromara.biz.domain.query.QuestionQuery;
import org.dromara.biz.domain.vo.QuestionVO;
import org.dromara.biz.service.QuestionsService;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 问答相关接口
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/question")
public class QuestionController {

    private final QuestionsService questionsService;

    /**
     * 新增问题
     * @param questionVO 问题信息
     * @return 新增结果
     */
    @PutMapping("/save")
    @RepeatSubmit
    public R<Boolean> save(@RequestBody @Validated QuestionVO questionVO) {
        return R.ok(questionsService.save(questionVO));
    }

    /**
     * 更改问题显示状态
     * @param questionId 问题ID
     * @param state 显示状态 0-不显示 1-显示
     * @return 更改结果
     */
    @PutMapping("/change/{state}/{questionId}")
    @RepeatSubmit
    public R<Boolean> changeVisible(@PathVariable Boolean state, @PathVariable Long questionId) {
        return R.ok(questionsService.changeVisible(questionId, state));
    }

    /**
     * 查询问题详情
     */
    @GetMapping("/info/{questionId}")
    public R<QuestionVO> info(@PathVariable Long questionId) {
        return R.ok(questionsService.getByQuestionId(questionId));
    }
    /**
     * 修改问题
     * @param questionVO 问题信息
     * @return 修改结果
     */
    @PostMapping("/update")
    @RepeatSubmit
    public R<Boolean> update(@RequestBody @Validated QuestionVO questionVO) {
        return R.ok(questionsService.update(questionVO));
    }

    /**
     * 分页查询问题列表
     */
    @GetMapping("/page")
    public TableDataInfo<QuestionVO> page(QuestionQuery query) {
        return questionsService.queryPage(query);
    }

    /**
     * 删除问题
     * @param questionId 问题id
     * @return 结果
     */
    @DeleteMapping("/{questionId}")
    public R<Boolean> remove(@PathVariable Long questionId){
        return R.ok(questionsService.removeById(questionId));
    }
}
