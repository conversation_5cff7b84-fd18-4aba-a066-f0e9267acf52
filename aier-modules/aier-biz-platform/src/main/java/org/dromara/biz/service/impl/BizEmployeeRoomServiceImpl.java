package org.dromara.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.biz.domain.BizEmployeeRoom;
import org.dromara.biz.domain.Employee;
import org.dromara.biz.mapper.BizEmployeeRoomMapper;
import org.dromara.biz.service.IBizEmployeeRoomService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 员工关联房间表（护理人员，产康等） 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-05
 */
@Service
@RequiredArgsConstructor
public class BizEmployeeRoomServiceImpl extends ServiceImpl<BizEmployeeRoomMapper, BizEmployeeRoom> implements IBizEmployeeRoomService {

    @Override
    public Boolean checkRoomByEmployeeId(Long employeeId, Long roomId, String rolePost) {
        LambdaQueryWrapper<BizEmployeeRoom> lqw = Wrappers.lambdaQuery();
        lqw.eq(BizEmployeeRoom::getRoomId, roomId);
        lqw.eq(BizEmployeeRoom::getRoleCode, rolePost);
        List<BizEmployeeRoom> bizEmployeeRooms = baseMapper.selectList(lqw);
        if (bizEmployeeRooms == null || bizEmployeeRooms.size() == 0) {
            bizEmployeeRooms = baseMapper.selectList(Wrappers.<BizEmployeeRoom>lambdaQuery().eq(BizEmployeeRoom::getEmployeeId, employeeId));
            return bizEmployeeRooms != null && bizEmployeeRooms.size() != 0;
        }
        return true;
    }

    @Override
    public List<BizEmployeeRoom> getRoomByEmployeeId(Long employeeId) {
        LambdaQueryWrapper<BizEmployeeRoom> lqw = Wrappers.lambdaQuery();
        lqw.eq(BizEmployeeRoom::getEmployeeId, employeeId);
        return baseMapper.selectList(lqw);
    }
}
