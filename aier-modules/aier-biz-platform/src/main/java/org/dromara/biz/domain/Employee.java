package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 用户人员表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("biz_employee")
public class Employee implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId
    private Long employeeId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 手机号
     */
    @Length(max = 11, min = 11, message = "手机号只能为11位")
    private String phone;

    /**
     * 照片
     */
    private String photos;

    /**
     * 入职日期
     */
    private Date hireDate;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 性别
     */
    private String gender;

    /**
     * 简介
     */
    private String description;

    /**
     * 密码
     */
    private String password;

    /**
     * 是否禁用
     */
    private Boolean disable;

    /**
     * 审核状态
     * 0 - 未审核
     * 1 - 审核通过
     * 2 - 审核未通过
     */
    private Integer auditStatus;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 宝妈小叮当小程序appid
     */
    private String appid;

    /**
     * 宝妈小叮当小程序openid
     */
    private String openid;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 职位（描述）
     */
    private String postDescription;
}
