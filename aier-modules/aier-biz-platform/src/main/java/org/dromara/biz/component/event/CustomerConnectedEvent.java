package org.dromara.biz.component.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

@Getter
public class CustomerConnectedEvent extends ApplicationEvent {
    private final String tenantId;
    private final Long customerId;

    public CustomerConnectedEvent(String tenantId, Long customerId) {
        super(customerId);
        this.tenantId = tenantId;
        this.customerId = customerId;
    }
} 