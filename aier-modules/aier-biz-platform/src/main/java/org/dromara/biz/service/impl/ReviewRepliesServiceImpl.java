package org.dromara.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.biz.domain.ReviewReplies;
import org.dromara.biz.domain.vo.ReviewRepliesVO;
import org.dromara.biz.service.ReviewRepliesService;
import org.dromara.biz.mapper.ReviewRepliesMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_review_replies(总体评价商家回复表)】的数据库操作Service实现
* @createDate 2024-03-25 14:26:20
*/
@Service
public class ReviewRepliesServiceImpl extends ServiceImpl<ReviewRepliesMapper, ReviewReplies>
    implements ReviewRepliesService{

    @Override
    public List<ReviewRepliesVO> queryListByReviewId(Long reviewId) {
        LambdaQueryWrapper<ReviewReplies> lqw = Wrappers.lambdaQuery();
        lqw.eq(ReviewReplies::getReviewId, reviewId);
        List<ReviewRepliesVO> reviewRepliesList = baseMapper.selectVoList(lqw);
        return reviewRepliesList;
    }


}




