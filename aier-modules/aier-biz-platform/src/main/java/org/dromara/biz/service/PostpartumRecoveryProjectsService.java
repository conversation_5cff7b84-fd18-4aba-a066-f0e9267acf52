package org.dromara.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.PostpartumRecoveryProjects;
import org.dromara.biz.domain.query.PostpartumRecoveryProjectQuery;
import org.dromara.biz.domain.vo.PostpartumRecoveryProjectVO;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_postpartum_recovery_projects(产后康复项目主表)】的数据库操作Service
* @createDate 2024-03-11 17:58:45
*/
public interface PostpartumRecoveryProjectsService extends IService<PostpartumRecoveryProjects> {

    /**
     * 保存产后康复项目
     * @param postpartumRecoveryProject 产后康复项目
     * @return 结果
     */
    Boolean save(PostpartumRecoveryProjectVO postpartumRecoveryProject);

    /**
     * 更新产后康复项目
     * @param postpartumRecoveryProject 产后康复项目
     * @return 结果
     */
    Boolean update(PostpartumRecoveryProjectVO postpartumRecoveryProject);

    TableDataInfo<PostpartumRecoveryProjectVO> queryPage(PostpartumRecoveryProjectQuery query);

    List<PostpartumRecoveryProjectVO> queryRecommendation(Integer limit);

    PostpartumRecoveryProjectVO queryInfo(Long projectId);
}
