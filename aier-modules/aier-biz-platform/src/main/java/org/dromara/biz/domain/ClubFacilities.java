package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.handler.type.StringListTypeHandler;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 会所设施表
 * @TableName biz_club_facilities
 */
@TableName(value ="biz_club_facilities", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class ClubFacilities extends TenantEntity implements Serializable {

    /**
     *
     */
    @TableId
    private Long facilityId;

    /**
     * 设施名称
     */
    private String facilityName;

    /**
     * 设施照片
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> facilityPhotos;

    /**
     * 视频链接
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> videos;

    /**
     * 背景照片
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> backgroundPhotos = new ArrayList<>();;


    /**
     * 设施描述
     */
    private String facilityDescription;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
