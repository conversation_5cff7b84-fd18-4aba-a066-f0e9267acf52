package org.dromara.biz.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.biz.domain.WechatRole;
import org.dromara.biz.mapper.WechatRoleMapper;
import org.dromara.biz.service.WechatRoleService;
import org.dromara.common.core.utils.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【biz_wechat_role(小程序角色表)】的数据库操作Service实现
* @createDate 2024-09-19 20:26:39
*/
@Service
public class WechatRoleServiceImpl extends ServiceImpl<WechatRoleMapper, WechatRole>
    implements WechatRoleService{

    @Override
    public WechatRole getByCode(String code) {
        LambdaQueryWrapper<WechatRole> lqw = Wrappers.lambdaQuery();
        lqw.eq(WechatRole::getCode, code);
        return getOne(lqw);
    }

    @Override
    public Set<String> selectRolePermissionByUserId(Long userId) {
        List<WechatRole> perms = baseMapper.selectRolePermissionByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        for (WechatRole perm : perms) {
            if (ObjectUtil.isNotNull(perm)) {
                permsSet.addAll(StringUtils.splitList(perm.getCode().trim()));
            }
        }
        return permsSet;
    }

    @Override
    public Set<String> selectPermsByUserId(Long userId) {
        List<String> perms = baseMapper.selectPermsByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        for (String perm : perms) {
            if (StringUtils.isNotEmpty(perm)) {
                permsSet.addAll(StringUtils.splitList(perm.trim()));
            }
        }
        return permsSet;
    }
}




