package org.dromara.biz.controller.pc;

import lombok.RequiredArgsConstructor;
import org.dromara.biz.domain.query.club.ClubActivityQuery;
import org.dromara.biz.domain.vo.club.ClubActivityVO;
import org.dromara.biz.service.ClubActivityService;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 会所活动相关接口
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/club_activity")
public class ClubActivityController {

    private final ClubActivityService clubActivityService;

    /**
     * 分页查询会所列表
     */
    @GetMapping("/page")
    public TableDataInfo<ClubActivityVO> page(ClubActivityQuery query) {
        return clubActivityService.queryPage(query);
    }

    /**
     * 查询会所信息
     */
    @GetMapping("/detail/{activityId}")
    public R<ClubActivityVO> detail(@PathVariable Long activityId) {
        return R.ok(clubActivityService.getDetail(activityId));
    }

    /**
     * 新增会所活动
     */
    @PostMapping("/save")
    @RepeatSubmit
    public R<Boolean> save(@RequestBody @Validated ClubActivityVO clubActivity) {
        return R.ok(clubActivityService.save(clubActivity));
    }

    /**
     * 修改活动
     */
    @PostMapping("/update")
    @RepeatSubmit
    public R<Boolean> update(@RequestBody @Validated ClubActivityVO clubActivity) {
        return R.ok(clubActivityService.update(clubActivity));
    }

    /**
     * 删除活动
     */
    @DeleteMapping("/remove")
    public R<Boolean> removeClubActivity(@RequestParam Long [] activityIds) {
        return R.ok(clubActivityService.remove(activityIds));
    }
}
