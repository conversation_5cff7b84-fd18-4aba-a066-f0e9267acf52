package org.dromara.biz.controller.pc;

import lombok.RequiredArgsConstructor;
import org.dromara.biz.domain.vo.ContractGiftVO;
import org.dromara.biz.service.ContractGiftsService;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 签约礼品相关接口
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/contract_gift")
public class ContractGiftController {

    private final ContractGiftsService contractGiftsService;

    /**
     * 新增签约礼品
     * @param contractGiftVO 签约礼品信息
     * @return 新增结果
     */
    @PutMapping("/save")
    @RepeatSubmit
    public R<Boolean> save(@RequestBody @Validated ContractGiftVO contractGiftVO) {
        return R.ok(contractGiftsService.save(contractGiftVO));
    }

    /**
     * 查询签约礼品详情
     */
    @GetMapping("/info/{contractGiftId}")
    public R<ContractGiftVO> info(@PathVariable Long contractGiftId) {
        return R.ok(contractGiftsService.getByContractGiftId(contractGiftId));
    }

    /**
     * 修改签约礼品
     * @param contractGiftVO 签约礼品信息
     * @return 修改结果
     */
    @PostMapping("/update")
    @RepeatSubmit
    public R<Boolean> update(@RequestBody @Validated ContractGiftVO contractGiftVO) {
        return R.ok(contractGiftsService.update(contractGiftVO));
    }

    /**
     * 分页查询签约礼品列表
     */
    @GetMapping("/page")
    public TableDataInfo<ContractGiftVO> queryPage(PageQuery query) {
        return contractGiftsService.queryPage(query);
    }


    /**
     * 查询签约礼品列表
     */
    @GetMapping("/list")
    public R<List<ContractGiftVO>> queryList(PageQuery query) {
        return R.ok(contractGiftsService.queryList(query));
    }
}
