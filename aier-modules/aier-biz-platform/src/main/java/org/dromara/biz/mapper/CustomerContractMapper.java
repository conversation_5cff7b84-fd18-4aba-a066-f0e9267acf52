package org.dromara.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.biz.domain.CustomerContract;
import org.dromara.biz.domain.vo.customer.CustomerContractVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_customer_contract(客户签约表)】的数据库操作Mapper
* @createDate 2024-09-04 17:08:52
* @Entity org.dromara.biz.domain.CustomerContract
*/
public interface CustomerContractMapper extends BaseMapper<CustomerContract> {

    /**
     * 查询客户签约列表
     */
    List<CustomerContractVO> getContractList(@Param(Constants.WRAPPER) QueryWrapper<CustomerContract> wrapper);

    /**
     * 查询签约详情
     */
    CustomerContractVO getContractDetail(Long customerContractId);

    /**
     * 分页查询客户签约列表
     */
    Page<CustomerContractVO> getContractPage(Page<CustomerContractVO> page,
                                             @Param(Constants.WRAPPER) QueryWrapper<CustomerContract> wrapper);

    void autoUpdateOrder();

    void autoUpdateOrderRes();

    void autoUpdateOrderOcc();
}




