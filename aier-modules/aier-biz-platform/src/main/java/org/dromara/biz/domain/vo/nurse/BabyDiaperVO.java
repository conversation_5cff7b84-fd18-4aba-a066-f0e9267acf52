package org.dromara.biz.domain.vo.nurse;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.biz.domain.BizBabyDiaper;
import org.dromara.common.mybatis.handler.type.StringListTypeHandler;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
@AutoMapper(target = BizBabyDiaper.class)
public class BabyDiaperVO {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 宝宝ID
     */
    private Long babyId;

    /**
     * 记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date recordTime;

    /**
     * 大便颜色
     */
    private String stoolColor;

    /**
     * 大便性状
     */
    private String stoolConsistency;

    /**
     * 大便量
     */
    private String stoolAmount;

    /**
     * 换尿不湿次数
     */
    private String diaperChangeCount;

    /**
     * 小便情况
     */
    private String urine;

    /**
     * 小便次数
     */
    private String urineCount;

    /**
     * 异常情况
     */
    private String abnormalCondition;

    /**
     * 备注信息
     */
    private String notes;

    /**
     * 照片列表（存储图片路径或URL，JSON格式）
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> photoList;

    /**
     * 入住时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    private Date createTime;

    /**
     * 宝宝姓名
     */
    private String babyName;

    /**
     * 操作人
     */
    private String authorName;
}
