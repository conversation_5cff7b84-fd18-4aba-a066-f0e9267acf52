package org.dromara.biz.controller.pc;

import lombok.RequiredArgsConstructor;
import org.dromara.biz.domain.vo.ClubFacilitiesVO;
import org.dromara.biz.service.ClubFacilitiesService;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 会所设施相关接口
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/club_facilities")
public class ClubFacilitiesController {

    private final ClubFacilitiesService clubFacilitiesService;

    /**
     * 查询设施列表
     */
    @GetMapping("/list")
    public R<List<ClubFacilitiesVO>> list() {
        return R.ok(clubFacilitiesService.queryList());
    }

    /**
     * 新增会所设施
     * @param vos 会所设施信息
     * @return 新增结果
     */
    @PutMapping("/save")
    @RepeatSubmit
    public R<Boolean> save(@RequestBody @Validated List<ClubFacilitiesVO> vos) {
        return R.ok(clubFacilitiesService.save(vos));
    }
}
