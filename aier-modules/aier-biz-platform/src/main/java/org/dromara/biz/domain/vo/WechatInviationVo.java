package org.dromara.biz.domain.vo;

import cn.hutool.json.JSONObject;
import io.github.linpeilie.annotations.AutoMapper;
import io.github.linpeilie.annotations.AutoMapping;
import lombok.Data;
import org.dromara.biz.domain.BizWechatInviation;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 微信电子请柬vo *
 * <AUTHOR>
 * @since 2024-09-03
 */
@Data
@AutoMapper(target = BizWechatInviation.class, reverseConvertGenerate = false)
public class WechatInviationVo implements Serializable {

    private Long id;

    /**
     * 用户id（保留字段）
     */
    private Long userId;

    /**
     * 宝宝姓名
     */
    private String babyName;

    /**
     * 小名
     */
    private String minName;

    /**
     * 宴会地址
     */
    private String address;

    /**
     * 联系电话
     */
    private String telephone;

    /**
     * 文案

     */
    private String copywriting;

    /**
     * 宾客名称
     */
    private String guestName;

    /**
     * 宾客人数
     */
    private Integer guestNum;

    /**
     * 模板id（暂用URL）
     */
    private String temple;

    /**
     * 卡片标题
     */
    private String title;

    /**
     * 卡片描述
     */
    private String description;

    /**
     * 封面图url
     */
    private String thumbUrl;

    /**
     * 卡片链接地址url
     */
    private String templeUrl;

    private LocalDateTime createTime;

    private String createBy;

    private LocalDateTime updateTime;

    private String updateBy;

    /**
     * 备用字段1
     */
    @AutoMapping(target = "standby1", ignore = true)
    private JSONObject standby1;

    @AutoMapping(target = "standby2", ignore = true)
    private JSONObject standby2;

    @AutoMapping(target = "standby3", ignore = true)
    private JSONObject standby3;

    private String standby4;

    private String standby5;

    @Serial
    private static final long serialVersionUID = 1L;
}
