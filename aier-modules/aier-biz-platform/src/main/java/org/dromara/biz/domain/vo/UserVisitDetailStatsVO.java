package org.dromara.biz.domain.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 用户浏览详情统计vo
 */
@Data
public class UserVisitDetailStatsVO {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 名称
     */
    private String nickname;
    /**
     * 手机号
     */
    private String tel;
    /**
     * 预产期
     */
    private Date dueDate;

    /**
     * 房间停留时间
     */
    private String roomStayTimeStr;

    private Long roomStayTime = 0L;

    /**
     * 社区停留时间
     */
    private String communityStayTimeStr;
    private Long communityStayTime  = 0L;

    /**
     * 膳食停留时间
     */
    private String mealStayTimeStr;
    private Long mealStayTime = 0L;

    /**
     * 产后康复停留时间
     */
    private String recoveryStayTimeStr;
    private Long recoveryStayTime = 0L;

    /**
     * 评价留时间
     */
    private String reviewStayTimeStr;
    private Long reviewStayTime = 0L;

    /**
     * 护理人员留时间
     */
    private String staffStayTimeStr;
    private Long staffStayTime = 0L;

    /**
     * 每日访问次数 7天数据
     */
    private List<UserDayStatsVO> viewsCountStats;
}
