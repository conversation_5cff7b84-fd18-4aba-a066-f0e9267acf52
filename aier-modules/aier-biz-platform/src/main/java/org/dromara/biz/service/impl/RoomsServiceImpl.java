package org.dromara.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.dromara.biz.domain.*;
import org.dromara.biz.domain.bo.*;
import org.dromara.biz.domain.event.CommonEvent;
import org.dromara.biz.domain.query.RoomQuery;
import org.dromara.biz.domain.query.RoomTodoQuery;
import org.dromara.biz.domain.query.room.RoomStatusQuery;
import org.dromara.biz.domain.vo.*;
import org.dromara.biz.domain.vo.room.RoomDetailVO;
import org.dromara.biz.domain.vo.room.RoomOccupationDateRangeVO;
import org.dromara.biz.domain.vo.room.RoomStatusVO;
import org.dromara.biz.domain.vo.room.RoomUserVO;
import org.dromara.biz.mapper.RoomsMapper;
import org.dromara.biz.service.*;
import org.dromara.common.core.enums.*;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【biz_rooms(房间信息表)】的数据库操作Service实现
* @createDate 2024-03-20 19:03:26
*/
@Service
@AllArgsConstructor
@Slf4j
public class RoomsServiceImpl extends ServiceImpl<RoomsMapper, Rooms>
    implements RoomsService{

    private final RoomLevelsService roomLevelsService;
    private final SuitesService suitesService;
    private final CustomersService customersService;
    private final StaffService staffService;
    private final RoomFeedbacksService roomFeedbacksService;
    private final RoomTodosService roomTodosService;
    private final RoomMessagesService roomMessagesService;
    private final RoomCheckoutsService roomCheckoutsService;
    private final RoomOccupationsService roomOccupationsService;
    private final CustomerContractServiceImpl customerContractServiceImpl;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveLevelAndRoom(List<RoomLevelBO> roomLevelList) {
        roomLevelList.forEach(leveRoom->{
            RoomLevels roomLevels = new RoomLevels();
            roomLevels.setFloorNumber(leveRoom.getFloorNumber());
            roomLevelsService.save(roomLevels);
            List<RoomBO> rooms = leveRoom.getRooms();
            List<Rooms> roomList = MapstructUtils.convert(rooms, Rooms.class);
            roomList.forEach(room-> {
                room.setLevelId(roomLevels.getLevelId());
                room.setOccupancyStatus(RoomCheckinStatus.NOT_CHECKED_IN.getCode());
                room.setTotalDaysBooked(0);
            });
            baseMapper.insertBatch(roomList);
        });
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(RoomMoveIntoBO roomMoveIntoBO) {
        //检查当前客户是否已经入住其他房间
        Long customerId = roomMoveIntoBO.getCustomerId();
        Long roomId = roomMoveIntoBO.getRoomId();
        Integer totalDaysBooked = roomMoveIntoBO.getTotalDaysBooked();
        Date checkinDate = roomMoveIntoBO.getCheckinDate();
        Date endDate = DateUtil.offsetDay(checkinDate, totalDaysBooked);

        LambdaUpdateWrapper<Rooms> luw = Wrappers.lambdaUpdate();
        luw.eq(Rooms::getRoomId, roomId);
        luw.set(Rooms::getCustomerId, customerId);
        luw.set(Rooms::getTotalDaysBooked, totalDaysBooked);
        luw.set(Rooms::getCheckinDate, checkinDate);

        List<RoomStaffBO> staffList = roomMoveIntoBO.getStaffList();
        List<String> staffIds = staffList.stream().map(RoomStaffBO::getStaffId).toList();
        List<String> staffName = staffList.stream().map(RoomStaffBO::getStaffName).toList();
        luw.set(Rooms::getServiceStaffIds, String.join(",", staffIds));
        luw.set(Rooms::getStaffName, String.join(",", staffName));

        Date now = DateUtil.date();
        if (ObjectUtil.isNotNull(checkinDate) && now.before(checkinDate)) {
            luw.set(Rooms::getOccupancyStatus, RoomCheckinStatus.PENDING_CHECKIN.getCode());
        }else if (ObjectUtil.isNotNull(checkinDate) && now.after(checkinDate)){
            luw.set(Rooms::getOccupancyStatus, RoomCheckinStatus.CHECKED_IN.getCode());
        }

        staffService.updateServiceNum(staffIds);
        boolean flag = update(luw);
        if(ObjectUtil.isNotNull(roomMoveIntoBO.getCustomerId())){
            RoomCheckouts roomCheckouts = new RoomCheckouts();
            roomCheckouts.setRoomId(roomMoveIntoBO.getRoomId());
            roomCheckouts.setCheckinDate(roomMoveIntoBO.getCheckinDate());
            roomCheckouts.setCustomerId(roomMoveIntoBO.getCustomerId());
            roomCheckouts.setTotalDaysBooked(roomMoveIntoBO.getTotalDaysBooked());
            roomCheckouts.setServiceStaffIds(staffIds);
            roomCheckouts.setCheckoutDate(endDate);
            roomCheckoutsService.save(roomCheckouts);
        }
        return flag;
    }

    @Override
    public List<RoomLevelVO> queryList(RoomQuery query) {
        LambdaQueryWrapper<RoomLevels> levelLqw = Wrappers.lambdaQuery();
        if(ObjectUtil.isNotNull(query.getFloorNumber())){
            levelLqw.eq(RoomLevels::getFloorNumber, query.getFloorNumber());
        }
        List<RoomLevels> levelList = roomLevelsService.list(levelLqw);
        List<Long> levelIds = levelList.stream().map(RoomLevels::getLevelId).toList();
        if(CollectionUtils.isEmpty(levelIds)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<Rooms> lqw = Wrappers.lambdaQuery();
        lqw.in(Rooms::getLevelId, levelIds);
        if(ObjectUtil.isNotNull(query.getOccupancyStatus())){
            lqw.eq(Rooms::getOccupancyStatus, query.getOccupancyStatus());
        }
        List<RoomVO> roomList = baseMapper.selectVoList(lqw);

        if(CollectionUtils.isEmpty(roomList)){
            return levelList.stream().map(level->{
                RoomLevelVO roomLevel = new RoomLevelVO();
                roomLevel.setFloorNumber(level.getFloorNumber());
                roomLevel.setRooms(null);
                roomLevel.setLevelId(level.getLevelId());
                return roomLevel;
            }).toList();
        }

        List<Long> suiteIds = roomList.stream().map(RoomVO::getSuiteId).toList();
        List<Long> customerIds = roomList.stream().map(RoomVO::getCustomerId).toList();
        Map<Long, SuiteVO> suiteMap = suitesService.getMapByIds(suiteIds);
        Map<Long, CustomerVO> customerMap = customersService.getMapByIds(customerIds);
        List<Long> userIds = customerMap.values().stream().map(CustomerVO::getUserId).toList();
        Map<Long, Boolean> roomTodoMap = roomFeedbacksService.getRoomTodoMap(userIds);
        Map<Long, RoomLevels> levelMap = levelList.stream()
            .collect(Collectors.toMap(RoomLevels::getLevelId, Function.identity(), (o1, o2) -> o1));
        Map<Long, List<RoomVO>> roomMap = roomList.stream().collect(Collectors.groupingBy(RoomVO::getLevelId));

        List<RoomLevelVO> result = new ArrayList<>();
        roomMap.forEach((k, v) ->{
            RoomLevelVO roomLevel = new RoomLevelVO();
            RoomLevels level = levelMap.get(k);
            if(ObjectUtil.isNotNull(level)){
                roomLevel.setFloorNumber(level.getFloorNumber());
                roomLevel.setLevelId(level.getLevelId());
            }
            List<RoomVO> rooms = v.stream().peek(room -> {
                SuiteVO suite = suiteMap.get(room.getSuiteId());
                if (ObjectUtil.isNotNull(suite)) {
                    List<String> suitePhotos = suite.getSuitePhotos();
                    room.setSuiteName(suite.getRoomName());
                    room.setPhotos(suitePhotos.stream().findFirst().orElse(""));
                }
                CustomerVO customer = customerMap.get(room.getCustomerId());
                if (ObjectUtil.isNotNull(customer)) {
                    room.setCustomerName(customer.getName());
                    room.setOccupancyStatus(RoomCheckinStatus.CHECKED_IN.getCode());
                    room.setIsTodo(roomTodoMap.getOrDefault(customer.getUserId(), false));
                } else {
                    Date checkinDate = room.getCheckinDate();
                    Date now = DateUtil.date();
                    if (ObjectUtil.isNotNull(checkinDate) && now.before(checkinDate)) {
                        room.setOccupancyStatus(RoomCheckinStatus.PENDING_CHECKIN.getCode());
                    }
                }
                Date checkinDate = room.getCheckinDate();
                room.setMoveIntoDays(getMoveIntoDays(checkinDate, room.getTotalDaysBooked()));
            }).toList();
            roomLevel.setRooms(rooms);
            result.add(roomLevel);
        });
        return result
            .stream()
            .sorted(Comparator.comparing(f->RoomLevelsServiceImpl.convertChineseToNumber(f.getFloorNumber())))
            .toList();
    }

    /**
     * 入住天数计算方法
     * @param checkinDate 入住时间
     * @param totalDaysBooked 总入住天数
     * @return 已经入住天数
     */
    private Long getMoveIntoDays(Date checkinDate, Integer totalDaysBooked){
        if(ObjectUtil.isNull(checkinDate) || ObjectUtil.isNull(totalDaysBooked)){
            return 0L;
        }
        long totalDays = Long.valueOf(totalDaysBooked);
        Date now = DateUtil.date();
        long moveIntoDays = DateUtil.betweenDay(checkinDate, now, true);
        return Math.min(moveIntoDays, totalDays);
    }

    @Override
    public RoomMoveIntoBO getByRoomId(Long roomId) {
        Rooms rooms = baseMapper.selectById(roomId);
        RoomMoveIntoBO roomMoveIntoBO = MapstructUtils.convert(rooms, RoomMoveIntoBO.class);

        if(CollectionUtils.isNotEmpty(rooms.getServiceStaffIds())){
            List<StaffVO> staffs = staffService.queryByStaffIds(rooms.getServiceStaffIds());
            List<RoomStaffBO> staffList = staffs.stream().map(staff -> {
                RoomStaffBO roomStaffBO = new RoomStaffBO();
                roomStaffBO.setStaffId(Convert.convert(String.class, staff.getStaffId()));
                roomStaffBO.setStaffName(staff.getStaffName());
                roomStaffBO.setStaffPost(staff.getStaffPost());
                roomStaffBO.setStaffPhotos(staff.getStaffPhotos());
                return roomStaffBO;
            }).toList();
            roomMoveIntoBO.setStaffList(staffList);
        }
        return roomMoveIntoBO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean out(Long roomId) {
        Rooms rooms = baseMapper.selectById(roomId);
        if(ObjectUtil.isNull(rooms)){
            throw new ServiceException("房间不存在");
        }
        //重置房间
        LambdaUpdateWrapper<Rooms> roomLqw = Wrappers.lambdaUpdate();
        roomLqw.eq(Rooms::getRoomId, roomId);
        roomLqw.set(Rooms::getCustomerId, null);
        roomLqw.set(Rooms::getOccupancyStatus, RoomCheckinStatus.NOT_CHECKED_IN.getCode());
        roomLqw.set(Rooms::getServiceStaffIds, null);
        roomLqw.set(Rooms::getTotalDaysBooked, null);
        roomLqw.set(Rooms::getCheckinDate, null);
        roomCheckoutsService.updateCheckoutDate(rooms.getRoomId(), rooms.getCustomerId());
        return baseMapper.update(roomLqw) > 0;
    }

    @Override
    public RoomVO getByCustomerId(Long customerId) {
        LambdaQueryWrapper<Rooms> roomLqw = Wrappers.lambdaQuery();
        roomLqw.eq(Rooms::getCustomerId, customerId);
        roomLqw.last("limit 1");
        Rooms rooms = baseMapper.selectOne(roomLqw);
        if(ObjectUtil.isNull(rooms)){
            return new RoomVO();
        }
        return getRoomInfo(rooms.getRoomId());
    }

    @Override
    public Map<Long, Rooms> getByCustomerIds(List<Long> customerIds){
        if(CollUtil.isEmpty(customerIds)){
            return Collections.emptyMap();
        }
        LambdaQueryWrapper<Rooms> roomLqw = Wrappers.lambdaQuery();
        roomLqw.in(Rooms::getCustomerId, customerIds);
        List<Rooms> roomList = baseMapper.selectList(roomLqw);
        return roomList.parallelStream().collect(Collectors.toMap(Rooms::getCustomerId, Function.identity(), (o1, o2) -> o1));

    }

    @Override
    public List<RoomTodoVO> queryTodoList(RoomTodoQuery query) {
        LambdaQueryWrapper<Rooms> roomLqw = Wrappers.lambdaQuery();
        if(ObjectUtil.isNotNull(query.getRoomId())){
            roomLqw.eq(Rooms::getRoomId, query.getRoomId());
        }
        roomLqw.eq(Rooms::getOccupancyStatus, RoomCheckinStatus.CHECKED_IN.getCode());
        List<Rooms> rooms = baseMapper.selectList(roomLqw);
        if(CollectionUtils.isEmpty(rooms)){
            return Collections.emptyList();
        }
        List<RoomTodoVO> result = new ArrayList<>();
        rooms.forEach(room->{
            Long customerId = room.getCustomerId();
            List<String> serviceStaffIds = room.getServiceStaffIds();
            Customers customer = customersService.getById(customerId);
            if(ObjectUtil.isNull(customer)){
                return;
            }
            LambdaQueryWrapper<RoomFeedbacks> feedbackLqw = Wrappers.lambdaQuery();
            feedbackLqw.eq(RoomFeedbacks::getUserId, customer.getUserId());
            feedbackLqw.ne(RoomFeedbacks::getSatisfaction, SatisfactionLevelEnum.MODERATELY_SATISFIED.getCode());
            if(ObjectUtil.isNotNull(query.getStatus())){
                feedbackLqw.eq(RoomFeedbacks::getStatus, query.getStatus());
            }
            List<RoomFeedbacks> roomFeedbacks = roomFeedbacksService.list(feedbackLqw);
            LambdaQueryWrapper<Staff> staffLqw = Wrappers.lambdaQuery();
            staffLqw.in(Staff::getStaffId, serviceStaffIds);
            List<StaffVO> staffList = staffService.queryByStaffIds(serviceStaffIds);
            List<Long> suiteIds = rooms.stream().map(Rooms::getSuiteId).toList();
            Map<Long, SuiteVO> suiteMap = suitesService.getMapByIds(suiteIds);
            List<RoomTodoVO> todoList = roomFeedbacks.stream().map(feedback -> {
                RoomTodoVO roomTodovo = new RoomTodoVO();

                roomTodovo.setFeedbackId(feedback.getFeedbackId());
                roomTodovo.setCheckinDate(room.getCheckinDate());
                SuiteVO suite = suiteMap.getOrDefault(room.getSuiteId(), new SuiteVO());
                roomTodovo.setRoomName(suite.getRoomName());
                roomTodovo.setRoomNumber(room.getRoomNumber());
                roomTodovo.setCustomerName(customer.getName());
                roomTodovo.setContent(feedback.getContent());
                roomTodovo.setCreateTime(feedback.getCreateTime());
                roomTodovo.setSatisfaction(feedback.getSatisfaction());
                roomTodovo.setStaffList(staffList);
                roomTodovo.setStatus(feedback.getStatus());
                List<RoomTodos> roomTodosList = roomTodosService.lambdaQuery()
                    .eq(RoomTodos::getFeedbackId, feedback.getFeedbackId()).list();
                roomTodovo.setReplyContent(roomTodosList);
                return roomTodovo;
            }).toList();
            result.addAll(todoList);
        });
        return result;
    }

    @Override
    public RoomTodoVO queryTodoInfo(Long feedbackId) {
        RoomFeedbacks feedback = roomFeedbacksService.getById(feedbackId);
        RoomTodoVO roomTodovo = new RoomTodoVO();
        roomTodovo.setFeedbackId(feedback.getFeedbackId());
        roomTodovo.setContent(feedback.getContent());
        roomTodovo.setCreateTime(feedback.getCreateTime());
        roomTodovo.setSatisfaction(feedback.getSatisfaction());
        roomTodovo.setStatus(feedback.getStatus());
        roomTodovo.setResultSatisfaction(feedback.getResultSatisfaction());
        List<RoomTodos> roomTodosList = roomTodosService.lambdaQuery()
            .eq(RoomTodos::getFeedbackId, feedback.getFeedbackId()).list();
        roomTodovo.setReplyContent(roomTodosList);

        Long userId = feedback.getUserId();
        RoomVO room = getByUserId(userId);
        roomTodovo.setRoomName(room.getSuiteName());
        roomTodovo.setRoomNumber(room.getRoomNumber());
        return roomTodovo;
    }

    /**
     * 通过用户id查询房间 可查询当前用户入住的房间
     * @param userId 用户id
     * @return 房间
     */
    @Override
    public RoomVO getByUserId(Long userId){
        CustomerVO customer = customersService.getByUserId(userId);
        if(ObjectUtil.isNull(customer)){
            throw new ServiceException(String.format("查看代办详情错误可能原因为：[%s]","当前待办用户ID查询不到绑定的客户，请联系管理员"));
        }
        Long customerId = customer.getCustomerId();
        RoomVO room = getByCustomerId(customerId);
        if(ObjectUtil.isNull(room)){
            throw new ServiceException(String.format("查看代办详情错误可能原因为：[%s]","当前客户ID查询不到绑定的房间，请联系管理员"));
        }
        return room;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean reply(RoomTodoBO roomTodo) {
        LambdaQueryWrapper<RoomTodos> todoLqw = Wrappers.lambdaQuery();
        todoLqw.eq(RoomTodos::getFeedbackId, roomTodo.getFeedbackId());
        long todoCount = roomTodosService.count(todoLqw);
        RoomFeedbacks roomFeedbacks = roomFeedbacksService.getById(roomTodo.getFeedbackId());
        if(todoCount <= 0){
            roomFeedbacks.setStatus(TodoStatusEnum.PROCESSING.getCode());
        }else {
            roomFeedbacks.setStatus(TodoStatusEnum.PROCESSED.getCode());
        }
        roomFeedbacksService.updateById(roomFeedbacks);
        RoomTodos roomTodos = MapstructUtils.convert(roomTodo, RoomTodos.class);
        roomTodosService.save(roomTodos);
        //用户消息
        RoomMessages roomMessages = new RoomMessages();
        roomMessages.setTodoId(roomTodos.getTodoId());
        roomMessages.setUserId(roomFeedbacks.getUserId());
        roomMessages.setStatus(false);
        if(todoCount <= 0){
            roomMessages.setType(MessageTypeEnum.EVALUATION_RESPONSE.getCode());
        }else {
            roomMessages.setType(MessageTypeEnum.PROCESS_RESULT.getCode());
        }
        roomMessages.setContent(roomTodo.getReplyContent());
        return roomMessagesService.save(roomMessages);
    }

    @Override
    public TableDataInfo<RoomCheckoutVO> queryRoomCheckoutPage(PageQuery query) {
        LambdaQueryWrapper<RoomCheckouts> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(RoomCheckouts::getCreateTime);
        Page<RoomCheckouts> page = roomCheckoutsService.page(query.build(), lqw);
        List<RoomCheckouts> records = page.getRecords();
        List<RoomCheckoutVO> result = records.stream().map(this::roomCheckoutConvertHandler).toList();
        return TableDataInfo.build(result, page.getTotal());
    }

    @Override
    public RoomCheckoutVO queryRoomCheckoutInfo(Long checkoutId) {
        RoomCheckouts roomCheckouts = roomCheckoutsService.getById(checkoutId);
        return this.roomCheckoutConvertHandler(roomCheckouts);
    }

    private RoomCheckoutVO roomCheckoutConvertHandler(RoomCheckouts roomCheckout){
        Long roomId = roomCheckout.getRoomId();
        RoomVO room = this.getRoomInfo(roomId);
        RoomCheckoutVO roomCheckoutvo = new RoomCheckoutVO();
        roomCheckoutvo.setCheckoutId(roomCheckout.getCheckoutId());

        roomCheckoutvo.setRoomNumber(room.getRoomNumber());
        roomCheckoutvo.setCheckoutDate(roomCheckout.getCheckoutDate());
        roomCheckoutvo.setCheckinDate(roomCheckout.getCheckinDate());
        roomCheckoutvo.setSuiteName(room.getSuiteName());
        CustomerVO customer = customersService.getByCustomerId(roomCheckout.getCustomerId());
        if(ObjectUtil.isNotNull(customer)){
            roomCheckoutvo.setCustomerName(customer.getName());
            roomCheckoutvo.setTel(customer.getTel());
        }
        Pair<String, List<String>> staffPair = this.getStaffHandler(roomCheckout.getServiceStaffIds());
        roomCheckoutvo.setStaffList(staffPair.getRight());
        roomCheckoutvo.setStaffName(staffPair.getLeft());
        return roomCheckoutvo;
    }


    /**
     * 获取房间全部信息
     * @param roomId 房间id
     * @return 房间信息
     */
    public RoomVO getRoomInfo(Long roomId){
        RoomVO roomvo = baseMapper.selectVoById(roomId);
        Long suiteId = roomvo.getSuiteId();
        Suites suites = suitesService.getById(suiteId);
        if(ObjectUtil.isNotNull(suites)){
            roomvo.setSuiteName(suites.getRoomName());
            if(CollectionUtils.isNotEmpty(suites.getSuitePhotos())){
                roomvo.setPhotos(suites.getSuitePhotos().stream().findFirst().orElse(""));
            }
        }
        Long customerId = roomvo.getCustomerId();
        CustomerVO customer = customersService.getByCustomerId(customerId);
        if(ObjectUtil.isNotNull(customer)){
            roomvo.setCustomerName(customer.getName());
            roomvo.setTel(customer.getTel());
            roomvo.setOccupancyStatus(RoomCheckinStatus.CHECKED_IN.getCode());
        }else{
            Date checkinDate = roomvo.getCheckinDate();
            Date now = DateUtil.date();
            if(ObjectUtil.isNotNull(checkinDate) && now.after(checkinDate)){
                roomvo.setOccupancyStatus(RoomCheckinStatus.PENDING_CHECKIN.getCode());
            }
        }
        Date checkinDate = roomvo.getCheckinDate();
        roomvo.setMoveIntoDays(getMoveIntoDays(checkinDate, roomvo.getTotalDaysBooked()));
        List<String> staffIds = roomvo.getServiceStaffIds();
        Pair<String, List<String>> staffPair = this.getStaffHandler(staffIds);
        roomvo.setStaffName(staffPair.getLeft());
        roomvo.setStaffNameList(staffPair.getRight());

        if(ObjectUtil.isNotNull(customer)){
            Long todoStatus = roomFeedbacksService
                .lambdaQuery()
                .eq(RoomFeedbacks::getUserId, customer.getUserId())
                .eq(RoomFeedbacks::getStatus, TodoStatusEnum.PENDING.getCode())
                .count();
            roomvo.setIsTodo(todoStatus > 0);
        }else{
            roomvo.setIsTodo(false);
        }
        return roomvo;
    }

    @Override
    public RoomStatusStatsVO queryRoomStatusStats() {
        return baseMapper.queryRoomStatusStats();
    }

    public List<RoomVO> getByStaffId(Long staffId) {
        LambdaQueryWrapper<Rooms> lqw = Wrappers.lambdaQuery();
        lqw.eq(Rooms::getOccupancyStatus, RoomCheckinStatus.CHECKED_IN.getCode());
        lqw.apply("FIND_IN_SET({0}, service_staff_ids)", staffId);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public List<SelectOptionsVO> getStaffCustomerSelectOptions(Long staffUserId) {
        Staff staff = staffService.lambdaQuery()
            .eq(Staff::getUserId, staffUserId)
            .one();
        if(ObjectUtil.isNull(staff)){
            return List.of();
        }
        Long staffId = staff.getStaffId();
        List<Rooms> roomList = lambdaQuery()
            .apply("FIND_IN_SET({0}, service_staff_ids)", staffId)
            .list();
        Set<Long> customerIds = roomList.stream().map(Rooms::getCustomerId).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(customerIds)){
            customerIds.add(-1L);
        }
        List<Customers> customerList = customersService.lambdaQuery()
            .in(Customers::getCustomerId, customerIds).list();
        return customerList.stream().map(customers -> {
            SelectOptionsVO selectOptionsVO = new SelectOptionsVO();
            selectOptionsVO.setLabel(customers.getName());
            selectOptionsVO.setValue(customers.getCustomerId());
            return selectOptionsVO;
        }).toList();
    }

    /**
     * 获取房间护理人员名称
     * @param staffIds 护理人员ids
     * @return 护理人员名称集合和护理人员名称
     */
    private Pair<String, List<String>> getStaffHandler(List<String> staffIds){
        List<String> staffNameList = staffService.lambdaQuery()
            .in(Staff::getStaffId, staffIds)
            .list()
            .stream()
            .map(Staff::getStaffName)
            .toList();
        String staffName = String.join(",", staffNameList);
        return new ImmutablePair<>(staffName, staffNameList);
    }

    @Override
    public List<SelectOptionsStringVO> getCurrentStaffServiceCustomerOptions() {
        Long userId = LoginHelper.getUserId();
        StaffVO staff = staffService.getByUserId(userId);
        if(ObjectUtil.isNull(staff)){
            return List.of();
        }
        List<RoomVO> rooms = getByStaffId(staff.getStaffId());
        if(com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(rooms)){
            return List.of();
        }
        List<Long> customerIds = rooms.stream().map(RoomVO::getCustomerId).toList();
        if(com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isEmpty(customerIds)){
            return List.of();

        }
        Map<Long, CustomerVO> customerMaps = customersService.getMapByIds(customerIds);
        return rooms.stream().map(room->{
            Long customerId = room.getCustomerId();
            SelectOptionsStringVO selectOptionsStringVO = new SelectOptionsStringVO();
            selectOptionsStringVO.setValue(customerId.toString());
            CustomerVO customer = customerMaps.getOrDefault(customerId, new CustomerVO());
            String name = customer.getName();
            String roomNumber = room.getRoomNumber();
            if(StringUtils.isNotEmpty(name) && StringUtils.isNotEmpty(roomNumber)){
                selectOptionsStringVO.setLabel(name + "(" + roomNumber + ")");
            }else {
                selectOptionsStringVO.setLabel("未知客户");
            }
            return selectOptionsStringVO;
        }).toList();
    }

    @Override
    public List<SelectOptionsVO> getCustomerServicePersonnelOptions(Long customerId){
        RoomVO room = getByCustomerId(customerId);
        if(ObjectUtil.isNull(room)){
            return List.of();
        }
        List<String> serviceStaffIds = room.getServiceStaffIds();
        List<StaffVO> staffList = staffService.queryByStaffIds(serviceStaffIds);
        return staffList.stream().map(staff -> {
            SelectOptionsVO selectOptionsVO = new SelectOptionsVO();
            selectOptionsVO.setLabel(staff.getStaffName());
            selectOptionsVO.setValue(staff.getStaffId());
            return selectOptionsVO;
        }).toList();
    }

    @Override
    public List<RoomVO> getSuitesList(Long suitesId){
        LambdaQueryWrapper<Rooms> lqw = Wrappers.lambdaQuery();
        if (null != suitesId){
            lqw.eq(Rooms::getSuiteId, suitesId);
        }
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public List<RoomStatusVO> getRoomStatus(RoomStatusQuery query){
        customerContractServiceImpl.autoUpdateOrder();
        handlerMonth(query);
        return baseMapper.getRoomStatus(query);
    }

    @Override
    public List<RoomOccupationDateRangeVO> getRoomOccupationsByDateRange(RoomStatusQuery query){
        handlerMonth(query);
        List<RoomOccupationDateRangeVO> roomOccupationsByDateRange = baseMapper.getRoomOccupationsByDateRange(query);
        roomOccupationsByDateRange.forEach(room -> {
            List<RoomOccupationDateRangeVO.Occupation> sortedOccupations = room.getOccupations().stream()
                    .filter(item->{
                        Date endTime = item.getEndTime();
                        Date nextMonth = DateUtil.offsetMonth(query.getStartDate(), 1);
                        if(ObjectUtil.isNotNull(endTime) && ObjectUtil.isNotNull(nextMonth)){
                            return !endTime.before(nextMonth);
                        }
                        return true;
                    })
                    .sorted(Comparator.comparing(RoomOccupationDateRangeVO.Occupation::getStartTime))
                    .collect(Collectors.toList());
            room.setOccupations(sortedOccupations);
        });
        return roomOccupationsByDateRange;
    }

    @Override
    public RoomBO getBoInfo(Long roomId) {
        Rooms rooms = baseMapper.selectById(roomId);
        RoomBO boomBO = MapstructUtils.convert(rooms,RoomBO.class);
        if (boomBO != null) {
            SuiteVO info = suitesService.getInfo(boomBO.getSuiteId());
            RoomLevels roomLevel = roomLevelsService.getById(boomBO.getLevelId());
            boomBO.setSuiteName(info.getRoomName());
            boomBO.setLevelName(roomLevel.getFloorNumber());
        }
        return boomBO;
    }

    @Override
    public List<RoomBO> queryMpList(RoomQuery query) {
        return baseMapper.queryMpList(query);
    }

    @Override
    public Boolean saveMp(Rooms rooms) {

        Rooms customerService1 = baseMapper.selectOne(Wrappers.<Rooms>lambdaQuery().eq(Rooms::getRoomNumber, rooms.getRoomNumber()));
        //校验是否修改的当前房间
        if(customerService1!=null && !customerService1.getRoomId().equals(rooms.getRoomId())){
            throw new ServiceException(rooms.getRoomNumber()+"已存在！请勿重复添加");
        }
        if (ObjectUtil.isNull(rooms.getRoomId())){
            this.save(rooms);
        }
        this.updateById(rooms);



        return true;
    }

    @Override
    public List<Rooms> getByRoomIds(List<Long> roomIds) {
        LambdaQueryWrapper<Rooms> lqw = Wrappers.lambdaQuery();
        lqw.in(Rooms::getRoomId, roomIds);
        return baseMapper.selectList(lqw);
    }

    @Override
    public RoomDetailVO getDetail(Long roomId){
        return baseMapper.getDetail(roomId);
    }

    /**
     * 查询月份处理
     */
    private void handlerMonth(RoomStatusQuery query){
        if(ObjectUtil.isNotNull(query.getMonth()) && ObjectUtil.isNotNull(query.getYear())){
            Calendar calendar = Calendar.getInstance();
            calendar.set(Calendar.YEAR, query.getYear());
            calendar.set(Calendar.MONTH, query.getMonth());
            Date now = calendar.getTime();
            query.setStartDate(DateUtil.beginOfMonth(now));
            query.setEndDate(DateUtil.endOfMonth(now));
        }
        if(ObjectUtil.isNotNull(query.getStart()) && ObjectUtil.isNotNull(query.getEnd())){
            query.setStartDate(DateUtil.beginOfMonth(query.getStart()));
            query.setEndDate(DateUtil.endOfMonth(query.getEnd()));
        }
    }

    /**
     * 退房
     */
    @Transactional(rollbackFor = Exception.class)
    public void handlerCheckOut(Long roomId){

        Date now = DateUtil.date();
        // 入住记录
        RoomOccupations roomOccupations = roomOccupationsService.getByRoomId(roomId);
        Date checkIn = roomOccupations.getCheckIn();
        long totalDaysActual = DateUtil.betweenDay(checkIn, now, true);
        roomOccupations.setTotalDaysActual(Convert.convert(Integer.class, totalDaysActual));
        roomOccupations.setStatus(1);
        roomOccupations.setCheckOut(now);
        roomOccupationsService.updateById(roomOccupations);

        // 房间状态
        LambdaUpdateWrapper<Rooms> roomLuw = Wrappers.lambdaUpdate();
        roomLuw.eq(Rooms::getRoomId, roomId);
        roomLuw.set(Rooms::getStatus, 0);
        update(roomLuw);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handlerPostpone(Long roomId){
        RoomOccupations roomOccupations = roomOccupationsService.getByRoomId(roomId);
        roomOccupations.setStatus(2);
        roomOccupationsService.updateById(roomOccupations);
    }

    @Override
    public RoomVO getRoomByUserId(Long userId) {
        CustomerVO customer = customersService.getByUserId(userId);
        LambdaQueryWrapper<Rooms> roomLqw = Wrappers.lambdaQuery();
        roomLqw.eq(Rooms::getCustomerId, customer.getCustomerId());
        roomLqw.last("limit 1");
        Rooms rooms = baseMapper.selectOne(roomLqw);
        return MapstructUtils.convert(rooms,RoomVO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handlerCheckIn(Long roomId, Long customerId, Date startDate, Date endDate){

        LambdaQueryWrapper<Rooms> roomLqw = Wrappers.lambdaQuery();
        roomLqw.eq(Rooms::getRoomId, roomId);
        roomLqw.eq(Rooms::getStatus, 1);
        long count = count(roomLqw);
        if(count > 0L){
            throw new ServiceException("该房间当前不为空房无法入住！");
        }

        LambdaUpdateWrapper<Customers> customerLuw = Wrappers.lambdaUpdate();
        customerLuw.eq(Customers::getCustomerId, customerId);
        customerLuw.set(Customers::getIsArrivedAtStore, true);
        customersService.update(customerLuw);

        long totalDaysBooked = DateUtil.betweenDay(startDate, endDate, true);
        RoomOccupations roomOccupations = new RoomOccupations();
        roomOccupations.setCustomerId(customerId);
        roomOccupations.setRoomId(roomId);
        roomOccupations.setCheckIn(startDate);
        roomOccupations.setCheckOut(endDate);
        roomOccupations.setStatus(0);
        roomOccupations.setTotalDaysBooked(Convert.convert(Integer.class, totalDaysBooked));
        roomOccupationsService.save(roomOccupations);
        SpringUtils.context().publishEvent(new CommonEvent<>(this, customerId, CommonEvent.EventType.STORE_CUSTOMER));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void handlerCheckIn(RoomReservations roomReservations){
        handlerCheckIn(roomReservations.getRoomId(), roomReservations.getCustomerId(),
                roomReservations.getStartDate(), roomReservations.getEndDate());
    }

    @Override
    public List<RoomUserVO> getRoomUserList(Long roomId){

        String tenantId = TenantHelper.getTenantId();
        if (roomId.equals(0L)){
            return TenantHelper.ignore(()-> baseMapper.getRoomUserList(null, tenantId));
        }
        return TenantHelper.ignore(()-> baseMapper.getRoomUserList(roomId, tenantId));
    }

    @Override
    public List<RoomVO> getByRoleCode(String roleCode){
        if(AppUserRoleEnum.CUSTOMER.name().equals(roleCode)){

            return CollUtil.newArrayList(baseMapper.getByRoleCode(roleCode));
        }
        return baseMapper.getByRoleCode(roleCode);
    }
}




