package org.dromara.biz.domain.query;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.util.List;

/**
 * 护理人员查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class StaffQuery extends PageQuery {

    /**
     * 员工姓名
     */
    private String staffName;

    /**
     * 员工职位
     */
    private String staffPost;

    /**
     * 多个职位查询
     */
    private List<String> inPost;

    /**
     * 是否展示
     */
    private Boolean isShow;
}
