package org.dromara.biz.listener;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.Customers;
import org.dromara.biz.domain.Employee;
import org.dromara.biz.domain.WechatUser;
import org.dromara.biz.domain.event.CommonEvent;
import org.dromara.biz.domain.vo.CustomerVO;
import org.dromara.biz.service.*;
import org.dromara.common.core.enums.AppUserRoleEnum;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@AllArgsConstructor
public class CommonEventListener {

    private final CustomersService customersService;
    private final WechatUserService wechatUserService;
    private final WechatUserRoleService wechatUserRoleService;
    private final IBizEmployeeService employeeService;

    /**
     * 客户事件
     */
    @EventListener
    @Transactional(rollbackFor = Exception.class)
    public void customerEvent(CommonEvent<Customers> customerEvent){
        CommonEvent.EventType eventType = customerEvent.getType();
        Customers customer = customerEvent.getData();

        if(eventType.name().equals(CommonEvent.EventType.CREATE_CUSTOMER.name())){
            createCustomer(customer);
        }
    }

    /**
     * 其他事件
     */
    @EventListener
    @Transactional(rollbackFor = Exception.class)
    public void customerStoreEvent(CommonEvent<Long> event){
        CommonEvent.EventType eventType = event.getType();

        if(eventType.name().equals(CommonEvent.EventType.STORE_CUSTOMER.name())){
            Long customerId = event.getData();
            storeCustomer(customerId);
        }
    }

    /**
     * 创建客户
     */
    private void createCustomer(Customers customer) {
        WechatUser wechatUser = wechatUserService.getByTel(customer.getTel());

        if(ObjectUtil.isNotNull(wechatUser)){
            Long userId = wechatUser.getUserId();
            LambdaUpdateWrapper<Customers> luw = Wrappers.lambdaUpdate();
            luw.eq(Customers::getCustomerId, customer.getCustomerId());
            luw.set(Customers::getUserId, userId);
            customersService.update(luw);
        }else {
            wechatUser = new WechatUser();
            wechatUser.setNickname(customer.getName());
            wechatUser.setTel(customer.getTel());
            wechatUser.setStatus("0");
            wechatUser.setUtype("1");
            wechatUserService.save(wechatUser);
            LambdaUpdateWrapper<Customers> luw = Wrappers.lambdaUpdate();
            luw.eq(Customers::getCustomerId, customer.getCustomerId());
            luw.set(Customers::getUserId, wechatUser.getUserId());
            customersService.update(luw);
        }
    }

    /**
     * 员工事件
     */
    @EventListener
    @Transactional(rollbackFor = Exception.class)
    public void employeeEvent(CommonEvent<Employee> event) {
        CommonEvent.EventType type = event.getType();
        Employee employee = event.getData();
        if(CommonEvent.EventType.CREATE_EMPLOYEE.name().equals(type.name())){
            createEmployee(employee);
        }
    }

    private void createEmployee(Employee employee) {
        WechatUser wechatUser = wechatUserService.getByTel(employee.getPhone());
        if(ObjectUtil.isNotNull(wechatUser)){
            Long userId = wechatUser.getUserId();
            LambdaUpdateWrapper<Employee> luw = Wrappers.lambdaUpdate();
            luw.eq(Employee::getEmployeeId, employee.getEmployeeId());
            luw.set(Employee::getUserId, userId);
            employeeService.update(luw);
        }else {
            wechatUser = new WechatUser();
            wechatUser.setNickname(employee.getName());
            wechatUser.setTel(employee.getPhone());
            wechatUser.setStatus("0");
            wechatUser.setAvatar(employee.getPhotos());
            wechatUser.setUtype("1");
            wechatUserService.save(wechatUser);
            LambdaUpdateWrapper<Employee> luw = Wrappers.lambdaUpdate();
            luw.eq(Employee::getEmployeeId, employee.getEmployeeId());
            luw.set(Employee::getUserId, wechatUser.getUserId());
            employeeService.update(luw);
        }
        wechatUserRoleService.createRole(wechatUser.getUserId(), employee.getRoleCode());
    }

    /**
     * 客户到店
     */
    private void storeCustomer(Long customerId) {
        CustomerVO customer = customersService.getByCustomerId(customerId);
        if(ObjectUtil.isNotNull(customer)){
            wechatUserRoleService.createRole(customer.getUserId(), AppUserRoleEnum.CUSTOMER.name());
        }
    }
}
