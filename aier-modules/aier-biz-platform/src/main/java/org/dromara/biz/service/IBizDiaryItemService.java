package org.dromara.biz.service;

import org.dromara.biz.domain.BizDiaryItem;
import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.bo.diary.DiaryItemBO;

/**
 * <p>
 * 服务项配置表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
public interface IBizDiaryItemService extends IService<BizDiaryItem> {

    Boolean create(DiaryItemBO diaryItemBO);

    Boolean update(DiaryItemBO diaryItemBO);

    DiaryItemBO getDetail(Integer timeNumber);
}
