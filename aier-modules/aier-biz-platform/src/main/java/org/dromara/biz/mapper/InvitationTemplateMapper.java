package org.dromara.biz.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.dromara.biz.domain.InvitationTemplate;
import org.dromara.biz.domain.vo.CustomerVO;
import org.dromara.biz.domain.vo.invitation.InvitationTemplateVO;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

/**
* <AUTHOR>
* @description 针对表【biz_invitation_template(请柬模版表)】的数据库操作Mapper
* @createDate 2024-07-30 11:25:40
* @Entity org.dromara.biz.domain.InvitationTemplate
*/
public interface InvitationTemplateMapper extends BaseMapperPlus<InvitationTemplate, InvitationTemplateVO> {

    /**
     * 分页查询用户收藏请帖列表
     */
    @InterceptorIgnore(tenantLine = "true", dataPermission = "false")
    Page<InvitationTemplateVO> getTemplateCollectPage(Page<CustomerVO> page, Long userId);
}




