package org.dromara.biz.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.biz.domain.MealItems;
import org.dromara.biz.domain.query.MealItemQuery;
import org.dromara.biz.domain.vo.MealItemVO;
import org.dromara.biz.service.MealItemsService;
import org.dromara.biz.mapper.MealItemsMapper;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_meal_items(月子餐菜品信息表)】的数据库操作Service实现
* @createDate 2024-03-11 16:54:27
*/
@Service
public class MealItemsServiceImpl extends ServiceImpl<MealItemsMapper, MealItems>
    implements MealItemsService{

    @Override
    public List<MealItemVO> queryList(Long mealId) {
        LambdaQueryWrapper<MealItems> lqw = Wrappers.lambdaQuery();
        lqw.eq(MealItems::getMealId, mealId);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public TableDataInfo<MealItemVO> queryPage(MealItemQuery query) {
        IPage<MealItemVO> page = baseMapper.selectVoPage(query.build(), Wrappers.lambdaQuery());
        return TableDataInfo.build(page);
    }

    @Override
    public List<MealItemVO> queryList(MealItemQuery query) {
        LambdaQueryWrapper<MealItems> lqw = Wrappers.lambdaQuery();
        //去掉类别
//        lqw.eq(MealItems::getCategory, query.getCategory());
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public String saveItem(MealItemVO mealItem) {
        MealItems mealItems = MapstructUtils.convert(mealItem, MealItems.class);
        if (ObjectUtil.isNotNull(mealItem.getItemId())){
         baseMapper.updateById(mealItems);
            return "success";
        }
        baseMapper.insert(mealItems);
        return "success";
    }

    @Override
    public MealItemVO itemDetail(Long itemId) {
        MealItems mealItems = baseMapper.selectById(itemId);
        return MapstructUtils.convert(mealItems, MealItemVO.class);
    }

    @Override
    public Boolean isRec(Long itemId) {
        MealItems mealItems = baseMapper.selectById(itemId);
        if (mealItems.getIsRec()){
            mealItems.setIsRec(false);
            baseMapper.updateById(mealItems);
            return true;
        }
        mealItems.setIsRec(true);
        baseMapper.updateById(mealItems);
        return true;
    }
}




