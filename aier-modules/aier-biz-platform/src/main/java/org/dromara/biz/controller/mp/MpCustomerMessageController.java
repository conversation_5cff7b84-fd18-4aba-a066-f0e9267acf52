package org.dromara.biz.controller.mp;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.RoomMessages;
import org.dromara.biz.domain.vo.CustomerMessageVO;
import org.dromara.biz.service.RoomMessagesService;
import org.dromara.common.core.domain.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 小程序客户端朋友圈&会所消息相关接口
 */
@AllArgsConstructor
@Slf4j
@Validated
@RestController
@RequestMapping("/mp/customer")
public class MpCustomerMessageController {

    private final RoomMessagesService roomMessagesService;

    /**
     * 查询客户端消息详情
     * @return 结果
     */
    @GetMapping("/message_detail")
    public R<List<RoomMessages>> queryMessageDetailList(){
        return R.ok(roomMessagesService.queryMessageDetailList());
    }

    /**
     * 查询客户端房间反馈处理结果消息列表
     * @return 结果
     */
    @GetMapping("/message_list")
    public R<List<CustomerMessageVO>> queryMessageList(){
        return R.ok(roomMessagesService.queryMessageList());
    }

}
