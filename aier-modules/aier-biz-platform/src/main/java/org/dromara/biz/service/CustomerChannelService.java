package org.dromara.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.CustomerChannel;
import org.dromara.biz.domain.query.customer.CustomerChannelQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_customer_channel(渠道列表)】的数据库操作Service
* @createDate 2024-11-01 15:52:57
*/
public interface CustomerChannelService extends IService<CustomerChannel> {

    Boolean create(CustomerChannel customerChannel);

    Boolean update(CustomerChannel customerChannel);

    TableDataInfo<CustomerChannel> queryPage(CustomerChannelQuery query);

    List<CustomerChannel> queryList(CustomerChannelQuery query);
}
