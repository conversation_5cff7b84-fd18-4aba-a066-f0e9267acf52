package org.dromara.biz.listener;

import com.alibaba.excel.context.AnalysisContext;
import org.dromara.biz.domain.vo.FeedPostExportVO;
import org.dromara.common.excel.core.DefaultExcelListener;

/**
 * Excel带下拉框的解析处理器
 *
 * <AUTHOR>
 */
public class FeedPostExportListener extends DefaultExcelListener<FeedPostExportVO> {

    public FeedPostExportListener() {
        // 显示使用构造函数，否则将导致空指针
        super(true);
    }

    @Override
    public void invoke(FeedPostExportVO data, AnalysisContext context) {
        // 添加到处理结果中
        getExcelResult().getList().add(data);
    }
}
