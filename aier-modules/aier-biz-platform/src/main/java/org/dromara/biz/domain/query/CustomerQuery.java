package org.dromara.biz.domain.query;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 客户查询
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CustomerQuery extends PageQuery {

    /**
     * 姓名
     */
    private String name;

    /**
     * 客户状态 0=未联系 1=已联系 2=已到店 3=已交易
     */
    private String status;

    /**
     * 电话
     */
    private String tel;

    /**
     * 客户来源
     */
    private String source;

    /**
     * 标签id
     */
    private Long customerTagId;

    /**
     * 跟进人id
     */
    private Long followerId;

    /**
     * 预产期开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date dueDateStart;

    /**
     * 预产期结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date dueDateEnd;

    /**
     * 创建时间结束
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date createTimeEnd;

    /**
     * 创建时间开始
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date createTimeStart;

    /**
     * 时间周期 day:天；week：周；month：月
     */
    private String timeCycle;

    /**
     * 意向度
     */
    private String intentLeve;

    /**
     * 关键字查询
     */
    private String keyword;

    /**
     * 排序 1=时间降序；2=时间升序； 默认时间降序
     */
    private Integer orderByType = 1;

    /**
     * 提醒状态 不传查全部
     */
    private Boolean reminderStatus;

    /**
     * 1=毛客；2=有效；3=到店；4=成交；5=金额
     */
    private Integer customerType;

    /**
     * 是否到店
     */
    private Boolean isArriveStore;


    /**
     * 查询周期 本月=0；历史=1；本日=2
     */
    private Integer queryPeriod;

    /**
     * 年份 查询周期为历史时传 year=2024或者 2025 或者 2026
     */
    private Integer year;

    /**
     * 月份 查询周期为历史时传 month值范围为0-11
     */
    private Integer month;

}
