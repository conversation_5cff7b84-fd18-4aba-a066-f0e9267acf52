package org.dromara.biz.service;

import org.dromara.biz.domain.ReviewReplies;
import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.vo.ReviewRepliesVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_review_replies(总体评价商家回复表)】的数据库操作Service
* @createDate 2024-03-25 14:26:20
*/
public interface ReviewRepliesService extends IService<ReviewReplies> {

    List<ReviewRepliesVO> queryListByReviewId(Long reviewId);

}
