package org.dromara.biz.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.biz.domain.PackageNursing;
import org.dromara.biz.domain.PackageSupplies;
import org.dromara.biz.domain.Packages;
import org.dromara.biz.domain.Suites;
import org.dromara.biz.domain.query.PackageQuery;
import org.dromara.biz.domain.vo.*;
import org.dromara.biz.mapper.PackagesMapper;
import org.dromara.biz.service.*;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【biz_packages(优惠套餐表)】的数据库操作Service实现
* @createDate 2024-03-12 14:27:07
*/
@Service
@AllArgsConstructor
@Slf4j
public class PackagesServiceImpl extends ServiceImpl<PackagesMapper, Packages>
    implements PackagesService{

    private final PackageSuppliesService packageSuppliesService;
    private final PackageNursingService packageNursingService;
    private final SuitesService suitesService;
    private final ContractGiftsService contractGiftsService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean save(PackageVO packagevo) {
        Packages packages = MapstructUtils.convert(packagevo, Packages.class);
        Long suiteId = packages.getSuiteId();
        if(ObjectUtil.isNull(suiteId)){
            List<SuiteVO> suiteList = packagevo.getSuiteList();
            SuiteVO suitevo = suiteList.stream().findFirst()
                .orElseThrow(() -> new ServiceException("月子套房不能为空"));
            packages.setSuiteId(suitevo.getSuiteId());
        }
        packages.setOnlineStatus(true);
        boolean flag = this.save(packages);
        this.otherHandler(packagevo, packages.getPackageId());
        return flag;
    }

    @Override
    public Boolean mpSave(PackageBo packageBO) {
        Packages packages = MapstructUtils.convert(packageBO, Packages.class);

        Packages customerService1 = baseMapper.selectOne(Wrappers.<Packages>lambdaQuery().eq(Packages::getPackageName, packageBO.getPackageName()));
        if(customerService1!=null && !customerService1.getPackageId().equals(packages.getPackageId())){
            throw new ServiceException("套餐名称已存在！");
        }
        if (!ObjectUtil.isNull(packageBO.getPackageId())){
            this.updateById(packages);
        }else {
            if (packages != null) {
                packages.setOnlineStatus(true);
            }
            this.save(packages);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(PackageVO packageVO) {
        Packages packages = MapstructUtils.convert(packageVO, Packages.class);
        boolean flag = this.updateById(packages);
        Long packageId = packages.getPackageId();

        LambdaQueryWrapper<PackageSupplies> suppliesLqw = Wrappers.lambdaQuery();
        suppliesLqw.eq(PackageSupplies::getPackageId, packageId);
        packageSuppliesService.remove(suppliesLqw);

        LambdaQueryWrapper<PackageNursing> nursingLqw = Wrappers.lambdaQuery();
        nursingLqw.eq(PackageNursing::getPackageId, packageId);
        packageNursingService.remove(nursingLqw);
        this.otherHandler(packageVO, packages.getPackageId());
        return flag;
    }

    @Override
    public PackageVO getInfo(Long packageId) {
        PackageVO packagevo = baseMapper.selectVoById(packageId);
        if(ObjectUtil.isNull(packagevo)){
            return null;
        }
        return this.convertHandler(packagevo);
    }

    @Override
    public TableDataInfo<PackageVO> queryPage(PackageQuery query) {
        IPage<PackageVO> page = baseMapper.selectVoPage(query.build(), this.buildQueryWrapper(query));
        List<PackageVO> records = page.getRecords();
        List<PackageVO> result = this.convertHandler(records);
        return TableDataInfo.build(result, page.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeByPackageId (Long packageId){
        int row = baseMapper.deleteById(packageId);
        LambdaQueryWrapper<PackageSupplies> suppliesLqw = Wrappers.lambdaQuery();
        suppliesLqw.eq(PackageSupplies::getPackageId, packageId);
        packageSuppliesService.remove(suppliesLqw);
        LambdaQueryWrapper<PackageNursing> nursingLqw = Wrappers.lambdaQuery();
        nursingLqw.eq(PackageNursing::getPackageId, packageId);
        packageNursingService.remove(nursingLqw);
        return row > 0;
    }

    @Override
    public List<PackageVO> queryRecommendation(Integer limit) {
        LambdaQueryWrapper<Packages> lqw = Wrappers.lambdaQuery();
        lqw.eq(Packages::getOnlineStatus, true);
        lqw.eq(Packages::getOnHomepage, true);
        List<PackageVO> packageList = baseMapper.selectVoList(lqw);
        List<PackageVO> packages = packageList.subList(0, Math.min(limit, packageList.size()));
        return this.convertHandler(packages);
    }

    @Override
    public List<PackageVO> queryList(PackageQuery query) {
        List<PackageVO> list = baseMapper.selectVoList(buildQueryWrapper(query));
        return this.convertHandler(list);
    }

    @Override
    public List<PackageBo> queryMpList(PackageQuery query) {
        LambdaQueryWrapper<Packages> lqw = Wrappers.lambdaQuery();
        lqw.eq(Packages::getOnlineStatus, true);
        lqw.orderByDesc(Packages::getCreateTime);
        Page<Packages> page = this.page(query.build(), lqw);
        List<Packages> pages = page.getRecords();
        List<PackageBo> bos =new ArrayList<>();
        for (Packages pa : pages) {
            PackageBo roomBO = MapstructUtils.convert(pa,PackageBo.class);
            SuiteVO info = suitesService.getInfo(pa.getSuiteId());
            if (roomBO != null && info != null) {
                roomBO.setSuiteName(info.getRoomName());
            }
            bos.add(roomBO);
        }
        return bos;
    }

    @Override
    public PackageVO getByName(String packageName, String tenantId) {
        LambdaQueryWrapper<Packages> lqw = Wrappers.lambdaQuery();
        lqw.eq(Packages::getPackageName, packageName);
        lqw.eq(Packages::getTenantId, tenantId);
        List<PackageVO> packageList = baseMapper.selectVoList(lqw);
        if (packageList != null && packageList.size() > 0) {
            return packageList.get(0);
        }
        return null;
    }

    private LambdaQueryWrapper<Packages> buildQueryWrapper(PackageQuery query){
        LambdaQueryWrapper<Packages> lqw = Wrappers.lambdaQuery();
        if (ObjectUtil.isNotNull(query.getOnlineStatus())){
            lqw.eq(Packages::getOnlineStatus, query.getOnlineStatus());
        }
        lqw.orderByDesc(Packages::getCreateTime);
        if(ObjectUtil.isNotNull(query.getOnHomepage())){
            lqw.eq(Packages::getOnHomepage, query.getOnHomepage());
        }
        return lqw;
    }

    private List<PackageVO> convertHandler(List<PackageVO> packageList){
        Set<Long> packageIds = packageList.stream().map(PackageVO::getPackageId).collect(Collectors.toSet());
        List<Long> suiteIds = packageList.stream().map(PackageVO::getSuiteId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(packageIds)){
            return List.of();
        }
        Map<Long, List<PackageSuppliesVO>> suppliesMap = packageSuppliesService.getMapByPackageIds(packageIds);
        Map<Long, List<PackageNursingVO>> nursingMap = packageNursingService.getMapByPackageIds(packageIds);
        Map<Long, SuiteVO> suiteMap = suitesService.getMapByIds(suiteIds);

        packageList.forEach(packageVO -> {
            Long packageId = packageVO.getPackageId();
            Long suiteId = packageVO.getSuiteId();
            List<PackageSuppliesVO> suppliesList = suppliesMap.getOrDefault(packageId, List.of());
            Map<String, List<PackageSuppliesVO>> suppliesMapByCategory = suppliesList
                .stream()
                .collect(Collectors.groupingBy(PackageSuppliesVO::getCategory));
            packageVO.setBabySuppliesList(suppliesMapByCategory.get("BABY"));
            packageVO.setMamaSuppliesList(suppliesMapByCategory.get("MAMA"));
            List<PackageNursingVO> nursingList = nursingMap.getOrDefault(packageId, List.of());
            Map<String, List<PackageNursingVO>> nursingMapByCategory = nursingList
                .stream()
                .collect(Collectors.groupingBy(PackageNursingVO::getCategory));
            packageVO.setMamaNursingList(nursingMapByCategory.get("MAMA"));
            packageVO.setBabyNursingList(nursingMapByCategory.get("BABY"));
            packageVO.setPostpartumList(nursingMapByCategory.get("CK"));

            SuiteVO suite = suiteMap.getOrDefault(suiteId, new SuiteVO());
            packageVO.setRoomType(suite.getRoomType());
            packageVO.setOrientation(suite.getOrientation());
            packageVO.setMinArea(suite.getMinArea());
            packageVO.setMaxArea(suite.getMaxArea());
            packageVO.setOutdoorFeatures(suite.getOutdoorFeatures());
            packageVO.setFacilityFeatures(suite.getFacilityFeatures());
            packageVO.setMediaFeatures(suite.getMediaFeatures());
            packageVO.setBathroomFacilities(suite.getBathroomFacilities());
            if(ObjectUtil.isNotNull(suite.getSuitePhotos())){
                packageVO.setPhotos(suite.getSuitePhotos().stream().findFirst().orElse(""));
            }
            packageVO.setSuiteList(List.of(suite));
            Long contractGiftId = packageVO.getContractGiftId();
            Map<Long, ContractGiftVO> contractGiftMap = contractGiftsService.queryMap();
            packageVO.setContractGift(contractGiftMap.getOrDefault(contractGiftId, new ContractGiftVO()));
            packageVO.setIsExistContractGift(contractGiftsService.isExistContractGift(contractGiftId));
        });
        return packageList;
    }

    private PackageVO convertHandler(PackageVO packageVO){
        return this.convertHandler(List.of(packageVO)).stream().findFirst().orElse(new PackageVO());
    }

    private void setOtherHandler(PackageVO packagevo){
        Long packageId = packagevo.getPackageId();
        List<PackageSuppliesVO> packageSupplies = packageSuppliesService.queryList(packageId);
        Map<String, List<PackageSuppliesVO>> suppliesMap = packageSupplies
            .stream()
            .collect(Collectors.groupingBy(PackageSuppliesVO::getCategory));

        packagevo.setBabySuppliesList(suppliesMap.get("BABY"));
        packagevo.setMamaSuppliesList(suppliesMap.get("MAMA"));
        List<PackageNursingVO> packageNursings = packageNursingService.queryList(packageId);
        Map<String, List<PackageNursingVO>> NursingMap = packageNursings
            .stream()
            .collect(Collectors.groupingBy(PackageNursingVO::getCategory));
        packagevo.setMamaNursingList(NursingMap.get("MAMA"));
        packagevo.setBabyNursingList(NursingMap.get("BABY"));

        Long suiteId = packagevo.getSuiteId();
        LambdaQueryWrapper<Suites> suiteLqw = Wrappers.lambdaQuery();
        suiteLqw.eq(Suites::getSuiteId, suiteId);
        List<Suites> suitesList = suitesService.list(suiteLqw);
        List<SuiteVO> suitevoList = MapstructUtils.convert(suitesList, SuiteVO.class);
        packagevo.setSuiteList(suitevoList);
    }

    private void otherHandler(PackageVO packageVO, Long packageId){
        List<PackageSuppliesVO> mamaSuppliesList = packageVO.getMamaSuppliesList();
        List<PackageSuppliesVO> babySuppliesList = packageVO.getBabySuppliesList();
        List<PackageNursingVO> mamaNursingList = packageVO.getMamaNursingList();
        List<PackageNursingVO> babyNursingList = packageVO.getBabyNursingList();
        List<PackageNursingVO> ckNursingList = packageVO.getPostpartumList();

        List<PackageSupplies> suppliesList = new ArrayList<>();
        List<PackageNursing> nursingList = new ArrayList<>();

        mamaSuppliesList.stream()
            .map(supplies -> mapToPackageSupplies(supplies, "MAMA", packageId))
            .forEach(suppliesList::add);
        babySuppliesList.stream()
            .map(supplies -> mapToPackageSupplies(supplies, "BABY", packageId))
            .forEach(suppliesList::add);

        mamaNursingList.stream()
            .map(nursing -> mapToPackageNursing(nursing, "MAMA", packageId))
            .forEach(nursingList::add);
        babyNursingList.stream()
            .map(nursing -> mapToPackageNursing(nursing, "BABY", packageId))
            .forEach(nursingList::add);
        if (ckNursingList != null  && ckNursingList.size() > 0) {
            ckNursingList.stream()
                .map(nursing -> mapToPackageNursing(nursing, "CK", packageId))
                .forEach(nursingList::add);
        }
        packageSuppliesService.saveBatch(suppliesList);
        packageNursingService.saveBatch(nursingList);
    }


    private PackageSupplies mapToPackageSupplies(PackageSuppliesVO supplies, String category, Long packageId) {
        PackageSupplies packageSupplies = new PackageSupplies();
        packageSupplies.setPackageId(packageId);
        packageSupplies.setSuppliesId(supplies.getSuppliesId());
        packageSupplies.setQuantity(supplies.getQuantity());
        packageSupplies.setInStoreUse(supplies.getInStoreUse());
        packageSupplies.setSupplyName(supplies.getSupplyName());
        packageSupplies.setBrandName(supplies.getBrandName());
        packageSupplies.setCategory(category);
        return packageSupplies;
    }

    private PackageNursing mapToPackageNursing(PackageNursingVO nursing, String category, Long packageId) {
        PackageNursing packageNursing = new PackageNursing();
        packageNursing.setPackageId(packageId);
        packageNursing.setNursingId(nursing.getNursingId());
        packageNursing.setQuantity(nursing.getQuantity());
        packageNursing.setCategory(category);
        packageNursing.setProjectName(nursing.getProjectName());
        return packageNursing;
    }
}




