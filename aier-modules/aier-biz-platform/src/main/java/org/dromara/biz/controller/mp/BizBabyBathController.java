package org.dromara.biz.controller.mp;


import cn.dev33.satoken.annotation.SaIgnore;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.BizBabyBath;
import org.dromara.biz.domain.vo.nurse.BabyBathVO;
import org.dromara.biz.service.IBizBabyBathService;
import org.dromara.biz.service.IBizBabyInfoService;
import org.dromara.common.core.domain.R;
import org.springframework.web.bind.annotation.*;

import org.springframework.stereotype.Controller;

import java.sql.Wrapper;
import java.util.List;

/**
 * 宝宝洗澡记录表 前端控制器
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping("/mp/baby/bath")
public class BizBabyBathController {
    private IBizBabyBathService bizBabyBathService;
    private final IBizBabyInfoService babyInfoService;

    /**
     * 新增宝宝洗澡记录
     * @param babyBath
     * @return
     */
    @PostMapping("/insert")
    public R<Boolean> insert(@RequestBody BizBabyBath babyBath) {

        return R.ok(bizBabyBathService.save(babyBath));
    }

    /**
     * 修改宝宝洗澡记录
     * @param babyBath
     * @return
     */
    @PostMapping("/update")
    public R<Boolean> update(@RequestBody BizBabyBath babyBath) {
        return R.ok(bizBabyBathService.updateById(babyBath));
    }

    /**
     * 洗澡记录列表
     * @param customerId
     * @return
     */
    @GetMapping("/list")
    @SaIgnore
    public R<List<BabyBathVO>> list(@RequestParam("customerId") Long customerId){
        return R.ok(bizBabyBathService.listVo(customerId));
    }
    /**
     * 洗澡记录列表
     * @return
     */
    @GetMapping("/myList")
    public R<List<BabyBathVO>> myList(){
        Long  babyId = babyInfoService.getByLoginUser();
        if (babyId != null) {
            return R.ok(bizBabyBathService.listVo(babyId));
        }
        return R.ok(null);
    }

    /**
     * 获取记录详情 （宝宝洗澡）
     * @param id
     * @return
     */
    @GetMapping("/getDetail")
    @SaIgnore
    public R<BabyBathVO> getDetail(@RequestParam("id") Long id){
        return R.ok(bizBabyBathService.getDetail(id));
    }

    /**
     * 删除记录
     * @param id
     * @return
     */
    @GetMapping("/delete")
    public R<Boolean> delete(@RequestParam("id") Long id){
            return R.ok(bizBabyBathService.removeById(id));
    }
}
