package org.dromara.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.biz.domain.Invitation;
import org.dromara.biz.service.InvitationDetailsService;
import org.dromara.biz.mapper.InvitationDetailsMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【biz_invitation_details(请柬详情表)】的数据库操作Service实现
* @createDate 2024-07-30 10:50:39
*/
@Service
public class InvitationDetailsServiceImpl extends ServiceImpl<InvitationDetailsMapper, Invitation>
    implements InvitationDetailsService{

}




