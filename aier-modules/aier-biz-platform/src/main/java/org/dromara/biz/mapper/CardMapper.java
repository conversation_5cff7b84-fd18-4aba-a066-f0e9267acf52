package org.dromara.biz.mapper;

import org.dromara.biz.domain.Card;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.dromara.biz.domain.vo.card.UserCardVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_card】的数据库操作Mapper
* @createDate 2025-02-25 17:37:05
* @Entity org.dromara.biz.domain.Card
*/
public interface CardMapper extends BaseMapper<Card> {

    List<UserCardVO> myCollect(Long userId);
}




