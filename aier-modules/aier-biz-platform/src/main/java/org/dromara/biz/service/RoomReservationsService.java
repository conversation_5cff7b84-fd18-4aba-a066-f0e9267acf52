package org.dromara.biz.service;

import org.dromara.biz.domain.RoomReservations;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【biz_room_reservations(房间预定表)】的数据库操作Service
* @createDate 2024-09-12 20:42:19
*/
public interface RoomReservationsService extends IService<RoomReservations> {

    /**
     * 获取客户房间预定信息
     */
    RoomReservations getByCustomerId(Long customerId);

    /**
     * 通过客户id删除房间预定信息
     */
    void removeByCustomerId(Long customerId, Long roomId);
}
