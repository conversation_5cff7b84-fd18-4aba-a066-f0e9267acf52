package org.dromara.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.biz.domain.CommunityVideos;
import org.dromara.biz.mapper.CommunityVideosMapper;
import org.dromara.biz.service.CommunityVideosService;
import org.dromara.common.core.constant.UgcConstants;
import org.dromara.common.core.enums.UgcSuggestEnum;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【biz_community_videos(朋友圈视频表)】的数据库操作Service实现
* @createDate 2024-06-24 19:57:41
*/
@Service
public class CommunityVideosServiceImpl extends ServiceImpl<CommunityVideosMapper, CommunityVideos>
    implements CommunityVideosService{

    @Override
    public Map<Long, List<String>> getVideosByPostIds(Set<Long> postIds) {
        if(CollectionUtils.isEmpty(postIds)){
            return Map.of();
        }
        LambdaQueryWrapper<CommunityVideos> lqw = Wrappers.lambdaQuery();
        lqw.in(CommunityVideos::getPostId, postIds);
        List<CommunityVideos> videos = list(lqw);
        return videos.stream()
            .collect(Collectors.toMap(CommunityVideos::getPostId, video -> {
                String suggest = video.getSuggest();
                //待审查或者审查中
                if(UgcSuggestEnum.REVIEW.getSuggest().equals(suggest)
                    || UgcSuggestEnum.PENDING.getSuggest().equals(suggest)){
                    return new ArrayList<>(List.of(UgcConstants.VIDEO_REVIEW));
                }
                //审查不通过
                if(UgcSuggestEnum.RISKY.getSuggest().equals(suggest)){
                    return new ArrayList<>(List.of(UgcConstants.VIDEO_RISKY));
                }
                return new ArrayList<>(List.of(video.getVideoUrl()));
            }, (o1, o2) -> {
                o1.addAll(o2);
                return o1;
            }));
    }
}




