package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 客户标签专业话术表
 * @TableName biz_customer_tag_professional
 */
@TableName(value ="biz_customer_tag_professional")
@Data
public class CustomerTagProfessional implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 话术名称
     */
    private String name;

    /**
     * 话术描述
     */
    @TableField("`desc`")
    private String desc;

    /**
     * 客户标签id
     */
    private Long customerTagId;

    /**
     * 
     */
    private Date createTime;

    /**
     * 
     */
    private Date updateTime;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}