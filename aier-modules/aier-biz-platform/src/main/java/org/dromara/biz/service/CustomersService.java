package org.dromara.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.CustomerReminders;
import org.dromara.biz.domain.Customers;
import org.dromara.biz.domain.SalesNotification;
import org.dromara.biz.domain.bo.CustomerCreateBO;
import org.dromara.biz.domain.bo.CustomerOperateBO;
import org.dromara.biz.domain.bo.customer.CustomerContractBO;
import org.dromara.biz.domain.bo.customer.CustomerTransferBO;
import org.dromara.biz.domain.params.customer.ReminderParams;
import org.dromara.biz.domain.query.CustomerQuery;
import org.dromara.biz.domain.query.SeasCustomerQuery;
import org.dromara.biz.domain.query.customer.*;
import org.dromara.biz.domain.vo.*;
import org.dromara.biz.domain.vo.customer.*;
import org.dromara.common.core.domain.R;
import org.dromara.common.excel.core.ExcelResult;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【biz_customers(客户信息表)】的数据库操作Service
* @createDate 2024-03-18 17:16:18
*/
public interface CustomersService extends IService<Customers> {

    /**
     * 分页查询客户列表
     */
    TableDataInfo<CustomerVO> queryPage(CustomerQuery query);

    /**
     * 分页查询客户列表
     */
    TableDataInfo<CustomerVO> querySeasPage(SeasCustomerQuery query);

    /**
     * 查询客户详情
     */
    CustomerVO getByCustomerId(Long customerId);

    /**
     * 修改客户信息
     */
    Boolean update(CustomerVO vo);

    List<CustomerBirthdayVO> queryBirthdayList(String type);

    /**
     * 根据用户id查询客户
     */
    CustomerVO getByUserId(Long userId);

    Map<Long, CustomerVO> getMapByIds(List<Long> customerIds);

    List<SelectOptionsVO> getSelectOptions();

    /**
     * 查询所有过生日的客户列表
     * @return 过生日的客户列表
     */
    List<CustomerBirthdayVO> queryAllBirthdayList();

    /**
     * 查询最近10天过生日的所有客户数量
     * @return 过生日的客户数量
     */
    Long queryAllBirthdayCount();

    /**
     * 查询客户map
     * @param userIds 用户ids
     */
    Map<Long, CustomerVO> getMapByUserIds(Set<Long> userIds);

    /**
     * 新增客资线索
     * @param customer 客资线索信息
     */
    Boolean createCustomer(CustomerCreateBO customer);

    /**
     * 创建提醒事项
     */
    Boolean createReminder(ReminderParams reminderParams);

    /**
     * 获取我的提醒事项
     */
    List<CustomerReminders> getMyReminders();

    /**
     * 查询客户跟进记录日志列表
     */
    TableDataInfo<CustomerFollowUpLogsVO > getFollowedLogs(CustomerFollowLogQuery params);

    /**
     * 分页查询客户操作日志列表
     */
    TableDataInfo<CustomerOperationLogsVO> getOperationLogs(CustomerOperationLogQuery params);

    /**
     * 查询客资分配统计 全部、待分配、已分配
     */
    CustomerAssignmentStats getAssignmentStats();

    /**
     * 销售员消息通知定时任务
     */
    void salesNotificationTask();

    /**
     * 跟进客户
     */
    Boolean customerOperate(CustomerOperateBO operate);

    /**
     * 查询消息通知列表
     */
    List<SalesNotification> getNotificationList(SalesNotificationQuery query);

    /**
     * 客户线索统计
     */
    CustomerLeadStats getCustomerLeadStats(CustomerLeadStatsQuery query);

    /**
     * 我的客户统计
     */
    CustomerInterestStats getCustomerMyStats(CustomerStatsQuery query);

    /**
     * 客户签约
     */
    Boolean customerContract(CustomerContractBO customerContract);

    /**
     * 确认到店
     */
    Boolean confirmArrivedAtStore(Long customerId);

    /**
     * 转移客户
     */
    Boolean transfer(CustomerTransferBO customerTransferBO);


    /**
     * 订单录入
     */
    Boolean orderEntry(OrderEntryVo orderEntryVo);
    /**
     * 订单修改
     */
    Boolean updateOrder(OrderEntryVo orderEntryVo);

    /**
     * 订单取消
     */
    Boolean cancelOrder(Long id);
    /**
     * 获取订单详情
     */
    OrderEntryVo getOrderDetail(Long customerId);

    /**
     * 查询每个销售的客户统计
     */
    List<CustomerSalesStatsVO> getCustomerSalesStats(CustomerLeadStatsQuery query);

    /**
     * 查询每个渠道的客户统计
     */
    List<CustomerSourceStatsVO> getCustomerSourceStats(CustomerLeadStatsQuery query);

    /**
     * 查询每个渠道意向度统计
     */
    List<CustomerIntentSourceStatsVO> getCustomerSourceIntentStats(CustomerLeadStatsQuery query);

    /**
     * 查询每个员工意向度统计
     */
    List<CustomerIntentEmployeeStatsVO> getCustomerEmployeeIntentStats(CustomerLeadStatsQuery query);

    /**
     * 查询客户年龄分布统计
     */
    List<CustomerAgeRangeStatsVO> getCustomerAgeRangeStats();

    /**
     * 客户收入统计
     */
    CustomerIncomeStatsVO getCustomerIncomeStats(CustomerLeadStatsQuery query);

    /**
     * 客户标签统计
     */
    List<CustomerInterestStats.TopIssueCategories> getCustomerTagGroupStats(CustomerLeadStatsQuery query);

    /**
     * 套餐收入统计
     */
    List<CustomerPackageStatsVO> getCustomerPackageStats(CustomerLeadStatsQuery query);

    /**
     * 查询每日客户统计
     */
    List<CustomerDayStoreStatsVO> getCustomerDayStoreStats(CustomerLeadStatsQuery query);

    /**
     * 查询客户来源列表
     */
    List<String> getCustomerSourceList();

    /**
     * 查询客户意向度统计
     */
    CustomerInterestStats getCustomerInterestStats(CustomerLeadStatsQuery query);

    /**
     * 根据用户ids  获取用户列表
     * @param customerIds
     * @return
     */
    List<CustomerVO> getCustomerList(List<Long> customerIds);

    /**
     * 订单延期
     * @param orderEntryVo
     * @return
     */
    R<Boolean> extensionOrder(OrderEntryVo orderEntryVo);

    /**
     * 获取已入住客户列表
     */
    List<CustomerVO> getCheckedInCustomers();

    /**
     *根据userId 获取用户列表
     * @param userIds
     * @return
     */
    List<CustomerVO> getCustomerListByUserIds(List<Long> userIds);

    /**
     * 分配公海客户
     * @param seasCustomerVO
     * @return
     */
    R<Boolean> allocationCustomer(SeasCustomerVO seasCustomerVO);

    /**
     * 获取已生成订单的宝妈
     * @return
     */
    List<CustomerVO> getMomList();

    /**
     * 员工客户统计-表格
     * @param query
     * @return
     */
    List<CustomerSalesTableVO> getCustomerSalesTable(CustomerLeadStatsQuery query);

    /**
     * 渠道客户统计-表格
     * @param query
     * @return
     */
    List<CustomerSalesTableVO> getCustomerSourceTable(CustomerLeadStatsQuery query);

    /**
     * 导入客资
     * @param customerExportResult
     * @return
     */
    Boolean importExcel( List<CustomerExportVO> customerExportResult);

    /**
     * 查询客户跟进数量
     * @param query
     * @return
     */
    List<CustomersFollowVO> getFollowedLogsNumber(CustomerFollowLogQuery query);
}
