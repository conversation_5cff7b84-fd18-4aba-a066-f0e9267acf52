package org.dromara.biz.controller.pc;

import lombok.RequiredArgsConstructor;
import org.dromara.biz.domain.query.PregnancyTrackerQuery;
import org.dromara.biz.domain.vo.PregnancyTrackerVO;
import org.dromara.biz.service.PregnancyTrackerService;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 孕期助手相关接口
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/pregnancy_tracker")
public class PregnancyTrackerController {

    private final PregnancyTrackerService pregnancyTrackerService;

    /**
     * 分页查询孕期助手列表
     * @param query 查询参数
     */
    @GetMapping("/page")
    public TableDataInfo<PregnancyTrackerVO> queryPage(PregnancyTrackerQuery query) {
        return pregnancyTrackerService.queryPage(query);
    }

    /**
     * 查询孕期助手详情
     * @param trackerId 孕期助手id
     */
    @GetMapping("/{trackerId}")
    public R<PregnancyTrackerVO> getInfoById(@PathVariable Long trackerId) {
        return R.ok(pregnancyTrackerService.getInfoById(trackerId));
    }

    /**
     * 新增孕期助手信息
     * @param pregnancyTrackerVO 孕期助手信息
     * @return 新增结果
     */
    @PostMapping
    @RepeatSubmit
    public R<Boolean> save(@RequestBody @Validated PregnancyTrackerVO pregnancyTrackerVO) {
        return R.ok(pregnancyTrackerService.save(pregnancyTrackerVO));
    }

    /**
     * 修改孕期助手信息
     * @param pregnancyTrackerVO 修改孕期助手信息
     * @return 修改结果
     */
    @PutMapping
    @RepeatSubmit
    public R<Boolean> update(@RequestBody @Validated PregnancyTrackerVO pregnancyTrackerVO) {
        return R.ok(pregnancyTrackerService.update(pregnancyTrackerVO));
    }

    /**
     * 删除孕期助手
     * @param trackerId 孕期助手id
     * @return 删除结果
     */
    @DeleteMapping("/{trackerId}")
    public R<Boolean> remove(@PathVariable Long trackerId){
        return R.ok(pregnancyTrackerService.removeById(trackerId));
    }
}
