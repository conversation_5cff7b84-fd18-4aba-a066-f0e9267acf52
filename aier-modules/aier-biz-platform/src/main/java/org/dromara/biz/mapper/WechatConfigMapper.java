package org.dromara.biz.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import org.dromara.biz.domain.WechatMiniProgram;
import org.dromara.biz.domain.vo.WechatMiniProgramVO;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_wechat_config(微信配置表)】的数据库操作Mapper
* @createDate 2024-03-13 20:35:08
* @Entity org.dromara.biz.domain.WechatConfig
*/
public interface WechatConfigMapper extends BaseMapperPlus<WechatMiniProgram, WechatMiniProgramVO> {

    @InterceptorIgnore(tenantLine = "true")
    List<WechatMiniProgram> queryListIgnoreTenant(String type);
}




