package org.dromara.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.biz.domain.CheckinGifts;
import org.dromara.biz.domain.ContractGifts;
import org.dromara.biz.domain.Coupons;
import org.dromara.biz.domain.vo.CouponVO;
import org.dromara.biz.service.CouponsService;
import org.dromara.biz.mapper.CouponsMapper;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_coupons(优惠券)】的数据库操作Service实现
* @createDate 2024-05-21 15:47:31
*/
@Service
public class CouponsServiceImpl extends ServiceImpl<CouponsMapper, Coupons>
    implements CouponsService{

    @Override
    public Boolean createCouponsByCheckGifts(CheckinGifts checkinGifts) {
        Coupons coupons = new Coupons();
        coupons.setCouponName(checkinGifts.getGiftName());
        List<String> giftPhotoUrlList = checkinGifts.getGiftPhotoUrl();
        if(CollectionUtils.isNotEmpty(giftPhotoUrlList)){
            coupons.setImage(giftPhotoUrlList.get(0));
        }
        coupons.setPrice(checkinGifts.getGiftPrice());
        coupons.setDescription(checkinGifts.getGiftDesc());
        return save(coupons);
    }

    @Override
    public Boolean createCouponsByContractGifts(ContractGifts contractGifts) {
        Coupons coupons = new Coupons();
        coupons.setCouponName("签约礼品");
        coupons.setPrice(contractGifts.getPrice());
        coupons.setDescription(contractGifts.getDescription());
        return save(coupons);
    }

    @Override
    public TableDataInfo<CouponVO> queryPage(PageQuery query) {
        IPage<CouponVO> page = baseMapper.selectVoPage(query.build(), buildQueryWrapper(query));
        return TableDataInfo.build(page);
    }

    private LambdaQueryWrapper<Coupons> buildQueryWrapper(PageQuery query){
        LambdaQueryWrapper<Coupons> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(Coupons::getCreateTime);
        return lqw;
    }
}




