package org.dromara.biz.domain.query;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class CustomerServiceQuery extends PageQuery {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 名称
     */
    private String userName;

    /**
     * 手机号
     */
    private String phone;
}
