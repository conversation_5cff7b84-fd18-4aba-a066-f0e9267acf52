package org.dromara.biz.domain.vo;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.biz.domain.CustomerFollowUpLogs;

@Data
@EqualsAndHashCode(callSuper = true)
public class CustomerFollowUpLogsVO extends CustomerFollowUpLogs {

    /**
     * 跟进人姓名
     */
    private String nickname = StrUtil.EMPTY;

    /**
     * 被跟进客户姓名
     */
    private String customerName = StrUtil.EMPTY;

    /**
     * 客户手机号
     */
    private String tel = StrUtil.EMPTY;

    /**
     * 意向度
     */
    private String intentLeve= StrUtil.EMPTY;

    /**
     * 客户标签名称
     */
    private String customerTagName = StrUtil.EMPTY;
}
