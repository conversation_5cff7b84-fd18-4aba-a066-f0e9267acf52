package org.dromara.biz.component.handler.mp;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.dromara.biz.buildr.mp.MpTextBuilder;
import org.dromara.biz.domain.WechatUser;
import org.dromara.biz.domain.WxMpTenant;
import org.dromara.biz.service.WechatUserService;
import org.dromara.biz.service.WxMpTenantService;
import org.dromara.common.core.constant.UserConstants;
import org.dromara.common.core.enums.UserStatus;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@Component
public class SubscribeHandler extends AbstractHandler {

    @Lazy
    @Autowired
    private WechatUserService userService;
    @Lazy
    @Autowired
    private WxMpTenantService wxMpTenantService;

    @Override
    public WxMpXmlOutMessage handle(WxMpXmlMessage wxMessage,
                                    Map<String, Object> context, WxMpService weixinService,
                                    WxSessionManager sessionManager) throws WxErrorException {

        logger.info("新关注用户 OPENID: " + wxMessage.getFromUser());

        // 获取微信用户基本信息
        try {
            WxMpUser userWxInfo = weixinService.getUserService()
                .userInfo(wxMessage.getFromUser());
            logger.info("用户信息: " + JSONUtil.toJsonStr(userWxInfo));
            if (userWxInfo != null) {
                TenantHelper.ignore(()->{
                    String openId = userWxInfo.getOpenId();
                    String unionId = userWxInfo.getUnionId();
                    String appId = weixinService.getWxMpConfigStorage().getAppId();
                    WxMpTenant mpTenant = wxMpTenantService.lambdaQuery()
                        .eq(WxMpTenant::getAppid, appId)
                        .last("limit 1")
                        .one();
                    String tenantId = mpTenant.getTenantId();
                    WechatUser user = userService.lambdaQuery()
                        .eq(WechatUser::getOpenid, openId)
                        .eq(WechatUser::getTenantId, tenantId)
                        .eq(WechatUser::getUtype, "2")
                        .last("limit 1")
                        .one();
                    if(ObjectUtil.isNull(user)){
                        //创建新用户
                        WechatUser u = new WechatUser();
                        u.setOpenid(openId);
                        u.setUnionid(unionId);
                        u.setUtype("2");
                        u.setLastLoginTime(DateUtil.date());
                        u.setTenantId(tenantId);
                        u.setAvatar(UserConstants.DEFAULT_AVATAR);
                        u.setNickname(UserConstants.DEFAULT_NAME);
                        u.setStatus(UserStatus.OK.getCode());
                        u.setAppid(appId);
                        userService.save(u);
                    }
                });
            }
        } catch (WxErrorException e) {
            e.printStackTrace();
            if (e.getError().getErrorCode() == 48001) {
                this.logger.info("该公众号没有获取用户信息权限！");
            }
        }


        WxMpXmlOutMessage responseResult = null;
        try {
            responseResult = this.handleSpecial(wxMessage);
        } catch (Exception e) {
            this.logger.error(e.getMessage(), e);
        }

        if (responseResult != null) {
            return responseResult;
        }

        try {
            return new MpTextBuilder().build("感谢关注", wxMessage, weixinService);
        } catch (Exception e) {
            this.logger.error(e.getMessage(), e);
        }

        return null;
    }

    /**
     * 处理特殊请求，比如如果是扫码进来的，可以做相应处理
     */
    private WxMpXmlOutMessage handleSpecial(WxMpXmlMessage wxMessage)
        throws Exception {
        //TODO
        return null;
    }

}
