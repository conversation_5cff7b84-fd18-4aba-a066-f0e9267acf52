package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.io.Serializable;

/**
 * 房间服务评价待办表
 * @TableName biz_room_todos
 */
@TableName(value ="biz_room_todos")
@Data
@EqualsAndHashCode(callSuper = true)
public class RoomTodos extends TenantEntity implements Serializable {

    /**
     * 主键id
     */
    @TableId
    private Long todoId;

    /**
     * 评价id
     */
    private Long feedbackId;

    /**
     * 回复内容
     */
    private String replyContent;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
