package org.dromara.biz.controller.mp;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.BizFieldConfig;
import org.dromara.biz.domain.Employee;
import org.dromara.biz.domain.vo.nurse.NurseListVO;
import org.dromara.biz.service.FieldConfigService;
import org.dromara.common.core.domain.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 字段配置表 前端控制器
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping("/mp/field/config")
public class BizFieldConfigController {
    private final  FieldConfigService fieldConfigService;
    /**
     * 通过表明获取所有列
     * @param tableName
     * @return
     */
    @GetMapping("/getField")
    public R<List<BizFieldConfig>> getField(@RequestParam("tableName")  String tableName) {
        return R.ok(fieldConfigService.getFiled(tableName));
    }

    /**
     * 更新单个列开启关闭状态
     * @param configId
     * @param isEnabled
     * @return
     */
    @GetMapping("/enabledField")
    public R<Boolean> enabledField(@RequestParam("configId")  Long configId,@RequestParam("isEnabled") Boolean isEnabled) {
        LambdaUpdateWrapper<BizFieldConfig> luw = Wrappers.lambdaUpdate();
        luw.eq(BizFieldConfig::getConfigId, configId);
        luw.set(BizFieldConfig::getIsEnabled, isEnabled);
        return R.ok(fieldConfigService.update(luw));
    }

    /**
     * 查询宝妈护理列表
     * @param momName
     * @return
     */
    @GetMapping("/getNurseList")
    public R<List<NurseListVO>> getNurseList(@RequestParam(name =  "momName",required = false)  String momName) {
        return R.ok(fieldConfigService.getNurseList(momName));
    }

}
