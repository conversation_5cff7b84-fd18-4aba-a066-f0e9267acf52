package org.dromara.biz.service;

import org.dromara.biz.domain.CheckIns;
import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.vo.CheckInVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_check_ins(用户签到表)】的数据库操作Service
* @createDate 2024-03-26 10:00:23
*/
public interface CheckInsService extends IService<CheckIns> {

    /**
     * 用户签到
     * @return 签到结果
     */
    Boolean signIn();

    /**
     * 获取当前用户已签到天数
     * @return 签到天数
     */
    Long getCheckInCount();

    List<Integer> getGiftsDays();

    CheckInVO queryCheckInInfo();
}
