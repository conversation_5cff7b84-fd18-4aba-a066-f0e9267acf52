package org.dromara.biz.controller.pc;

import lombok.RequiredArgsConstructor;
import org.dromara.biz.domain.RoomLevels;
import org.dromara.biz.domain.Rooms;
import org.dromara.biz.domain.bo.RoomBO;
import org.dromara.biz.domain.bo.RoomMoveIntoBO;
import org.dromara.biz.domain.bo.RoomTodoBO;
import org.dromara.biz.domain.query.RoomQuery;
import org.dromara.biz.domain.query.RoomTodoQuery;
import org.dromara.biz.domain.vo.*;
import org.dromara.biz.service.RoomLevelsService;
import org.dromara.biz.service.RoomsService;
import org.dromara.biz.service.impl.RoomLevelsServiceImpl;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.enums.RoomCheckinStatus;
import org.dromara.common.core.enums.TodoStatusEnum;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Comparator;
import java.util.List;

/**
 * 楼层相关接口
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/room_level")
public class RoomLevelController {

    private final RoomsService roomsService;
    private final RoomLevelsService roomLevelsService;

    /**
     * 新增楼层
     * @param roomLevel 楼层信息
     * @return 结果
     */
    @PutMapping("/add_level")
    @RepeatSubmit
    public R<Boolean> saveLevel(@RequestBody LevelVO roomLevel){
        return R.ok(roomLevelsService.save(roomLevel));
    }

    /**
     * 获取楼层列表
     * @return 楼层列表
     */
    @GetMapping("/list_level")
    public R<List<LevelVO>> getLevelList(){
        return R.ok(roomLevelsService.queryList());
    }

    /**
     * 获取楼层房间列表
     * @return 房间列表
     */
    @GetMapping("/list_level_room")
    public R<List<RoomLevelVO>> roomList(){
        List<RoomLevels> levelList = roomLevelsService.list();
        List<RoomLevelVO> result = levelList.stream().map(level -> {
            RoomLevelVO roomLevelVO = new RoomLevelVO();
            List<Rooms> rooms = roomsService.lambdaQuery().eq(Rooms::getLevelId, level.getLevelId()).list();
            roomLevelVO.setRooms(MapstructUtils.convert(rooms, RoomVO.class));
            roomLevelVO.setFloorNumber(level.getFloorNumber());
            roomLevelVO.setLevelId(level.getLevelId());
            return roomLevelVO;
        }).toList();
        result = result.stream()
            .sorted(Comparator.comparing(level->RoomLevelsServiceImpl.convertChineseToNumber(level.getFloorNumber())))
            .toList();
        return R.ok(result);
    }

    /**
     * 新增房间
     * @param room 房间信息
     * @return 结果
     */
    @PutMapping("/add_room")
    @RepeatSubmit
    public R<Boolean> saveRoom(@RequestBody @Validated RoomBO room){
        Rooms rooms = MapstructUtils.convert(room, Rooms.class);
        rooms.setOccupancyStatus(RoomCheckinStatus.NOT_CHECKED_IN.getCode());
        return R.ok(roomsService.saveOrUpdate(rooms));
    }

    /**
     * 修改房间&办理入住
     * @param roomMoveIntoBO 房间信息
     * @return 结果
     */
    @PostMapping
    @RepeatSubmit
    public R<Boolean> update(@RequestBody RoomMoveIntoBO roomMoveIntoBO){
        return R.ok(roomsService.update(roomMoveIntoBO));
    }

    /**
     * 查询房间列表
     */
    @GetMapping("/list")
    public R<List<RoomLevelVO>> list(RoomQuery query) {
        return R.ok(roomsService.queryList(query));
    }

    /**
     * 查询房间信息
     */
    @GetMapping("/info/{roomId}")
    public R<RoomMoveIntoBO> info(@PathVariable Long roomId) {
        return R.ok(roomsService.getByRoomId(roomId));
    }

    /**
     * 退房
     * @param roomId 房间id
     * @return 结果
     */
    @PostMapping("/out/{roomId}")
    public R<Boolean> out (@PathVariable Long roomId){
        return R.ok(roomsService.out(roomId));
    }

    /**
     * 分页查询房间客户入住记录
     * @param query 查询参数
     * @return 结果
     */
    @GetMapping("/checkout_page")
    public TableDataInfo<RoomCheckoutVO> queryRoomCheckoutPage(PageQuery query){
        return roomsService.queryRoomCheckoutPage(query);
    }

    /**
     * 查询房间退房详情信息
     * @param checkoutId 退房id
     * @return 结果
     */
    @GetMapping("/checkout_page/{checkoutId}")
    public R<RoomCheckoutVO> queryRoomCheckoutInfo(@PathVariable Long checkoutId){
        return R.ok(roomsService.queryRoomCheckoutInfo(checkoutId));
    }

    /**
     * 查询房间待办
     */
    @GetMapping("/todo/{roomId}")
    public R<List<RoomTodoVO>> queryTodoList(@PathVariable Long roomId) {
        RoomTodoQuery query = new RoomTodoQuery();
        query.setRoomId(roomId);
        query.setStatus(TodoStatusEnum.PENDING.getCode());
        return R.ok(roomsService.queryTodoList(query));
    }

    /**
     * 回复投诉
     * @param roomTodo 回复内容
     * @return 结果
     */
    @PostMapping("/reply")
    @RepeatSubmit
    public R<Boolean> reply (@RequestBody RoomTodoBO roomTodo){
        return R.ok(roomsService.reply(roomTodo));
    }

    /**
     * 查询楼层下拉选项数据
     * @return 结果
     */
    @GetMapping("/options")
    public R<List<SelectOptionsStringVO>> getSelectOptions(){
        return R.ok(roomLevelsService.getSelectOptions());
    }

    /**
     * 查询服务人员服务的客户下拉选项数据
     * @return 结果
     */
    @GetMapping("/customer_options")
    public R<List<SelectOptionsVO>> getCustomerSelectOptions(@RequestParam("staffUserId") Long staffUserId){
        return R.ok(roomsService.getStaffCustomerSelectOptions(staffUserId));
    }

    /**
     * 查询客户的服务人员下拉选项数据
     * @return 结果
     */
    @GetMapping("/staff_options")
    public R<List<SelectOptionsVO>> getCustomerServicePersonnelOptions(@RequestParam("customerId") Long customerId){
        return R.ok(roomsService.getCustomerServicePersonnelOptions(customerId));
    }
}
