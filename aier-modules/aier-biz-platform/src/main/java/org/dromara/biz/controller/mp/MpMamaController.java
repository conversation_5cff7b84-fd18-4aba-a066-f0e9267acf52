package org.dromara.biz.controller.mp;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.query.FeedPostWeekQuery;
import org.dromara.biz.domain.vo.customer.MamaInfoVO;
import org.dromara.biz.domain.vo.feedpost.FeedPostDayVO;
import org.dromara.biz.domain.vo.feedpost.FeedPostWeekVO;
import org.dromara.biz.service.MamaService;
import org.dromara.common.core.domain.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 小程序宝妈相关接口
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping("/mp/mama")
public class MpMamaController {

    private final MamaService mamaService;

    /**
     * 查询宝妈信息
     */
    @GetMapping("/info")
    public R<MamaInfoVO> getMamaInfo(@RequestParam("userId") Long userId) {
        return R.ok(mamaService.getMamaInfo(userId));
    }

    /**
     * 查询宝妈动态列表 包含宝妈自己发布的和工作人员发布关联宝妈的动态
     */
    @GetMapping("/post_list")
    public R<List<FeedPostWeekVO>> getMamaPostList(FeedPostWeekQuery query) {
        return R.ok(mamaService.getMamaFeedPostWeek(query));
    }

    /**
     * 查询宝妈动态列表 包含宝妈自己发布的和工作人员发布关联宝妈的动态
     */
    @GetMapping("/post_list_day")
    public R<List<FeedPostDayVO>> getMamaDayPostList(FeedPostWeekQuery query) {
        return R.ok(mamaService.getMamaDayPostList(query));
    }
}
