package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 会所朋友圈点赞表
 * @TableName biz_feed_post_links
 */
@TableName(value ="biz_feed_post_links")
@Data
public class FeedPostLinks implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.AUTO)
    private Long linkId;

    /**
     * 点赞时间
     */
    private Date linkTime;

    /**
     * 点赞用户id
     */
    private Long userId;

    /**
     * 动态id
     */
    private Long postId;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}