package org.dromara.biz.domain.vo;

import lombok.Data;

import java.util.Date;

/**
 * 系统用户实体类 用来判断权限
 */
@Data
public class SystemUser {
    private Long userId;
    private Long tenantId;
    private Long deptId;
    private String userName;
    private String nickName;
    private String userType;
    private String email;
    private String avatar;
    private String phoneNumber;
    private String password;
    private String sex;
    private String status;
    private String delFlag;
    private String loginIp;
    private Date loginDate;
    private String createBy;
    private Date createTime;
    private String remark;
    private SystemRole systemRole;
}
