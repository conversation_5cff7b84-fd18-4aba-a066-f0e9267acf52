package org.dromara.biz.controller.pc;

import lombok.RequiredArgsConstructor;
import org.dromara.biz.domain.DefaultCustomers;
import org.dromara.biz.domain.DefaultCustomersStaff;
import org.dromara.biz.domain.bo.DefaultCustomerStaffBO;
import org.dromara.biz.service.DefaultCustomersService;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 默认客户controller
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/default_customer")
public class DefaultCustomerController {

    private final DefaultCustomersService defaultCustomersService;

    /**
     * 分页查询
     */
    @GetMapping("/page")
    public TableDataInfo<DefaultCustomers> page(PageQuery query) {
        return TableDataInfo.build(defaultCustomersService.page(query.build()));
    }

    /**
     * 查询详情
     */
    @GetMapping("/detail/{id}")
    public R<DefaultCustomers> detail(@PathVariable Long id) {
        DefaultCustomers defaultCustomer = defaultCustomersService.getById(id);
        List<DefaultCustomersStaff> bindStaffList = defaultCustomersService.getBindStaffList(id);
        defaultCustomer.setStaffIdList(bindStaffList.stream().map(DefaultCustomersStaff::getDefaultStaffId).toList());
        return R.ok(defaultCustomer);
    }

    /**
     * 新增默认客户
     * @param defaultCustomer 客户信息
     * @return 新增结果
     */
    @PostMapping("/add")
    @RepeatSubmit
    public R<Boolean> add(@RequestBody @Validated DefaultCustomers defaultCustomer) {
        boolean result = defaultCustomersService.save(defaultCustomer);
        DefaultCustomerStaffBO defaultCustomerStaffBO = new DefaultCustomerStaffBO();
        defaultCustomerStaffBO.setCustomerId(defaultCustomer.getId());
        defaultCustomerStaffBO.setDefaultStaffIds(defaultCustomer.getStaffIdList());
        defaultCustomersService.bindStaff(defaultCustomerStaffBO);
        return R.ok(result);
    }

    /**
     * 修改默认客户
     * @param defaultCustomer 客户信息
     * @return 修改结果
     */
    @PutMapping("/update")
    @RepeatSubmit
    public R<Boolean> update(@RequestBody @Validated DefaultCustomers defaultCustomer) {
        defaultCustomersService.updateById(defaultCustomer);
        DefaultCustomerStaffBO defaultCustomerStaffBO = new DefaultCustomerStaffBO();
        defaultCustomerStaffBO.setCustomerId(defaultCustomer.getId());
        defaultCustomerStaffBO.setDefaultStaffIds(defaultCustomer.getStaffIdList());
        defaultCustomersService.bindStaff(defaultCustomerStaffBO);
        return R.ok(true);
    }

    /**
     * 删除默认客户
     * @param id 客户id
     * @return 结果
     */
    @DeleteMapping("/{id}")
    public R<Boolean> remove(@PathVariable Long id){
        return R.ok(defaultCustomersService.removeById(id));
    }

    /**
     * 绑定服务人员
     * @param defaultCustomerStaffBO 绑定信息
     * @return 绑定结果
     */
    @PostMapping("/bind_staff")
    @RepeatSubmit
    public R<Boolean> bindStaff(@RequestBody @Validated DefaultCustomerStaffBO defaultCustomerStaffBO) {
        return R.ok(defaultCustomersService.bindStaff(defaultCustomerStaffBO));
    }

    /**
     * 查询员工绑定客户列表
     */
    @GetMapping("/customers_staff_list/{id}")
    public R<List<DefaultCustomersStaff>> getBindStaffList(@PathVariable Long id) {
        return R.ok(defaultCustomersService.getBindStaffList(id));
    }
}
