package org.dromara.biz.controller.pc;

import cn.dev33.satoken.stp.StpUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.CustomerReminders;
import org.dromara.biz.domain.bo.CustomerCreateBO;
import org.dromara.biz.domain.params.customer.*;
import org.dromara.biz.domain.query.CustomerQuery;
import org.dromara.biz.domain.query.WechatUserQuery;
import org.dromara.biz.domain.query.customer.CustomerFollowLogQuery;
import org.dromara.biz.domain.query.customer.CustomerOperationLogQuery;
import org.dromara.biz.domain.vo.CustomerFollowUpLogsVO;
import org.dromara.biz.domain.vo.CustomerOperationLogsVO;
import org.dromara.biz.domain.vo.CustomerVO;
import org.dromara.biz.domain.vo.WechatUserVO;
import org.dromara.biz.service.CustomerRemindersService;
import org.dromara.biz.service.CustomersService;
import org.dromara.biz.service.WechatUserService;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 客资跟踪相关接口
 */
@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping("/platform/customer_track")
@Validated
public class CustomerTrackController {

    private final CustomersService customersService;
    private final WechatUserService wechatUserService;
    private final CustomerRemindersService customerRemindersService;

    /**
     * 新增客资线索
     */
    @PostMapping("/create_customer")
    public R<Boolean> createCustomer(@RequestBody CustomerCreateBO customer){
        return R.ok(customersService.createCustomer(customer));
    }

    /**
     * 创建提醒事项
     */
    @PostMapping("/create_reminder")
    public R<Boolean> createReminder(@RequestBody ReminderParams reminderParams){
        return R.ok(customersService.createReminder(reminderParams));
    }

    /**
     * 获取我的提醒事项
     */
    @GetMapping("/get_my_reminders")
    public R<List<CustomerReminders>> getMyReminders(){
        return R.ok(customersService.getMyReminders());
    }

    /**
     * 获取提醒事项详情
     * @param reminderId 提醒事项id
     */
    @GetMapping("/get_reminder_by_id")
    public R<CustomerReminders> getReminderById(@RequestParam("reminderId") Long reminderId){
        return R.ok(customerRemindersService.getDetail(reminderId));
    }

    /**
     * 分页查询跟进记录列表
     */
    @GetMapping("/followed_logs")
    public TableDataInfo<CustomerFollowUpLogsVO> getFollowedLogs(CustomerFollowLogQuery params) {
        return customersService.getFollowedLogs(params);
    }

    /**
     * 分页查询操作记录列表
     */
    @GetMapping("/operation_logs")
    public TableDataInfo<CustomerOperationLogsVO> getOperationLogs(CustomerOperationLogQuery params) {
        return customersService.getOperationLogs(params);
    }

    /**
     * 分页查询客资列表
     */
    @GetMapping("/page")
    public TableDataInfo<CustomerVO> page(CustomerQuery query) {
        if(StpUtil.hasRole("SALES")){
            Long userId = LoginHelper.getUserId();
            query.setFollowerId(userId);
        }
        return customersService.queryPage(query);
    }

    /**
     * 分页查询销售员列表
     */
    @GetMapping("/user_page")
    public TableDataInfo<WechatUserVO> wechatUserPage(WechatUserQuery query) {
        return wechatUserService.querySalesPage(query);
    }
}
