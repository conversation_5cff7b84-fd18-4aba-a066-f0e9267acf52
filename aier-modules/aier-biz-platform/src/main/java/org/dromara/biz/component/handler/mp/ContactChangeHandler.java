package org.dromara.biz.component.handler.mp;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.cp.bean.message.WxCpXmlMessage;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import org.dromara.biz.buildr.mp.TextBuilder;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 通讯录变更事件处理器.
 *
 * <AUTHOR> href="https://github.com/binarywang">Binary Wang</a>
 */
@Slf4j
@Component
public class ContactChangeHandler extends AbstractHandler {

//    public WxMpXmlOutMessage handle(WxCpXmlMessage wxMessage, Map<String, Object> context, WxMpService cpService,
//                                    WxSessionManager sessionManager) {
//        String content = "收到通讯录变更事件，内容：" + JsonUtils.toJson(wxMessage);
//        log.info(content);
//
//        return new TextBuilder().build(content, wxMessage, cpService);
//    }

    @Override
    public WxMpXmlOutMessage handle(WxMpXmlMessage wxMessage, Map<String, Object> context, WxMpService wxMpService, WxSessionManager sessionManager) throws WxErrorException {
        return null;
    }
}
