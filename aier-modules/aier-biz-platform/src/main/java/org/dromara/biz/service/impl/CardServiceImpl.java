package org.dromara.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.biz.domain.Card;
import org.dromara.biz.domain.vo.card.UserCardVO;
import org.dromara.biz.mapper.CardMapper;
import org.dromara.biz.service.CardService;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_card】的数据库操作Service实现
* @createDate 2025-02-25 17:37:05
*/
@Service
public class CardServiceImpl extends ServiceImpl<CardMapper, Card>
    implements CardService{

    @Override
    public List<UserCardVO> myCollect() {
        Long userId  = LoginHelper.getUserId();
        if (userId == null) {
            return null;
        }
        return baseMapper.myCollect(userId);
    }

    @Override
    public Boolean removeByCardId(Long cardId) {
        return removeById(cardId);
    }
}




