package org.dromara.biz.domain.query;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * 朋友圈查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FeedPostQuery extends PageQuery {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 入住时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date date;

    /**
     * 房间id
     */
    private Long roomId;

    /**
     * 节点id 单节点查询传
     */
    private Long nodeId;

    /**
     * 节点类型 单节点查询传
     */
    private String nodeType;

    /**
     * 节点id列表 多节点查询传
     */
    private List<Long> taskNodeIdList;

    /**
     * 节点类型 多类型查询传
     */
    private List<String> nodeTypeList;

    /**
     * 动态类型 STAFF=员工动态；USER=客户动态；CLUB=会所动态
     */
    private String postType;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 发布者id
     */
    private Long authorId;

    /**
     * 是否有附件 图片和视频都为附件
     */
    private Boolean isAttachments;

    /**
     * 是否精选动态
     */
    private Boolean isFeatured;

    /**
     * 排除的动态id集合
     */
    private List<Long> notPostIds;

    /**
     * 周数 以入住时间为开始计算周数 周数需要依赖入住时间 传入此参数入住时间为必填参数
     */
    private Integer weekNum;

    /**
     * 入住时间 需要与周数参数配合使用 单独传入不生效
     */
    private Date checkInTime;

    /**
     * 查询宝妈和为宝妈服务的员工动态
     */
    private Long userAndEmployee;
}
