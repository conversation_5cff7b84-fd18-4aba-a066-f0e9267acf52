package org.dromara.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.biz.domain.BizDiary;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.dromara.biz.domain.CustomerContract;
import org.dromara.biz.domain.vo.DiaryVO;
import org.dromara.biz.domain.vo.WechatUserVO;
import org.dromara.biz.domain.vo.customer.CustomerContractVO;

import java.util.List;

/**
 * <p>
 * 宝妈笔记表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
public interface BizDiaryMapper extends BaseMapper<BizDiary> {

    Page<DiaryVO> selectDiaryPage(Page<DiaryVO> page, @Param(Constants.WRAPPER) QueryWrapper<BizDiary> wrapper);

    DiaryVO getDetail(String diaryId);

}
