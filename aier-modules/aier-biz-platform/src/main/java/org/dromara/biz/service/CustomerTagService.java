package org.dromara.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.CustomerTag;
import org.dromara.biz.domain.CustomerTagConsultation;
import org.dromara.biz.domain.query.customer.CustomerTagQuery;
import org.dromara.biz.domain.vo.customer.CustomerTagVO;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_customer_tag(客户标签表)】的数据库操作Service
* @createDate 2024-08-22 14:20:27
*/
public interface CustomerTagService extends IService<CustomerTag> {

    Boolean saveCustomerTag(CustomerTagVO vo);

    Boolean updateCustomerTag(CustomerTagVO vo);

    TableDataInfo<CustomerTagVO> queryPage(CustomerTagQuery query);

    CustomerTagVO detail(Long id);

    Boolean delete(Long id);

    Boolean updateConsultation(CustomerTagConsultation tagConsultation);

    List<CustomerTagVO> queryList(CustomerTagQuery query);

    String getConsultation();
}
