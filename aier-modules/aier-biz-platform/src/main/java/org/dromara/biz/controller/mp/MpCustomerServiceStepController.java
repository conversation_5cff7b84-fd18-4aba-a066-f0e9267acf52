package org.dromara.biz.controller.mp;

import cn.dev33.satoken.annotation.SaIgnore;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.TaskNode;
import org.dromara.biz.domain.vo.CustomerServiceStepVO;
import org.dromara.biz.domain.vo.SelectOptionsStringVO;
import org.dromara.biz.domain.vo.TaskNodeVO;
import org.dromara.biz.service.RoomsService;
import org.dromara.biz.service.TaskNodeService;
import org.dromara.common.core.domain.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 客户服务过程相关接口
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping("/mp/customer_service_step")
public class MpCustomerServiceStepController {

    private final RoomsService roomsService;
    private final TaskNodeService taskNodeService;

    /**
     * 获取当前服务人员可用的客户列表
     */
    @GetMapping("/get_customer_list")
    public R<List<SelectOptionsStringVO>> getCustomerList(){
        return R.ok(roomsService.getCurrentStaffServiceCustomerOptions());
    }

    /**
     * 获取所有节点下拉框选项列表
     */
    @GetMapping("/get_step_node_list")
    public R<List<SelectOptionsStringVO>> getStepNodeList(){
        List<TaskNode> taskNodeList = taskNodeService.list();
        List<SelectOptionsStringVO> options = taskNodeList.stream().map(node -> {
            SelectOptionsStringVO vo = new SelectOptionsStringVO();
            vo.setLabel(node.getNodeName());
            vo.setValue(node.getTaskNodeId().toString());
            return vo;
        }).toList();
        return R.ok(options);
    }

    /**
     * 获取所有节点列表
     */
    @GetMapping("/get_all_step_node_list")
    public R<List<TaskNodeVO>> getAllStepNodeList(){
        return R.ok(taskNodeService.queryList());
    }

    /**
     * 获取用户服务过程天数以及对应的节点
     * @param userId 用户ID
     * @param group 查询组 day-按天查询 week-按周查询 month-按月查询 all-全部
     */
    @GetMapping("/get_customer_service_step")
    @SaIgnore
    public R<List<CustomerServiceStepVO>> getCustomerServiceStepList(Long userId,
                                                                     @RequestParam(defaultValue = "all") String group){
        group = "all";
        return R.ok(taskNodeService.getCustomerServiceStepList(userId, group));
    }

    /**
     * 获取用户服务过程中工作人员对其发布的所有节点
     */
    @GetMapping("/get_customer_service_node")
    public R<List<TaskNodeVO>> getCustomerServiceNodeList(Long userId){
        return R.ok(taskNodeService.getListByUserId(userId));
    }
}
