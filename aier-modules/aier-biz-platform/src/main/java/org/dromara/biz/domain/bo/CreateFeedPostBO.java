package org.dromara.biz.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.core.validate.EditGroup;

import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class CreateFeedPostBO extends FeedPostBO{

    /**
     * 动态配置
     */
    private List<Long> config;

    /**
     * 动态发布日期
     */
    private Date date;

    /**
     * 动态id
     */
    @NotNull(groups = EditGroup.class, message = "动态id不能为空")
    private Long postId;

    /**
     * 发布会所动态时需要传入authorId
     */
    private Long authorId;
}
