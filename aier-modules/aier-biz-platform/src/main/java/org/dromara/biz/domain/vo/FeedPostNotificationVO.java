package org.dromara.biz.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.biz.domain.FeedPostNotification;
import org.dromara.common.mybatis.core.domain.BaseEntity;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 会所我的消息
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = FeedPostNotification.class)
public class FeedPostNotificationVO extends BaseEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 通知id
     */
    private Long notificationId;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 动态
     */
    private Long postId;

    /**
     * 消息
     */
    private String message;

    /**
     * 动态内容
     */
    private String postContent;

    /**
     * 动态图片
     */
    private List<String> postPhotos;

    /**
     * 发布者
     */
    private String author;

    /**
     * 发布者头像
     */
    private String authorAvatar;

    /**
     * 时间字符串 xx小时前 or xx分钟前
     */
    private String hisTimeStr;
}
