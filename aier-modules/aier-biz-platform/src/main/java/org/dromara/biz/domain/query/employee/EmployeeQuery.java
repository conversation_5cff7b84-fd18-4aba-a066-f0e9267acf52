package org.dromara.biz.domain.query.employee;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.page.PageQuery;

/**
 * 新增员工bo
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EmployeeQuery extends PageQuery {


    /**
     * 关键字
     */
    private String keyword;

    /**
     * NURSE("护理"),
     * POSTPARTUM("产康"),
     * CHEF("厨师"),
     * MATERNITY_NANNY("月嫂"),
     * SALES("销售"),
     */
    private String roleCode;

    /**
     * 审核状态
     */
    private Integer auditStatus = 1;
}
