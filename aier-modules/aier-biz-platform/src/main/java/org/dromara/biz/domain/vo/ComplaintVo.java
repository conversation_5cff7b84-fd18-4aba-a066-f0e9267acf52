package org.dromara.biz.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.biz.domain.BizComplaint;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@AutoMapper(target = BizComplaint.class)
public class ComplaintVo implements Serializable {
    private Long complaintId;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 内容
     */
    private String content;
    /**
     *内容照片
     */
    private List<String> contentPhotos;
    /**
     * 状态：1处理中，2已处理
     */
    private Integer status;
    /**
        * 反馈处理人
     */
    private Long feedbackTransactor;

    /**
     * 反馈处理人名称
     */
    private String feedbackTransactorName;

    /**
     * 反馈时间
     */
    private Date feedbackTime;

    /**
     * 反馈内容
     */
    private String feedbackContent;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 入住房号
     */
    private String roomNumber;
}
