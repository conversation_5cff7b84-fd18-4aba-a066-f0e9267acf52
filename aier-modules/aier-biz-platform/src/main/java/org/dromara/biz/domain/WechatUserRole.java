package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 微信用户角色绑定表
 * @TableName biz_wechat_user_role
 */
@TableName(value ="biz_wechat_user_role")
@Data
public class WechatUserRole implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 角色id
     */
    private Long roleId;

    private String tenantId;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}