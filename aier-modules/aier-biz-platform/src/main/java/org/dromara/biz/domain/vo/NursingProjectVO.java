package org.dromara.biz.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.dromara.biz.domain.NursingProjects;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 护理项目vo
 */
@Data
@AutoMapper(target = NursingProjects.class)
public class NursingProjectVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long projectId;

    /**
     * 护理项目名称
     */
    @NotBlank(message = "护理项目名称不能为空")
    private String projectName;

    /**
     * 护理项目类别
     */
    private String category;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
