package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.handler.type.StringListTypeHandler;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 月子套房表
 * @TableName biz_suites
 */
@TableName(value ="biz_suites", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class Suites extends TenantEntity implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long suiteId;

    /**
     * 房间名称
     */
    private String roomName;

    /**
     * 房型
     */
    private String roomType;

    /**
     * 朝向
     */
    private String orientation;

    /**
     * 床型
     */
    private String bedType;

    /**
     * 最低楼层
     */
    private Integer minFloor;

    /**
     * 最高楼层
     */
    private Integer maxFloor;

    /**
     * 最小面积
     */
    private String minArea;

    /**
     * 最大面积
     */
    private String maxArea;

    /**
     * 室外景观
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> outdoorFeatures;

    /**
     * 便利设施
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> facilityFeatures;

    /**
     * 媒体娱乐
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> mediaFeatures;

    /**
     * 卫浴配套
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> bathroomFacilities;

    /**
     * 套房照片列表
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> suitePhotos;

    /**
     * 套房视频列表
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> suiteVideos;

    /**
     * 打扫频次
     */
    private String cleaningFrequency;

    /**
     * 换床单频次
     */
    private String sheetChangeFrequency;

    /**
     * 消毒频次
     */
    private String disinfectionFrequency;

    /**
     * 套房描述
     */
    private String description;

    /**
     * 房型标签
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> tag;

    /**
     * 在线状态
     */
    private Boolean onlineStatus;
    /**
     * 展示状态
     */
    private Boolean isShow;

    /**
     * 签约礼品id
     */
    private Long contractGiftId;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 图文详情
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> textPhotos;

    /**
     * 是否推荐房型
     */
    private Boolean isRecommended;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
