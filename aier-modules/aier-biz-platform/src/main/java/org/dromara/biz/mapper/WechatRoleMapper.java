package org.dromara.biz.mapper;

import org.dromara.biz.domain.WechatRole;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_wechat_role(小程序角色表)】的数据库操作Mapper
* @createDate 2024-09-19 20:26:39
* @Entity org.dromara.biz.domain.WechatRole
*/
public interface WechatRoleMapper extends BaseMapper<WechatRole> {

    /**
     * 根据用户id查询角色
     */
    List<WechatRole> selectRolePermissionByUserId(Long userId);

    /**
     * 根据用户id查询权限
     */
    List<String> selectPermsByUserId(Long userId);
}




