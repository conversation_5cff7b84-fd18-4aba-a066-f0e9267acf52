package org.dromara.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.biz.domain.CustomerIntentLeveRecord;
import org.dromara.biz.service.CustomerIntentLeveRecordService;
import org.dromara.biz.mapper.CustomerIntentLeveRecordMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_customer_intent_leve_record(客户意向度变更记录)】的数据库操作Service实现
* @createDate 2024-08-23 11:55:19
*/
@Service
public class CustomerIntentLeveRecordServiceImpl extends ServiceImpl<CustomerIntentLeveRecordMapper, CustomerIntentLeveRecord>
    implements CustomerIntentLeveRecordService{

    @Override
    public List<CustomerIntentLeveRecord> getByCustomerId(Long customerId) {
        LambdaQueryWrapper<CustomerIntentLeveRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(CustomerIntentLeveRecord::getCustomerId, customerId);
        return list(lqw);
    }
}




