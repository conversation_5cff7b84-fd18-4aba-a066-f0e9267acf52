package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 房间入住客户服务人员表
 * @TableName biz_room_occupation_fws
 */
@TableName(value ="biz_room_occupation_fws")
@Data
public class RoomOccupationFws implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 入住记录id
     */
    private Long occupationId;

    /**
     * 服务人员id
     */
    private Long employeeId;

    /**
     * 服务时间
     */
    private Date serviceTime;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 
     */
    private Date createTime;

    /**
     * 
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}