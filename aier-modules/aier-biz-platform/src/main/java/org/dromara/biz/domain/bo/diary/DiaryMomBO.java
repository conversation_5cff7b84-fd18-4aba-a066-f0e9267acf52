package org.dromara.biz.domain.bo.diary;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.dromara.biz.domain.BizDiaryItem;
import org.dromara.biz.domain.BizDiaryMom;
import org.dromara.common.web.annotation.MobilePhone;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
@AutoMapper(target = BizDiaryMom.class, reverseConvertGenerate = false)
public class DiaryMomBO {
    private Long momId;

    /**
     * 宝妈名称
     */
    private String name;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @MobilePhone(message = "手机号格式错误")
    private String tel;

    /**
     * 年龄
     */
    private String age;

    /**
     * 宝宝体重
     */
    private String babyWeight;

    /**
     * 入住时间

     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date serviceStartDate;

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 是否推荐
     */
    private Boolean isShow;

    private Long customerId;

    private List<Long> employeeIds;

    /**
     * 头像
     */
    private String avatar;
    /**
     *唯一表示（用于微信小程序码生成）
     */
    private String uuid;
}
