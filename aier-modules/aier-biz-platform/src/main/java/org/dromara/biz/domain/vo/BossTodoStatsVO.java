package org.dromara.biz.domain.vo;

import cn.hutool.core.util.ObjectUtil;
import lombok.Data;

/**
 * 老板端待办数量统计vo
 */
@Data
public class BossTodoStatsVO {

    /**
     * 待处理数量
     */
    private Integer processed = 0;
    /**
     * 处理中数量
     */
    private Integer processedIng = 0;
    /**
     * 已经处理数量
     */
    private Integer processedNot = 0;

    /**
     * 总数
     */
    private Integer allProcessed = 0;

    /**
     * 获取待办总数
     * @return 待办总数
     */
    public Integer getAllProcessed(){
        return this.processed + this.processedIng + this.processedNot;
    }
}
