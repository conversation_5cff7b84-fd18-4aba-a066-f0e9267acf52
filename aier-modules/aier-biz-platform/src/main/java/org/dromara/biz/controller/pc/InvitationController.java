package org.dromara.biz.controller.pc;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.query.invitation.InvitationQuery;
import org.dromara.biz.domain.vo.invitation.InvitationTemplateVO;
import org.dromara.biz.service.InvitationTemplateService;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.web.bind.annotation.*;

/**
 * 请帖相关接口
 */
@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping("/platform/invitation")
public class InvitationController {

    private final InvitationTemplateService templateService;

    /**
     * 分页查询请帖模版列表
     */
    @GetMapping("/template/page")
    public TableDataInfo<InvitationTemplateVO> queryTemplatePage(InvitationQuery query){
       return templateService.queryPage(query);
    }

    /**
     * 查询请帖模版详情
     */
    @GetMapping("/template/detail/{id}")
    public R<InvitationTemplateVO> getTemplateDetail(@PathVariable Long id){
          return R.ok(templateService.getTemplateDetail(id));
    }

    /**
     * 新增请帖模版
     **/
    @PostMapping("/template/save")
    public R<Void> templateSave(@RequestBody InvitationTemplateVO templateVO){
        templateService.save(templateVO);
        return R.ok();
    }

    /**
     * 修改请帖模版
     **/
    @PostMapping("/template/update")
    public R<Void> templateUpdate(@RequestBody InvitationTemplateVO templateVO){
        templateService.update(templateVO);
        return R.ok();
    }

    /**
     * 更新请帖模板状态
     */
    @PutMapping("/template/{id}/status")
    public R<Void> updateTemplateStatus(@RequestParam Boolean status, @PathVariable Long id) {
        templateService.updateTemplateStatus(id, status);
        return R.ok();
    }
}
