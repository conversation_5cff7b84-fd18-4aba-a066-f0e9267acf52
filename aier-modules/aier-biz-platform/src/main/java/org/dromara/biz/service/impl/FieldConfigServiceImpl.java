package org.dromara.biz.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.dromara.biz.domain.BizFieldConfig;
import org.dromara.biz.domain.vo.nurse.NurseListVO;
import org.dromara.biz.mapper.BizFieldConfigMapper;
import org.dromara.biz.service.FieldConfigService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.common.core.enums.FiledTableEnum;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 字段配置表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-13
 */
@Service
public class FieldConfigServiceImpl extends ServiceImpl<BizFieldConfigMapper, BizFieldConfig> implements FieldConfigService {

    @Override
    public List<BizFieldConfig> getFiled(String tableName) {
        List<BizFieldConfig> configList = baseMapper.selectList(Wrappers.<BizFieldConfig>lambdaQuery().eq(BizFieldConfig::getTableName, tableName));
        if (CollUtil.isNotEmpty(configList)) {
            return configList;
        }
        configList = initFoundationFiled(tableName);
        return configList;
    }

    @Override
    public List<NurseListVO> getNurseList(String momName) {
        if(StpUtil.hasRole("NURSE")){
            Long userId = LoginHelper.getUserId();
            return baseMapper.getNurseList(momName,userId);
        }
        return baseMapper.getNurseList(momName,null);
    }


    /**
     * 初始化列数据
     * @param tableName
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<BizFieldConfig> initFoundationFiled(String tableName) {
        List<BizFieldConfig> configList = CollUtil.newArrayList();

        if (tableName.equals(FiledTableEnum.MOTHER_CARE.getTableName())){
            //宝妈护理
            configList.add(new BizFieldConfig(tableName, "customerId", "客户ID", true));
            configList.add(new BizFieldConfig(tableName, "recordTime", "记录时间", true));
            configList.add(new BizFieldConfig(tableName, "checkinTemperature", "入住体温（单位：摄氏度）", true));
            configList.add(new BizFieldConfig(tableName, "checkinWeight", "入住重量（单位：千克）", true));
            configList.add(new BizFieldConfig(tableName, "systolicPressure", "收缩压（单位：mmHg）", true));
            configList.add(new BizFieldConfig(tableName, "diastolicPressure", "舒张压（单位：mmHg）", true));
            configList.add(new BizFieldConfig(tableName, "bloodSugar", "血糖（单位：mmol/L）", true));
            configList.add(new BizFieldConfig(tableName, "bloodPressure", "血压（单位：mmHg）", true));
            configList.add(new BizFieldConfig(tableName, "sleepCondition", "睡眠情况", true));
            configList.add(new BizFieldConfig(tableName, "dietCondition", "饮食情况", true));
            configList.add(new BizFieldConfig(tableName, "appetite", "食欲", true));
            configList.add(new BizFieldConfig(tableName, "breastCondition", "乳房情况", true));
            configList.add(new BizFieldConfig(tableName, "milkCondition", "乳汁情况", true));
            configList.add(new BizFieldConfig(tableName, "nippleCondition", "乳头情况", true));
            configList.add(new BizFieldConfig(tableName, "moodCondition", "情绪情况", true));
            configList.add(new BizFieldConfig(tableName, "uterineCondition", "宫体情况", true));
            configList.add(new BizFieldConfig(tableName, "lochiaCondition", "恶露情况", true));
            configList.add(new BizFieldConfig(tableName, "lochiaColor", "恶露颜色", true));
            configList.add(new BizFieldConfig(tableName, "diastasisRecti", "腹直肌分离（单位：厘米）", true));
            configList.add(new BizFieldConfig(tableName, "woundCondition", "伤口情况（顺/剖）", true));
            configList.add(new BizFieldConfig(tableName, "hemorrhoids", "痔疮情况", true));
            configList.add(new BizFieldConfig(tableName, "lowerLimbEdema", "下肢水肿情况", true));
            configList.add(new BizFieldConfig(tableName, "medication", "用药情况", true));
            configList.add(new BizFieldConfig(tableName, "notes", "备注信息", true));
            configList.add(new BizFieldConfig(tableName, "photoList", "照片列表（存储图片路径或URL，JSON格式）", true));
            saveBatch(configList);

        }
        if (tableName.equals(FiledTableEnum.MOTHER_ROUNDS.getTableName())){
            //宝妈查房
            configList.add(new BizFieldConfig(tableName, "customerId", "客户ID", true));
            configList.add(new BizFieldConfig(tableName, "roomNumber", "入住房号", true));
            configList.add(new BizFieldConfig(tableName, "recordTime", "记录时间", true));
            configList.add(new BizFieldConfig(tableName, "deliveryDays", "分娩天数（天）", true));
            configList.add(new BizFieldConfig(tableName, "deliveryMethod", "生产方式（如：顺产、剖宫产）", true));
            configList.add(new BizFieldConfig(tableName, "breastCondition", "乳房状况", true));
            configList.add(new BizFieldConfig(tableName, "checkinWeight", "入住体重（单位：千克）", true));
            configList.add(new BizFieldConfig(tableName, "dietCondition", "饮食情况", true));
            configList.add(new BizFieldConfig(tableName, "lochiaCondition", "恶露情况", true));
            configList.add(new BizFieldConfig(tableName, "lochiaColor", "恶露颜色", true));
            configList.add(new BizFieldConfig(tableName, "digestiveSystem", "消化系统（排便）", true));
            configList.add(new BizFieldConfig(tableName, "bodyFeeling", "体感状况（冷热）", true));
            configList.add(new BizFieldConfig(tableName, "lumbarSacralCondition", "腰骶情况", true));
            configList.add(new BizFieldConfig(tableName, "notes", "备注信息", true));
            configList.add(new BizFieldConfig(tableName, "photoList", "图片列表（存储图片路径或URL，JSON格式）", true));
            saveBatch(configList);

        }

        if (tableName.equals(FiledTableEnum.BABY_INFO.getTableName())){
            //宝宝信息
            configList.add(new BizFieldConfig(tableName,"babyId","宝宝ID",true));
            configList.add(new BizFieldConfig(tableName,"customerId","客户ID",true));
            configList.add(new BizFieldConfig(tableName,"fetusNumber","胎数",true));
            configList.add(new BizFieldConfig(tableName,"gender","性别",true));
            configList.add(new BizFieldConfig(tableName,"deliveryHospital","宝宝姓名",true));
            configList.add(new BizFieldConfig(tableName,"birthDate","出生日期",true));
            configList.add(new BizFieldConfig(tableName,"birthWeight","出生体重",true));
            configList.add(new BizFieldConfig(tableName,"birthHeight","出生身高",true));
            configList.add(new BizFieldConfig(tableName,"headCircumference","头围",true));
            configList.add(new BizFieldConfig(tableName,"deliveryMethod","出生方式",true));
            configList.add(new BizFieldConfig(tableName,"vaccinationRecords","接种疫苗记录",true));
            configList.add(new BizFieldConfig(tableName,"remarks","备注信息",true));
            configList.add(new BizFieldConfig(tableName,"photoPath","照片存储路径",true));
            saveBatch(configList);
        }
        if (tableName.equals(FiledTableEnum.BABY_DIAPER.getTableName())){
            //宝宝尿布记录表
            configList.add(new BizFieldConfig(tableName, "babyId", "宝宝ID", true));
            configList.add(new BizFieldConfig(tableName, "recordTime", "记录时间", true));
            configList.add(new BizFieldConfig(tableName, "stoolColor", "大便颜色", true));
            configList.add(new BizFieldConfig(tableName, "stoolConsistency", "大便性状", true));
            configList.add(new BizFieldConfig(tableName, "stoolAmount", "大便量", true));
            configList.add(new BizFieldConfig(tableName, "diaperChangeCount", "换尿不湿次数", true));
            configList.add(new BizFieldConfig(tableName, "urine", "小便情况", true));
            configList.add(new BizFieldConfig(tableName, "urineCount", "小便次数", true));
            configList.add(new BizFieldConfig(tableName, "abnormalCondition", "异常情况", true));
            configList.add(new BizFieldConfig(tableName, "notes", "备注信息", true));
            configList.add(new BizFieldConfig(tableName, "photoList", "照片列表（存储图片路径或URL，JSON格式）", true));
            saveBatch(configList);
        }
        if (tableName.equals(FiledTableEnum.BABY_ROUTINE.getTableName())){
            //宝宝常规记录表
            configList.add(new BizFieldConfig(tableName, "babyId", "宝宝ID", true));
            configList.add(new BizFieldConfig(tableName, "recordTime", "记录时间", true));
            configList.add(new BizFieldConfig(tableName, "motherBabySendTime", "母婴同房送去时间", true));
            configList.add(new BizFieldConfig(tableName, "motherBabyReceiveTime", "母婴同房接回时间", true));
            configList.add(new BizFieldConfig(tableName, "temperature", "体温（单位：摄氏度）", true));
            configList.add(new BizFieldConfig(tableName, "height", "身高（单位：厘米）", true));
            configList.add(new BizFieldConfig(tableName, "weight", "重量（单位：千克）", true));
            configList.add(new BizFieldConfig(tableName, "headCircumference", "头围（单位：厘米）", true));
            configList.add(new BizFieldConfig(tableName, "chestCircumference", "胸围（单位：厘米）", true));
            configList.add(new BizFieldConfig(tableName, "jaundiceValue", "黄疸值", true));
            configList.add(new BizFieldConfig(tableName, "heartRate", "心跳（单位：次/分钟）", true));
            configList.add(new BizFieldConfig(tableName, "respiratoryRate", "呼吸频率（单位：次/分钟）", true));
            configList.add(new BizFieldConfig(tableName, "sound", "声音（如：哭声、笑声等）", true));
            configList.add(new BizFieldConfig(tableName, "skinColor", "肤色", true));
            configList.add(new BizFieldConfig(tableName, "allergyHistory", "过敏史", true));
            configList.add(new BizFieldConfig(tableName, "sleepDuration", "睡眠时长（单位：小时）", true));
            configList.add(new BizFieldConfig(tableName, "shift", "班次（如：早班、晚班等）", true));
            configList.add(new BizFieldConfig(tableName, "abnormalCondition", "异常情况", true));
            configList.add(new BizFieldConfig(tableName, "notes", "备注信息", true));
            configList.add(new BizFieldConfig(tableName, "photoList", "照片列表（存储图片路径或URL，JSON格式）", true));
            saveBatch(configList);
        }
        if (tableName.equals(FiledTableEnum.BABY_MEDICATION.getTableName())){
            //宝宝用药记录
            configList.add(new BizFieldConfig(tableName, "babyId", "宝宝ID", true));
            configList.add(new BizFieldConfig(tableName, "recordTime", "记录时间", true));
            configList.add(new BizFieldConfig(tableName, "medicineName", "药名", true));
            configList.add(new BizFieldConfig(tableName, "dosage", "用量", true));
            configList.add(new BizFieldConfig(tableName, "administrationMethod", "用药方法（如：口服、外用等）", true));
            configList.add(new BizFieldConfig(tableName, "waterVolume", "用水冲服（单位：毫升）", true));
            configList.add(new BizFieldConfig(tableName, "abnormalCondition", "异常情况", true));
            configList.add(new BizFieldConfig(tableName, "notes", "备注信息", true));
            configList.add(new BizFieldConfig(tableName, "photoList", "照片列表（存储图片路径或URL，JSON格式）", true));
            saveBatch(configList);
        }
        if (tableName.equals(FiledTableEnum.BABY_FEEDING.getTableName())){
            //宝宝喂奶记录
            configList.add(new BizFieldConfig(tableName, "babyId", "宝宝ID", true));
            configList.add(new BizFieldConfig(tableName, "recordTime", "记录时间", true));
            configList.add(new BizFieldConfig(tableName, "feedingMethod", "进食方式（如：母乳、配方奶、辅食等）", true));
            configList.add(new BizFieldConfig(tableName, "feedingDuration", "进食时长（单位：分钟）", true));
            configList.add(new BizFieldConfig(tableName, "milkAmount", "喂奶量（单位：毫升）", true));
            configList.add(new BizFieldConfig(tableName, "waterAmount", "喂水量（单位：毫升）", true));
            configList.add(new BizFieldConfig(tableName, "notes", "备注信息", true));
            configList.add(new BizFieldConfig(tableName, "photoList", "照片列表（存储图片路径或URL，JSON格式）", true));
            saveBatch(configList);
        }
        if (tableName.equals(FiledTableEnum.BABY_BATH.getTableName())){
            //宝宝洗澡记录
            configList.add(new BizFieldConfig(tableName, "babyId", "宝宝ID", true));
            configList.add(new BizFieldConfig(tableName, "recordTime", "记录时间", true));
            configList.add(new BizFieldConfig(tableName, "roomTemperature", "房间温度", true));
            configList.add(new BizFieldConfig(tableName, "roomHumidity", "房间湿度", true));
            configList.add(new BizFieldConfig(tableName, "bathDuration", "洗澡时长", true));
            configList.add(new BizFieldConfig(tableName, "bodyTemperature", "体温", true));;
            configList.add(new BizFieldConfig(tableName, "notes", "备注信息", true));
            configList.add(new BizFieldConfig(tableName, "photoList", "照片列表（存储图片路径或URL，JSON格式）", true));
            saveBatch(configList);
        }
        if (tableName.equals(FiledTableEnum.BABY_CARE.getTableName())){
            //宝宝护理
            configList.add(new BizFieldConfig(tableName, "babyId", "宝宝ID", true));
            configList.add(new BizFieldConfig(tableName, "temperature", "体温", true));
            configList.add(new BizFieldConfig(tableName, "jaundiceValue", "黄疸值", true));
            configList.add(new BizFieldConfig(tableName, "height", "身高", true));
            configList.add(new BizFieldConfig(tableName, "weight", "体重", true));
            configList.add(new BizFieldConfig(tableName, "head", "头部情况", true));
            configList.add(new BizFieldConfig(tableName, "fontanelle", "囟门情况", true));
            configList.add(new BizFieldConfig(tableName, "eyes", "眼部情况", true));
            configList.add(new BizFieldConfig(tableName, "nose", "鼻部情况", true));
            configList.add(new BizFieldConfig(tableName, "mouth", "口腔情况", true));
            configList.add(new BizFieldConfig(tableName, "face", "面部情况", true));
            configList.add(new BizFieldConfig(tableName, "neck", "颈部情况", true));
            configList.add(new BizFieldConfig(tableName, "abdomen", "腹部情况", true));
            configList.add(new BizFieldConfig(tableName, "umbilicus", "脐部情况", true));
            configList.add(new BizFieldConfig(tableName, "genitals", "生殖器情况", true));
            configList.add(new BizFieldConfig(tableName, "limbs", "四肢情况", true));
            configList.add(new BizFieldConfig(tableName, "muscleTone", "肌张力", true));
            configList.add(new BizFieldConfig(tableName, "rashLocation", "红疹部位", true));
            configList.add(new BizFieldConfig(tableName, "hemangiomaLocation", "血管瘤部位", true));
            configList.add(new BizFieldConfig(tableName, "mongolianSpotLocation", "蒙古斑部位", true));
            configList.add(new BizFieldConfig(tableName, "notes", "备注", true));
            configList.add(new BizFieldConfig(tableName, "imageInfo", "图片信息", true));
            saveBatch(configList);
        }
        return configList;
    }
}
