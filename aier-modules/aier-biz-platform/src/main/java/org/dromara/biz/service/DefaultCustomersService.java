package org.dromara.biz.service;

import org.dromara.biz.domain.DefaultCustomers;
import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.DefaultCustomersStaff;
import org.dromara.biz.domain.bo.DefaultCustomerStaffBO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_default_customers(默认用户表)】的数据库操作Service
* @createDate 2024-08-15 17:54:54
*/
public interface DefaultCustomersService extends IService<DefaultCustomers> {

    /**
     * 绑定员工
     */
    Boolean bindStaff(DefaultCustomerStaffBO defaultCustomerStaffBO);

    /**
     * 查询客户绑定员工列表
     */
    List<DefaultCustomersStaff> getBindStaffList(Long id);
}
