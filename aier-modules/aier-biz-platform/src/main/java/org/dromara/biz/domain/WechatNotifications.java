package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 消息通知、订阅表
 * @TableName biz_wechat_notifications
 */
@TableName(value ="biz_wechat_notifications")
@Data
public class WechatNotifications implements Serializable {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     * 通知内容
     */
    private String content;

    /**
     * 发送时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /**
     * 发送状态 pending', 'sent', 'failed
     */
    private String sendStatus;

    /**
     * 接收通知的人数
     */
    private Integer recipientCount;

    /**
     * 发送通知的人数
     */
    private Integer senderCount;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 通知类型 1=小程序订阅通知;2=公众号订阅通知;3=公众号模版消息;4=短信
     */
    private String type;

    /**
     * 消息模版
     */
    private String templateKey;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
