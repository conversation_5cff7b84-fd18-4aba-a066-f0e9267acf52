package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 阅读者表
 * @TableName biz_invitation_read
 */
@TableName(value ="biz_invitation_read")
@Data
public class InvitationRead implements Serializable {
    /**
     * 主键id
     */
    @TableId
    private Long id;

    /**
     * 主表id
     */
    private Long masterId;

    /**
     * 阅读人昵称
     */
    private String readuserName;

    /**
     * 被发帖人参与人数
     */
    private Integer readSum;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 阅读人openid
     */
    private String readOpenid;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
