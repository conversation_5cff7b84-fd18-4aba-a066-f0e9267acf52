package org.dromara.biz.controller.mp;


import cn.dev33.satoken.annotation.SaIgnore;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.BizBabyCare;
import org.dromara.biz.domain.BizBabyDiaper;
import org.dromara.biz.domain.BizBabyInfo;
import org.dromara.biz.domain.vo.nurse.BabyCareVO;
import org.dromara.biz.domain.vo.nurse.BabyDiaperVO;
import org.dromara.biz.service.IBizBabyCareService;
import org.dromara.biz.service.IBizBabyDiaperService;
import org.dromara.biz.service.IBizBabyInfoService;
import org.dromara.common.core.domain.R;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.web.bind.annotation.*;

import org.springframework.stereotype.Controller;

import java.util.List;

/**
 * 宝宝尿布记录表 前端控制器
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping("/mp/baby/diaper")
public class BizBabyDiaperController {
    private final IBizBabyDiaperService bizBabyDiaperService;
    private final IBizBabyInfoService babyInfoService;
    /**
     * 新增宝宝尿布记录
     * @param babyDiaper
     * @return
     */
    @PostMapping("/insert")
    public R<Boolean> insert(@RequestBody BizBabyDiaper babyDiaper) {

        return R.ok(bizBabyDiaperService.save(babyDiaper));
    }

    /**
     * 修改宝宝尿布记录
     * @param babyDiaper
     * @return
     */
    @PostMapping("/update")
    public R<Boolean> update(@RequestBody BizBabyDiaper babyDiaper) {
        return R.ok(bizBabyDiaperService.updateById(babyDiaper));
    }

    /**
     * 尿布记录列表
     * @param customerId
     * @return
     */
    @GetMapping("/list")
    @SaIgnore
    public R<List<BabyDiaperVO>> list(@RequestParam("customerId") Long customerId){
        return R.ok(bizBabyDiaperService.listVo(customerId));
    }
    /**
     * 我的尿布记录列表
     * @return
     */
    @GetMapping("/myList")
    public R<List<BabyDiaperVO>> myList(){
        Long  babyId = babyInfoService.getByLoginUser();
        if (babyId != null) {
            return R.ok(bizBabyDiaperService.listVo(babyId));
        }
        return R.ok(null);
    }

    /**
     * 获取记录详情 （宝宝尿布）
     * @param id
     * @return
     */
    @GetMapping("/getDetail")
    @SaIgnore
    public R<BabyDiaperVO> getDetail(@RequestParam("id") Long id){
        return R.ok(bizBabyDiaperService.getDetail(id));
    }

    /**
     * 删除记录
     * @param id
     * @return
     */
    @GetMapping("/delete")
    public R<Boolean> delete(@RequestParam("id") Long id){
        return R.ok(bizBabyDiaperService.removeById(id));
    }
}
