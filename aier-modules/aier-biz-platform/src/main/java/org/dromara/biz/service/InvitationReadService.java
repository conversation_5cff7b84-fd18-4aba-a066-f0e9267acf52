package org.dromara.biz.service;

import org.dromara.biz.domain.InvitationRead;
import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.query.InvitationReadQuery;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.web.bind.annotation.RequestParam;

/**
* <AUTHOR>
* @description 针对表【biz_invitation_read(阅读者表)】的数据库操作Service
* @createDate 2024-07-30 10:50:39
*/
public interface InvitationReadService extends IService<InvitationRead> {

    Boolean saveReaderMsg(InvitationRead readMsg);

    TableDataInfo<InvitationRead> selectReaderDetails(InvitationReadQuery masterId);

}
