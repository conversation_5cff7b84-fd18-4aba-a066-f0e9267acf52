package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 客户服务过程表
 * @TableName biz_service_procedure_steps
 */
@TableName(value ="biz_service_procedure_steps")
@Data
public class ServiceProcedureSteps implements Serializable {
    /**
     *
     */
    @TableId
    private Long stepId;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 节点id
     */
    private Long taskNodeId;

    /**
     * 动态id
     */
    private Long postId;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 服务人员id
     */
    private Long staffId;

    /**
     * 房间id
     */
    private Long roomId;

    /**
     * 目标用户id
     */
    private Long targetUserId;

    /**
     * 动态发布时间
     */
    private Date publishedAt;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
