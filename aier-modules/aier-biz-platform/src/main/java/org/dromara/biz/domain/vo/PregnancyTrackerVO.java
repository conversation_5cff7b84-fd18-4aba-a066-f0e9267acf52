package org.dromara.biz.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.dromara.biz.domain.PregnancyTracker;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 孕期追踪表
 * @TableName biz_pregnancy_tracker
 */
@Data
@AutoMapper(target = PregnancyTracker.class)
public class PregnancyTrackerVO implements Serializable {
    /**
     *
     */
    private Long trackerId;

    /**
     * 孕期周数（1-42）
     */
    @NotNull(message = "孕期周数不能为空")
    private Integer gestationWeek;

    /**
     * 宝宝图片
     */
    private String babyImage;

    /**
     * 特别关注
     */
    private String specialAttention;

    /**
     * 宝宝情况图片1 URL
     */
    private String babyDevelopmentImage1;

    /**
     * 宝宝情况1标题
     */
    private String babyDevelopmentLabel1;

    /**
     * 宝宝情况图片2 URL
     */
    private String babyDevelopmentImage2;

    /**
     * 宝宝情况2标题
     */
    private String babyDevelopmentLabel2;

    /**
     * 宝宝情况详情
     */
    private String babyDevelopmentDetail;

    /**
     * 注意事项1
     */
    private String attentionItem1;

    /**
     * 注意事项1描述
     */
    private String attentionDesc1;

    /**
     * 注意事项2
     */
    private String attentionItem2;

    /**
     * 注意事项2描述
     */
    private String attentionDesc2;

    /**
     * 食物图1 URL
     */
    private String foodImage1;

    /**
     * 食物图2 URL
     */
    private String foodImage2;

    /**
     * 食物名称1
     */
    private String foodName1;

    /**
     * 食物描述1
     */
    private String foodDescription1;

    /**
     * 食物名称2
     */
    private String foodName2;

    /**
     * 食物描述2
     */
    private String foodDescription2;

    /**
     * 能做的事
     */
    private String canDoActivities;

    /**
     * 不能做的事
     */
    private String cannotDoActivities;

    private Date createTime;

    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}
