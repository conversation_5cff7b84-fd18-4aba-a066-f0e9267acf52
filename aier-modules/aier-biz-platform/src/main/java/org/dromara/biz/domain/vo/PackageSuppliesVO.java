package org.dromara.biz.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.dromara.biz.domain.PackageSupplies;

import java.io.Serial;
import java.io.Serializable;

/**
 * 套餐用品vo
 */
@Data
@AutoMapper(target = PackageSupplies.class)
public class PackageSuppliesVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long relationId;

    /**
     * 套餐id
     */
    private Long packageId;

    /**
     * 用品id
     */
    @NotNull(message = "用品id不能为空")
    private Long suppliesId;

    /**
     * 数量
     */
    @NotNull(message = "用品数量不能为空")
    private Integer quantity;

    /**
     * 限店内使用
     */
    private Boolean inStoreUse;

    /**
     * 用品类别
     */
    private String category;

    /**
     * 用品名称
     */
    private String supplyName;

    /**
     * 用品品牌
     */
    private String brandName;
}
