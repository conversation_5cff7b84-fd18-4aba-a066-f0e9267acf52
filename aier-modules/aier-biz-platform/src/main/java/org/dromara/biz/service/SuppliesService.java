package org.dromara.biz.service;

import org.dromara.biz.domain.Supplies;
import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.query.SuppliesQuery;
import org.dromara.biz.domain.vo.SuppliesVO;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
* <AUTHOR>
* @description 针对表【biz_supplies(用品管理表)】的数据库操作Service
* @createDate 2024-03-12 11:10:57
*/
public interface SuppliesService extends IService<Supplies> {

    TableDataInfo<SuppliesVO> queryPage(SuppliesQuery query);
}
