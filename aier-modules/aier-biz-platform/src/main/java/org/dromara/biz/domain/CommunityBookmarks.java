package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.io.Serializable;

/**
 * 社区动态收藏表
 * @TableName biz_community_collections
 */
@TableName(value ="biz_community_bookmarks")
@Data
@EqualsAndHashCode(callSuper = true)
public class CommunityBookmarks extends TenantEntity implements Serializable {
    /**
     *
     */
    @TableId
    private Long collectionId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 动态id
     */
    private Long postId;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
