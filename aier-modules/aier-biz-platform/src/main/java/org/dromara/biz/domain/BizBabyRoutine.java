package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.dromara.common.mybatis.handler.type.StringListTypeHandler;
import org.dromara.common.tenant.core.TenantEntity;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 宝宝常规记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("biz_baby_routine")
public class BizBabyRoutine extends TenantEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 宝宝ID
     */
    private Long babyId;

    /**
     * 记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date recordTime;

    /**
     * 母婴同房送去时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date motherBabySendTime;

    /**
     * 母婴同房接回时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date motherBabyReceiveTime;

    /**
     * 体温（单位：摄氏度）
     */
    private String temperature;

    /**
     * 身高（单位：厘米）
     */
    private String height;

    /**
     * 重量（单位：千克）
     */
    private String weight;

    /**
     * 头围（单位：厘米）
     */
    private String headCircumference;

    /**
     * 胸围（单位：厘米）
     */
    private String chestCircumference;

    /**
     * 黄疸值
     */
    private String jaundiceValue;

    /**
     * 心跳（单位：次/分钟）
     */
    private String heartRate;

    /**
     * 呼吸频率（单位：次/分钟）
     */
    private String respiratoryRate;

    /**
     * 声音（如：哭声、笑声等）
     */
    private String sound;

    /**
     * 肤色
     */
    private String skinColor;

    /**
     * 过敏史
     */
    private String allergyHistory;

    /**
     * 睡眠时长（单位：小时）
     */
    private String sleepDuration;

    /**
     * 班次（如：早班、晚班等）
     */
    private String shift;

    /**
     * 异常情况
     */
    private String abnormalCondition;

    /**
     * 备注
     */
    private String notes;

    /**
     * 照片列表（存储图片路径或URL，JSON格式）
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> photoList;
}
