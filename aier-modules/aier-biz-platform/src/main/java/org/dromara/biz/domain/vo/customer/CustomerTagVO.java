package org.dromara.biz.domain.vo.customer;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.biz.domain.CustomerTag;
import org.dromara.biz.domain.CustomerTagExpert;
import org.dromara.biz.domain.CustomerTagProfessional;
import org.dromara.biz.domain.CustomerTagSolution;

import java.util.List;

/**
 * 客户标签vo
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = CustomerTag.class)
public class CustomerTagVO extends CustomerTag {

    /**
     * 专业话术列表
     */
    private List<CustomerTagProfessional> professionalList;

    /**
     * 专家咨询列表
     */
    private List<CustomerTagExpert> expertList;

    /**
     * 系统解决方案
     */
    private CustomerTagSolution solution;

    /**
     * 专业话术数量
     */
    private Integer professionalNum;

    /**
     * 系统解决方案
     */
    private Integer solutionNum = 1;
}
