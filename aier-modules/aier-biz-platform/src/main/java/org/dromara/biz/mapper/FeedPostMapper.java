package org.dromara.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.biz.domain.FeedPost;
import org.dromara.biz.domain.vo.FeedPostVO;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_feed_post(会所朋友圈动态表)】的数据库操作Mapper
* @createDate 2024-03-15 10:57:12
* @Entity org.dromara.biz.domain.FeedPost
*/
public interface FeedPostMapper extends BaseMapperPlus<FeedPost, FeedPostVO> {

    List<FeedPostVO> queryFeaturedNodeList(Integer limit);

    /**
     * 分页查询朋友圈列表
     */
    Page<FeedPostVO> getFeedPostPage(Page<FeedPostVO> page,
                                     @Param(Constants.WRAPPER) QueryWrapper wrapper,
                                     @Param("userId") Long userId);

    /**
     * 查询朋友圈列表
     */
    List<FeedPostVO> getFeedPostList(@Param(Constants.WRAPPER) QueryWrapper wrapper,
                                     @Param("userId") Long userId);

    /**
     * 分页查询会所动态列表
     */
    Page<FeedPostVO> queryClubPost(Page<FeedPostVO> page,
                                   @Param(Constants.WRAPPER) QueryWrapper wrapper,
                                   @Param("userId") Long userId);

    /**
     * 查询会所动态详情
     */
    FeedPostVO getClubDetail(@Param("postId") Long postId,
                         @Param("userId") Long userId);

    /**
     * 查询动态详情
     */
    FeedPostVO getDetail(@Param("postId") Long postId,
                         @Param("userId") Long userId);
}




