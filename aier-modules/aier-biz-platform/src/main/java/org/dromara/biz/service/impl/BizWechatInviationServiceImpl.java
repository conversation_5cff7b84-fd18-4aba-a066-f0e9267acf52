package org.dromara.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.dromara.biz.domain.BizWechatInviation;
import org.dromara.biz.domain.Clubs;
import org.dromara.biz.domain.CommunityPosts;
import org.dromara.biz.domain.Reviews;
import org.dromara.biz.domain.vo.WechatInviationVo;
import org.dromara.biz.mapper.BizWechatInviationMapper;
import org.dromara.biz.service.IBizWechatInviationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;

import java.sql.Wrapper;
import java.util.Optional;

/**
 * <p>
 * 微信电子请柬表
 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-03
 */
@Service
public class BizWechatInviationServiceImpl extends ServiceImpl<BizWechatInviationMapper, BizWechatInviation> implements IBizWechatInviationService {

    @Override
    public WechatInviationVo save(WechatInviationVo wechatInviationVo) {
        Long userId = LoginHelper.getUserId();
        BizWechatInviation wechatInviation = MapstructUtils.convert(wechatInviationVo, BizWechatInviation.class);
        if (wechatInviationVo.getStandby1()!=null) {
            wechatInviation.setStandby1(wechatInviationVo.getStandby1().toString());
        }
        if (wechatInviationVo.getStandby2()!=null) {
            wechatInviation.setStandby2(wechatInviationVo.getStandby2().toString());
        }
        if (wechatInviationVo.getStandby3()!=null) {
            wechatInviation.setStandby3(wechatInviationVo.getStandby3().toString());
        }
        if (wechatInviationVo.getId() != null) {
            baseMapper.updateById(wechatInviation);
            return wechatInviationVo;
        }
        if (baseMapper.insert(wechatInviation) > 0) {
            wechatInviationVo.setId(wechatInviation.getId());
            return wechatInviationVo;
        }
        throw new ServiceException("保存请柬失败，请联系管理员！");
    }
}
