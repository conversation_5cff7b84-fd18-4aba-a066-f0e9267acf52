package org.dromara.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.Suites;
import org.dromara.biz.domain.query.SuiteQuery;
import org.dromara.biz.domain.vo.SelectOptionsVO;
import org.dromara.biz.domain.vo.SuiteVO;
import org.dromara.biz.domain.vo.room.SuiteBo;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【biz_suites(月子套房表)】的数据库操作Service
* @createDate 2024-03-12 11:01:33
*/
public interface SuitesService extends IService<Suites> {

    /**
     * 分页查询房型列表
     */
    TableDataInfo<SuiteVO> queryPage(SuiteQuery query);

    /**
     * 修改房型
     */
    Boolean update(SuiteVO suite);

    /**
     * 新增房型
     */
    Boolean save(SuiteVO suite);

    Map<Long, SuiteVO> getMapByIds(List<Long> suiteIds);

    List<SelectOptionsVO> getSelectOptions();

    SuiteVO getInfo(Long suiteId);

    /**
     * 查询热门推荐房型列表
     * @param limit 查询数量
     * @return 热门推荐房型列表
     */
    List<SuiteVO> queryRecommendation(Integer limit);

    Boolean updateShowStatus(Long suiteId, Boolean status);

    /**
     * 查询月子套房列表
     */
    List<SuiteVO> queryList(SuiteQuery query);

    /**
     * 设置热门推荐套房
     */
    Boolean setRecommended(String suiteIds);

    /**
     * 取消热门推荐房型
     */
    Boolean cancelRecommended(String suiteIds);

    /**
     * 小程序信息修改房型
     * @param suiteBo
     * @return
     */
    Boolean mpSave(SuiteBo suiteBo);

    /**
     * 小程序房型列表
     * @param query
     * @return
     */
    List<SuiteBo> queryMpList(SuiteQuery query);
}
