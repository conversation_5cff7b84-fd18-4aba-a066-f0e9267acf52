package org.dromara.biz.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 大家问答
 */
@Data
@AutoMapper(target = org.dromara.biz.domain.Questions.class)
public class QuestionVO implements Serializable {
    /**
     *
     */
    private Long questionId;

    /**
     * 问题
     */
    private String questionText;

    /**
     * 答案
     */
    private String answerText;

    /**
     * 是否显示
     */
    private Boolean isVisible;

    /**
     * 分类 room=房间; meal_package=膳食套餐; recovery=产康; nanny=移动月嫂; package=优惠套餐;

     */
    private String category;

    private Date createTime;

    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}
