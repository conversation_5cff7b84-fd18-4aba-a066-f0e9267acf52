package org.dromara.biz.service.impl;

import org.dromara.biz.domain.BizMotherRounds;
import org.dromara.biz.domain.vo.nurse.MotherCareVO;
import org.dromara.biz.domain.vo.nurse.MotherRoundsVO;
import org.dromara.biz.mapper.BizMotherRoundsMapper;
import org.dromara.biz.service.IBizMotherRoundsService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 宝妈查房表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-24
 */
@Service
public class BizMotherRoundsServiceImpl extends ServiceImpl<BizMotherRoundsMapper, BizMotherRounds> implements IBizMotherRoundsService {

    @Override
    public List<MotherRoundsVO> listVo(Long customerId) {
        return baseMapper.listVo(customerId);
    }

    @Override
    public MotherRoundsVO getDetail(Long id) {
        return baseMapper.getDatail(id);
    }
}
