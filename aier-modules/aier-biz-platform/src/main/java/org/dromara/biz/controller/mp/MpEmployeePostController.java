package org.dromara.biz.controller.mp;


import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import org.dromara.biz.domain.EmployeePost;
import org.dromara.biz.domain.query.employee.EmployeePostQuery;
import org.dromara.biz.domain.vo.EmployeePostVO;
import org.dromara.biz.domain.vo.SelectOptionsVO;
import org.dromara.biz.service.EmployeePostService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 员工职位表
 */
@RestController
@RequestMapping("/mp/employee_post")
@AllArgsConstructor
@SaIgnore
public class MpEmployeePostController {

    private final EmployeePostService employeePostService;

    /**
     * 查询员工职位列表
     */
    @GetMapping("/page")
    @Deprecated
    public TableDataInfo<EmployeePostVO> page(EmployeePostQuery query){
        return employeePostService.queryPage(query);
    }

    /**
     * 新增员工职位
     */
    @PostMapping("/save")
    @RepeatSubmit
    @Deprecated
    public R<Boolean> save(@RequestBody @Validated EmployeePost employeePost){
        employeePost.setCreateTime(DateUtil.date());
        employeePost.setId(null);
        return R.ok(employeePostService.save(employeePost));
    }

    /**
     * 修改员工职位
     */
    @PostMapping("/update")
    @RepeatSubmit
    @Deprecated
    public R<Boolean> update(@RequestBody @Validated(EditGroup.class) EmployeePost employeePost){
        employeePost.setUpdateTime(DateUtil.date());
        return R.ok(employeePostService.updateById(employeePost));
    }

    /**
     * 查询员职位位详情
     */
    @GetMapping("/detail")
    @Deprecated
    public R<EmployeePost> detail(@RequestParam("id") Long id){
        return R.ok(employeePostService.getById(id));
    }

    /**
     * 查询员工职位下拉列表选项
     */
    @GetMapping("/options")
    @Deprecated
    public R<List<SelectOptionsVO>> getOptions() {
        return R.ok(employeePostService.getOptions());
    }
}
