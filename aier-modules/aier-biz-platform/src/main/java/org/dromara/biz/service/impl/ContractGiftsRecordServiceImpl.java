package org.dromara.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.biz.domain.ContractGiftsRecord;
import org.dromara.biz.service.ContractGiftsRecordService;
import org.dromara.biz.mapper.ContractGiftsRecordMapper;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【biz_contract_gifts_record(签约礼品领取记录表)】的数据库操作Service实现
* @createDate 2024-05-21 15:48:52
*/
@Service
public class ContractGiftsRecordServiceImpl extends ServiceImpl<ContractGiftsRecordMapper, ContractGiftsRecord>
    implements ContractGiftsRecordService{

    @Override
    public Boolean isSigning(Long contractGiftId) {
        Long userId = LoginHelper.getUserId();
        LambdaQueryWrapper<ContractGiftsRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(ContractGiftsRecord::getContractGiftId, contractGiftId);
        lqw.eq(ContractGiftsRecord::getUserId, userId);
        return count(lqw) > 0L;
    }

    @Override
    public Boolean save(Long contractGiftId) {
        Long userId = LoginHelper.getUserId();
        ContractGiftsRecord contractGiftsRecord = new ContractGiftsRecord();
        contractGiftsRecord.setUserId(userId);
        contractGiftsRecord.setContractGiftId(contractGiftId);
        return save(contractGiftsRecord);
    }
}




