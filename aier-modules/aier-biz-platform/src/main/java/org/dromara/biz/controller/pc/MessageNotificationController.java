package org.dromara.biz.controller.pc;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import org.dromara.biz.domain.WechatNotifications;
import org.dromara.biz.domain.bo.MessageNotificationBO;
import org.dromara.biz.domain.query.MessageNotificationQuery;
import org.dromara.biz.service.WechatNotificationsService;
import org.dromara.biz.service.WechatUserService;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 微信消息通知相关接口
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/message_notification")
public class MessageNotificationController {

    private final WechatUserService wechatUserService;
    private final WechatNotificationsService wechatNotificationsService;

    /**
     * 发送小程序订阅消息
     * @param messageNotificationBO 通知消息数据
     * @return 结果
     */
    @PostMapping("/send")
    @RepeatSubmit
    public R<Boolean> sendMessage(@RequestBody @Validated MessageNotificationBO messageNotificationBO) {
        return R.ok(wechatUserService.sendMessage(messageNotificationBO));
    }

    /**
     * 发送公众号订阅消息
     * @param messageNotificationBO 通知消息数据
     * @return 结果
     */
    @PostMapping("/mp/send")
    @RepeatSubmit
    public R<Boolean> sendMpMessage(@RequestBody @Validated MessageNotificationBO messageNotificationBO) {
        return R.ok(wechatUserService.sendMpMessage(messageNotificationBO));
    }

    /**
     * 分页查询消息推送列表
     */
    @GetMapping("/page")
    public TableDataInfo<WechatNotifications> queryPage(MessageNotificationQuery query) {
        LambdaQueryWrapper<WechatNotifications> lqw = Wrappers.lambdaQuery();
        lqw.eq(WechatNotifications::getType, query.getType());
        lqw.orderByDesc(WechatNotifications::getCreateTime);
        return TableDataInfo.build(wechatNotificationsService.page(query.build(), lqw));
    }

}
