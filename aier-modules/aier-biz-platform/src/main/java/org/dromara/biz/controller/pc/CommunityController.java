package org.dromara.biz.controller.pc;

import lombok.RequiredArgsConstructor;
import org.dromara.biz.domain.query.CommunityCommentQuery;
import org.dromara.biz.domain.query.CommunityPostQuery;
import org.dromara.biz.domain.vo.CommunityCommentVO;
import org.dromara.biz.domain.vo.CommunityPostVO;
import org.dromara.biz.service.CommunityCommentService;
import org.dromara.biz.service.CommunityPostsService;
import org.dromara.common.core.domain.R;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 宝妈社区动态相关接口
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/community")
public class CommunityController {

    private final CommunityPostsService communityPostsService;
    private final CommunityCommentService communityCommentService;

    /**
     * 分页查询宝妈社区动态列表
     */
    @GetMapping("/page")
    public TableDataInfo<CommunityPostVO> queryPage (CommunityPostQuery query) {
        return communityPostsService.queryPage(query);
    }

    /**
     * 查询宝妈社区动态列表_没有分页
     */
    @GetMapping("/list")
    public R<List<CommunityPostVO>> queryList (CommunityPostQuery query) {
        return R.ok(communityPostsService.queryList(query));
    }

    /**
     * 查询宝妈社区动态评论列表
     * @param query 查询参数
     * @return 社区动态评论列表
     */
    @GetMapping("/list_comment")
    public R<List<CommunityCommentVO>> queryCommentList(CommunityCommentQuery query){
        return R.ok(communityCommentService.queryList(query));
    }

    /**
     * 删除评论
     * @param commentIds 评论ids
     * @return
     */
    @DeleteMapping("/comment")
    public R<Boolean> removeComment(@RequestParam Long [] commentIds){
        return R.ok(communityCommentService.removeComment(commentIds));
    }

    /**
     * 删除动态
     * @param postIds 动态ids
     * @return
     */
    @DeleteMapping("/post")
    public R<Boolean> removePost(@RequestParam Long [] postIds){
        return R.ok(communityPostsService.remove(postIds));
    }
}
