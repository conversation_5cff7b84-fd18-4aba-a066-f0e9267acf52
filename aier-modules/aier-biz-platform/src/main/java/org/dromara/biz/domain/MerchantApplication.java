package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 商家入驻申请比
 * @TableName biz_merchant_application
 */
@TableName(value ="biz_merchant_application")
@Data
public class MerchantApplication implements Serializable {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     * 商家名称
     */
    private String merchantName;

    /**
     * 联系人姓名
     */
    private String contactName;

    /**
     * 联系电话
     */
    private String contactPhone;

    /**
     * 联系邮箱
     */
    private String contactEmail;

    /**
     * 地址
     */
    private String address;

    /**
     * 申请日期
     */
    private Date applicationDate;

    /**
     * 申请状态 0=审核中、1=通过、2=拒绝
     */
    private Integer status;

    /**
     *
     */
    private Date createTime;

    /**
     *
     */
    private Date updateTime;

    /**
     * 密码
     */
    private String password;

    private String tenantId;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
