package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 小程序事件追踪表-社区
 * @TableName biz_event_tracking_community
 */
@TableName(value ="biz_event_tracking_community")
@Data
public class EventTrackingCommunity implements Serializable {
    /**
     *
     */
    @TableId
    private Long eventId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * session_id
     */
    private String sessionId;

    /**
     * 事件类型
     */
    private String eventType;

    /**
     * 事件发生时间戳
     */
    private Date eventTime;

    /**
     * 离开时间
     */
    private Date leaveTime;

    /**
     * 页面url
     */
    private String pageUrl;

    /**
     * 页面标题
     */
    private String pageTitle;

    /**
     * 所属模块
     */
    private String module;

    /**
     * 来源页面url
     */
    private String refererUrl;

    /**
     * 平台 (微信小程序, 支付宝小程序 等)
     */
    private String platform;

    /**
     * 设备id
     */
    private String deviceId;

    /**
     * 操作系统版本
     */
    private String osVersion;

    /**
     * 应用版本
     */
    private String appVersion;

    /**
     * 其他事件相关数据，如点击元素ID，表单数据等 (JSON格式存储)
     */
    private String additionalData;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
