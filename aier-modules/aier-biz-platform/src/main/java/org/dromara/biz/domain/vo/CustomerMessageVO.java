package org.dromara.biz.domain.vo;

import lombok.Data;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;

import java.io.Serial;
import java.io.Serializable;

/**
 * 客户消息vo
 */
@Data

public class CustomerMessageVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 会所名称
     */
    @Translation(type = TransConstant.CLUB_NAME)
    private String clubName = "";

    /**
     * 最新消息内容
     */
    private String content;
}
