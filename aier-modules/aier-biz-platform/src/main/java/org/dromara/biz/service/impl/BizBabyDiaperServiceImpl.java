package org.dromara.biz.service.impl;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.BizBabyDiaper;
import org.dromara.biz.domain.vo.nurse.BabyDiaperVO;
import org.dromara.biz.mapper.BizBabyDiaperMapper;
import org.dromara.biz.service.IBizBabyDiaperService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 宝宝尿布记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
@Service
@AllArgsConstructor
@Slf4j
public class BizBabyDiaperServiceImpl extends ServiceImpl<BizBabyDiaperMapper, BizBabyDiaper> implements IBizBabyDiaperService {

    @Override
    public List<BabyDiaperVO> listVo(Long babyId) {
        return baseMapper.listVo(babyId);
    }

    @Override
    public BabyDiaperVO getDetail(Long id) {
        return baseMapper.getDetail(id);
    }
}
