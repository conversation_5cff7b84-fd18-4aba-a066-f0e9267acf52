package org.dromara.biz.domain.event;

import lombok.Getter;
import lombok.Setter;
import org.dromara.biz.domain.UgcTrace;
import org.springframework.context.ApplicationEvent;
import org.springframework.core.ResolvableType;
import org.springframework.core.ResolvableTypeProvider;
@Getter
@Setter
public class UgcMediaCheckEvent extends ApplicationEvent implements ResolvableTypeProvider {

    private UgcTrace urcTrace;

    public UgcMediaCheckEvent(Object source) {
        super(source);
    }
    public UgcMediaCheckEvent(Object source, UgcTrace urcTrace) {
        super(source);
        this.urcTrace = urcTrace;
    }

    @Override
    public ResolvableType getResolvableType() {
        return null;
    }
}
