package org.dromara.biz.domain.query;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.page.PageQuery;

/**
 * 社区动态评论查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CommunityCommentQuery extends PageQuery {

    /**
     * 社区动态id
     */
    @NotNull(message = "社区动态id不能为空")
    private Long postId;

    /**
     * 查询评论条数 跟随帖子查询只会返回2条评论用于展示 需要获取更多评论需要调用接口查询
     */
    private Integer limit;

    /**
     * 排序方式 1-最新 2-最热
     */
    private String orderType;

    /**
     * 排序方式 最新
     */
    public static final String ORDER_TYPE_NEW = "1";

    /**
     * 排序方式 最热
     */
    public static final String ORDER_TYPE_HOT = "2";
}
