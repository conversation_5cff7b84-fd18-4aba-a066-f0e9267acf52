package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 动态推送记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-24
 */
@Data
@TableName("biz_feed_post_reminders")
public class BizFeedPostReminders implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    private String tenantId;

    /**
     * 提醒事项标题
     */
    private String title;

    /**
     * 提醒事项描述
     */
    private String description;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 动态id
     */
    private Long postId;

    /**
     * 提醒日期
     */
    private Date reminderDate;

    /**
     * 提醒状态
     */
    private Boolean status;

    private Date createTime;

    private Date updateTime;

    private String appid;

    private String openid;
}
