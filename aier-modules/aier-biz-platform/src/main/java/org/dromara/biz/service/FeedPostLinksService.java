package org.dromara.biz.service;

import org.dromara.biz.domain.FeedPostLinks;
import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.vo.FeedPostVO.LikeUsers;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【biz_feed_post_links(会所朋友圈点赞表)】的数据库操作Service
* @createDate 2024-03-15 10:57:13
*/
public interface FeedPostLinksService extends IService<FeedPostLinks> {

    /**
     * 根据动态id集合获取点赞信息
     * @param postSet 动态id集合
     * @return 结果
     */
    Map<Long, FeedPostLinks> getMapByPostIds(Set<Long> postSet);

    /**
     * 获取当前用户对指定动态的点赞信息
     * @param postSet 动态id集合
     * @return 结果
     */
    Map<Long, FeedPostLinks> getCurrentUserMapByPostIds(Set<Long> postSet);

    /**
     * 获取动态的点赞用户
     * @param postSet 动态id集合
     * @return 结果
     */
    Map<Long, List<LikeUsers>> getLikeUsers(Set<Long> postSet);

    /**
     * 判断用户是否点赞过该动态
     * @param postId 动态id
     * @param userId 用户id
     * @return 结果
     */
    Boolean isLiked(Long postId, Long userId);
}
