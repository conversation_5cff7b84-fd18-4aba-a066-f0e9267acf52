package org.dromara.biz.service;

import org.dromara.biz.domain.BizBabyInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.vo.nurse.BabyCareVO;
import org.dromara.biz.domain.vo.nurse.BabyInfoVO;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 宝宝信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-13
 */
public interface BabyInfoService extends IService<BizBabyInfo> {

    List<BizBabyInfo> getDetailByCustomerId(Long customerId);

    BabyInfoVO getByVo(Long babyId);

    List<BabyCareVO> getBabyAnalysis(Long babyId, Date time);
}
