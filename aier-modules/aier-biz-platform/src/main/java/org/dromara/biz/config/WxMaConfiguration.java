package org.dromara.biz.config;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.bean.WxMaKefuMessage;
import cn.binarywang.wx.miniapp.bean.WxMaMediaAsyncCheckResult;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import cn.binarywang.wx.miniapp.message.WxMaMessageHandler;
import cn.binarywang.wx.miniapp.message.WxMaMessageRouter;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.result.WxMediaUploadResult;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.biz.domain.CardInvitations;
import org.dromara.biz.domain.UgcTrace;
import org.dromara.biz.domain.WechatMiniProgram;
import org.dromara.biz.domain.event.UgcMediaCheckEvent;
import org.dromara.biz.service.CardInvitationsService;
import org.dromara.biz.service.WechatMiniProgramService;
import org.dromara.common.core.utils.SpringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.File;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 微信小程序配置
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class WxMaConfiguration {

    @Autowired
    private WechatMiniProgramService wechatMiniProgramService;

    @Bean
    public WxMaService wxMaService() {
        List<WechatMiniProgram> configs = wechatMiniProgramService.queryListIgnoreTenant("1");
        if(CollectionUtils.isEmpty(configs)){
            return null;
        }
        WxMaService maService = new WxMaServiceImpl();
        maService.setMultiConfigs(
            configs.stream()
                .map(a -> {
                    WxMaDefaultConfigImpl config = new WxMaDefaultConfigImpl();
                    config.setAppid(a.getAppid());
                    config.setSecret(a.getSecret());
                    config.setToken(a.getToken());
                    config.setAesKey(a.getAesKey());
                    config.setMsgDataFormat(a.getMsgDataFormat());
                    return config;
                }).collect(Collectors.toMap(WxMaDefaultConfigImpl::getAppid, a -> a, (o, n) -> o)));
        log.info("初始化 {}个 微信小程序 配置", configs.size());
        return maService;
    }

    @Bean
    public WxMaMessageRouter wxMaMessageRouter(WxMaService wxMaService) {
        final WxMaMessageRouter router = new WxMaMessageRouter(wxMaService);
        router
            .rule().handler(logHandler).next()
            .rule().async(false).event("wxa_media_check").msgType("event").handler(mediaCheck).end()
            .rule().async(false).msgType("event").event("user_enter_tempsession").handler(userEnterTempsessionHandler).end()
            .rule().async(false).msgType("miniprogrampage").handler(miniProgramPageMsgHandler).end()
            .rule().async(false).content("订阅消息").handler(subscribeMsgHandler).end()
            .rule().async(false).content("文本").handler(textHandler).end()
            .rule().async(false).content("图片").handler(picHandler).end()
            .rule().async(false).msgType("text").handler(textHandler).end()
            .rule().async(false).content("二维码").handler(qrcodeHandler).end();
        return router;
    }

    private final WxMaMessageHandler logHandler = (wxMessage, context, service, sessionManager) -> {
        log.info("收到消息：{}", wxMessage.toString());
//        service.getMsgService().sendKefuMsg(WxMaKefuMessage.newTextBuilder().content("收到信息为：" + wxMessage.toJson())
//            .toUser(wxMessage.getFromUser()).build());
        return null;
    };

    /**
     * 媒体文件检查事件
     */
    private final WxMaMessageHandler mediaCheck = (wxMessage, context, service, sessionManager)->{
        log.info("收到媒体文件检查事件：{}", wxMessage.toString());
        String traceId = wxMessage.getTraceId();
        WxMaMediaAsyncCheckResult.ResultBean result = wxMessage.getResult();
        String label = result.getLabel();
        String suggest = result.getSuggest();
        UgcTrace ugcTrace = new UgcTrace();
        ugcTrace.setTraceId(traceId);
        ugcTrace.setSuggest(suggest);
        ugcTrace.setLabel(Integer.valueOf(label));
        SpringUtils.context().publishEvent(new UgcMediaCheckEvent(this, ugcTrace));
        return null;
    };

    /**
     * 客服小程序卡片消息处理
     */
    private final WxMaMessageHandler miniProgramPageMsgHandler = (wxMessage, context, service, sessionManager) -> {
        log.info("收到小程序卡片消息：{}", wxMessage);
        String pagePath = wxMessage.getPagePath();
        Map<String, String> params = UriComponentsBuilder.fromUriString(pagePath)
                .build()
                .getQueryParams()
                .toSingleValueMap();
        //请帖分享页面、生成网页卡片链接
        if("share".equals(params.get("source"))){
            Long cardId = Convert.convert(Long.class, params.get("cardId"));
            CardInvitationsService cardInvitationsService = SpringUtils.getBean(CardInvitationsService.class);
            CardInvitations invitations = cardInvitationsService.getInvitation(cardId);
            if(ObjectUtil.isNotNull(invitations)){
                // 发送指引消息
                service.getMsgService().sendKefuMsg(WxMaKefuMessage.newTextBuilder().content("点击打开以下请帖，按请帖内指引去分享给好友或朋友圈")
                        .toUser(wxMessage.getFromUser()).build());
                // 发送卡片消息
                service.getMsgService().sendKefuMsg(WxMaKefuMessage.newLinkBuilder()
                        .title(invitations.getTitle()).description(invitations.getText())
                        .url(invitations.getShareUrl() + "&source=share")
                        .thumbUrl(invitations.getCoverImageUrl())
                        .toUser(wxMessage.getFromUser()).build());
            }
        }
        return null;
    };

    private final WxMaMessageHandler userEnterTempsessionHandler = (wxMessage, context, service, sessionManager)->{
        log.info("用户进入会话事件：{}", wxMessage.toString());

        return null;
    };


    private final WxMaMessageHandler subscribeMsgHandler = (wxMessage, context, service, sessionManager) -> {
        service.getMsgService().sendSubscribeMsg(WxMaSubscribeMessage.builder()
            .templateId("IOn9fTPeoURr_o8AfB70jHQcbWr8SAn3MI1v_thi-dY")
            .data(Lists.newArrayList(
                new WxMaSubscribeMessage.MsgData("thing1", "公园烟花节"),
                new WxMaSubscribeMessage.MsgData("time2", "2020-05-01 12:22"),
                new WxMaSubscribeMessage.MsgData("thing5", "参加活动，赢取1000元大礼")
            ))
            .toUser(wxMessage.getFromUser())
            .build());

        return null;
    };

    private final WxMaMessageHandler textHandler = (wxMessage, context, service, sessionManager) -> {
        log.info("回复消息事件：{}", wxMessage.toString());

        return null;

    };

    private final WxMaMessageHandler picHandler = (wxMessage, context, service, sessionManager) -> {
        try {
            WxMediaUploadResult uploadResult = service.getMediaService()
                .uploadMedia("image", "png",
                    ClassLoader.getSystemResourceAsStream("tmp.png"));
            service.getMsgService().sendKefuMsg(
                WxMaKefuMessage
                    .newImageBuilder()
                    .mediaId(uploadResult.getMediaId())
                    .toUser(wxMessage.getFromUser())
                    .build());
        } catch (WxErrorException e) {
            e.printStackTrace();
        }

        return null;
    };

    private final WxMaMessageHandler qrcodeHandler = (wxMessage, context, service, sessionManager) -> {
        try {
            final File file = service.getQrcodeService().createQrcode("123", 430);
            WxMediaUploadResult uploadResult = service.getMediaService().uploadMedia("image", file);
            service.getMsgService().sendKefuMsg(
                WxMaKefuMessage
                    .newImageBuilder()
                    .mediaId(uploadResult.getMediaId())
                    .toUser(wxMessage.getFromUser())
                    .build());
        } catch (WxErrorException e) {
            e.printStackTrace();
        }
        return null;
    };
}
