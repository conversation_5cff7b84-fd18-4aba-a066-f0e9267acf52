package org.dromara.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.RoomCheckouts;

/**
* <AUTHOR>
* @description 针对表【biz_room_checkouts(房间入住记录)】的数据库操作Service
* @createDate 2024-03-21 16:24:54
*/
public interface RoomCheckoutsService extends IService<RoomCheckouts> {

    RoomCheckouts getByCustomerId(Long customerId);

    /**
     * 更新退房时间
     * @param roomId 房间id
     * @param customerId 客户id
     * @return 更新结果
     */
    Boolean updateCheckoutDate(Long roomId, Long customerId);

    /**
     * 查询当前客户是否已经办理入住
     * @param customerId 客户id
     * @return 入住结果
     */
    Boolean isMoveInto(Long customerId);
}
