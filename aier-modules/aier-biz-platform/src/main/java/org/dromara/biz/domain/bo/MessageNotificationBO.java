package org.dromara.biz.domain.bo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 微信消息推送bo
 */
@Data
public class MessageNotificationBO {

    /**
     * 消息模版
     */
    @NotBlank(message = "消息模版不能为空")
    private String template;

    /**
     * 排除列表
     */
    private List<String> excludedList;

    /**
     * 排除工作人员
     */
    private Boolean isStaffExcluded = true;

    /**
     * 排除老板
     */
    private Boolean isBossExcluded = true;

    /**
     * 发送数据
     */
    @NotNull(message = "消息数据不能为空")
    private Map<String, String> notificationData;
}
