package org.dromara.biz.service;

import org.dromara.biz.domain.CheckinGiftsRecord;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【biz_checkin_gifts_record(签到礼物领取记录表)】的数据库操作Service
* @createDate 2024-04-09 10:45:52
*/
public interface CheckinGiftsRecordService extends IService<CheckinGiftsRecord> {

    /**
     * 检查用户是否已经领取对应签到天数的礼品
     * @param userId 用户id
     * @param checkInCount 签到天数
     * @return 结果
     */
    Boolean whetherReceive(Long userId, Long checkInCount);
}
