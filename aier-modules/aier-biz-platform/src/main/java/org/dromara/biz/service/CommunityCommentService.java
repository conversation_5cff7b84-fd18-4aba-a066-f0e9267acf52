package org.dromara.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.CommunityComment;
import org.dromara.biz.domain.query.CommunityCommentQuery;
import org.dromara.biz.domain.vo.CommunityCommentVO;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【biz_community_comment(社区动态评论表)】的数据库操作Service
* @createDate 2024-03-25 15:43:35
*/
public interface CommunityCommentService extends IService<CommunityComment> {

    /**
    * 查询评论列表
    * @param query 查询条件
    * @return 返回满足查询条件的评论列表
    */
    List<CommunityCommentVO> queryList(CommunityCommentQuery query);

    /**
    * 获取指定动态的评论数量
    * @param postId 动态ID
    * @return 返回指定动态的评论数量
    */
    Integer getCommentNum(Long postId);

    /**
    * 根据评论id删除评论
    * @param commentIds 要删除的评论ID数组
    * @return 如果删除成功则返回true，否则返回false
    */
    Boolean removeComment(Long[] commentIds);

    /**
     * 根据动态id删除评论
     * @param postIdList 动态ids
     * @return 如果删除成功则返回true，否则返回false
     */
    Boolean removeByPostIds(List<Long> postIdList);

    Map<Long, CommunityComment> queryMapByIds(List<Long> commentIds);
}
