package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 会所活动表
 * @TableName biz_club_activity
 */
@TableName(value ="biz_club_activity", autoResultMap = true)
@Data
public class ClubActivity implements Serializable {

    /**
     * 主键id
     */
    @TableId
    private Long activityId;

    private Long clubId;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 活动标题
     */
    private String title;

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 主图
     */
    private String mainImage;

    /**
     * 活动内容
     */
    private String content;

    /**
     * 视频
     */
    private String video;

    /**
     * 类型 1:图文；2:视频
     */
    private Integer type;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
