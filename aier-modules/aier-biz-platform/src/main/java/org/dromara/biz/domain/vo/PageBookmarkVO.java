package org.dromara.biz.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.dromara.biz.domain.PageBookmarks;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 页面收藏vo
 */
@Data
@AutoMapper(target = PageBookmarks.class)
public class PageBookmarkVO implements Serializable {

    private Long bookmarkId;

    /**
     * 页面路径
     */
    @NotBlank(message = "页面路径不能为空")
    private String pagePath;

    /**
     * 页面标题
     */
    @NotBlank(message = "页面标题不能为空")
    private String pageTitle;

    /**
     * 页面图片
     */
    private String imageUrl;

    /**
     * 页面标签
     */
    private List<String> tag;

    private Date createTime;

    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}
