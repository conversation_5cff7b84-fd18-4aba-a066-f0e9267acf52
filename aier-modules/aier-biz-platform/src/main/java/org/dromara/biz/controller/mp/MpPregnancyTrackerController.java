package org.dromara.biz.controller.mp;

import lombok.RequiredArgsConstructor;
import org.dromara.biz.domain.vo.GestationWeekVO;
import org.dromara.biz.domain.vo.PregnancyTrackerVO;
import org.dromara.biz.service.PregnancyTrackerService;
import org.dromara.common.core.domain.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 小程序孕期助手相关接口
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/mp/pregnancy_tracker")
public class MpPregnancyTrackerController {

    private final PregnancyTrackerService pregnancyTrackerService;

    /**
     * 获取当前用户预产期信息
     */
    @GetMapping("/gestation_weeks")
    public R<GestationWeekVO> queryGestationWeeks() {
        return R.ok(pregnancyTrackerService.queryGestationWeeks());
    }

    /**
     * 获取孕期助手周数据
     */
    @GetMapping
    public R<PregnancyTrackerVO> queryPregnancyTracker(@RequestParam("gestationWeek") Integer gestationWeek) {
        return R.ok(pregnancyTrackerService.queryByGestationWeeks(gestationWeek));
    }
}
