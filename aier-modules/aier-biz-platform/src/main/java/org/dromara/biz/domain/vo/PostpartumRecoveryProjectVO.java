package org.dromara.biz.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.dromara.biz.domain.PostpartumRecoveryProjects;
import org.dromara.common.mybatis.handler.type.StringListTypeHandler;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 产后康复项目
 */
@Getter
@Setter
@AutoMapper(target = PostpartumRecoveryProjects.class)
public class PostpartumRecoveryProjectVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long projectId;

    /**
     * 项目名称
     */
    @NotBlank(message = "产后康复项目名称不能为空")
    private String projectName;

    /**
     * 服务方式
     */
    @NotBlank(message = "产后康复服务方式不能为空")
    private String serviceMode;

    /**
     * 服务分类
     */
    @NotBlank(message = "产后康复服务分类不能为空")
    private String serviceCategory;

    /**
     * 服务次数
     */
    @NotNull(message = "产后康复服务次数不能为空")
    private Integer serviceCount;

    /**
     * 单次时常
     */
    @NotNull(message = "产后康复单次时常不能为空")
    private Integer singleDuration;

    /**
     * 服务功效
     */
    @NotBlank(message = "产后康复服务功效不能为空")
    private String serviceEffect;

    /**
     * 产后康复项目照片
     */
    @NotEmpty(message = "产后康复项目照片不能为空")
    private List<String> displayPhotos;

    /**
     * 视频链接
     */
    private List<String> videos;

    /**
     * 产后康复图文详情照片
     */
    private List<String> descriptionPhotos;

    /**
     * 背景照片
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> backgroundPhotos = new ArrayList<>();


    /**
     * 是否上架
     */
    private Boolean isOnShelf;

    /**
     * 产后康复标签
     */
    private List<String> tag;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 描述
     */
    private String description;

    /**
     * 服务项目
     */
    private String serviceItem;

    /**
     * 签约礼品id
     */
    private Long contractGiftId;

    /**
     * 签约礼品
     */
    private ContractGiftVO contractGift;

    /**
     * 是否领取签约礼品
     */
    private Boolean isExistContractGift;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 咨询次数
     */
    private Integer consultNumber;
}
