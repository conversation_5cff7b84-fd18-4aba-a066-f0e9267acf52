package org.dromara.biz.service;

import org.dromara.biz.domain.CommunityCommentLikes;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_community_comment_likes(社区动态评论点赞表)】的数据库操作Service
* @createDate 2024-04-10 09:25:01
*/
public interface CommunityCommentLikesService extends IService<CommunityCommentLikes> {

    /**
     * 获取评论点赞状态
     * @param commentId 评论id
     * @param userId 用户id
     * @return 返回评论点赞状态
     */
    Boolean getCommentLikeStatus(Long commentId, Long userId);

    /**
     * 删除宝妈社区动态评论点赞
     * @param commentIdList 评论id列表
     * @return 如果删除成功则返回true，否则返回false
     */
    Boolean removeByCommentIds(List<Long> commentIdList);
}
