package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 会所朋友圈动态通知表
 * @TableName biz_feed_post_notification
 */
@TableName(value ="biz_feed_post_notification")
@Data
public class FeedPostNotification implements Serializable {
    /**
     *
     */
    @TableId
    private Long notificationId;

    /**
     * 通知类型（如：点赞、评论）
     */
    private String type;

    /**
     * 发送者id
     */
    private Long senderId;

    /**
     * 接收者id
     */
    private Long receiverId;

    /**
     * 评论id
     */
    private Long commentId;

    /**
     * 动态id
     */
    private Long postId;

    /**
     * 消息
     */
    private String message;

    /**
     * 通知状态（未读/已读）
     */
    private Boolean status;

    /**
     *
     */
    private String createBy;

    /**
     *
     */
    private Date createTime;

    /**
     *
     */
    private String updateBy;

    /**
     *
     */
    private Date updateTime;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 通知类型 点赞
     */
    @TableField(exist = false)
    public static final String TYPE_LIKES = "LIKES";

    /**
     * 通知类型 评论
     */
    @TableField(exist = false)
    public static final String TYPE_COMMENT = "COMMENT";
}
