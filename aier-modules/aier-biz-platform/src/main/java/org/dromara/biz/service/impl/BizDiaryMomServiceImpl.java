package org.dromara.biz.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.BizDiaryMom;
import org.dromara.biz.domain.BizDiaryMomTeam;
import org.dromara.biz.domain.WechatUser;
import org.dromara.biz.domain.bo.diary.DiaryMomBO;
import org.dromara.biz.domain.query.diary.DiaryMomQuery;
import org.dromara.biz.domain.query.employee.EmployeeQuery;
import org.dromara.biz.domain.vo.CustomerVO;
import org.dromara.biz.domain.vo.DiaryMomVO;
import org.dromara.biz.domain.vo.employee.EmployeeVO;
import org.dromara.biz.mapper.BizDiaryMomMapper;
import org.dromara.biz.service.*;
import org.dromara.common.core.enums.AppUserRoleEnum;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <p>
 * 笔记-优质宝妈表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Service
@AllArgsConstructor
@Slf4j
public class BizDiaryMomServiceImpl extends ServiceImpl<BizDiaryMomMapper, BizDiaryMom> implements IBizDiaryMomService {
    IBizDiaryMomTeamService bizDiaryMomTeamService;
    IBizEmployeeService employeeService;
    CustomersService customerService;
    IBizDiaryMomTeamService momTeamService;
    WechatUserService wechatUserService;
    WechatUserRoleService wechatUserRoleService;



    @Override
    public List<CustomerVO> getMomList() {
        List<CustomerVO> momList = customerService.getMomList();
        return momList;
    }

    @Override
    public List<EmployeeVO> getMomTeamList(String roleCode) {
        EmployeeQuery employeeQuery = new EmployeeQuery();
        if (StrUtil.isNotBlank(roleCode)) {
            employeeQuery.setRoleCode(roleCode);
        }
        return  employeeService.queryList(employeeQuery);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insert(DiaryMomBO diaryMomBO) {
        BizDiaryMom bizDiaryMom = MapstructUtils.convert(diaryMomBO, BizDiaryMom.class);
        String uuid = UUID.randomUUID().toString();
        bizDiaryMom.setUuid(uuid.substring(0,5));
        baseMapper.insert(bizDiaryMom);
        List<Long> employeeIds = diaryMomBO.getEmployeeIds();
        List<BizDiaryMomTeam> diaryMomTeamList  = new ArrayList<>();
        for (Long employeeId : employeeIds) {
            BizDiaryMomTeam bizDiaryMomTeam = new BizDiaryMomTeam();
            bizDiaryMomTeam.setMomId(bizDiaryMom.getMomId());
            bizDiaryMomTeam.setEmployeeId(employeeId);
            diaryMomTeamList.add(bizDiaryMomTeam);
        }
        bizDiaryMomTeamService.saveBatch(diaryMomTeamList);
        //关联用户表
        WechatUser wechatUser = wechatUserService.getByTel(diaryMomBO.getTel());
        if (wechatUser == null) {
            wechatUser = new WechatUser();
            wechatUser.setTel(diaryMomBO.getTel());
            wechatUser.setNickname(diaryMomBO.getName());
            wechatUser.setUtype("1");
            wechatUser.setStatus("0");
            wechatUserService.save(wechatUser);
        }
//        bizDiaryMom.setAvatar(wechatUser.getAvatar());
        baseMapper.updateById(bizDiaryMom);
        wechatUserRoleService.createRole(wechatUser.getUserId(), AppUserRoleEnum.CUSTOMER.name());
        return true;
    }

    @Override
    public TableDataInfo<BizDiaryMom> queryPage(DiaryMomQuery query) {
        LambdaQueryWrapper<BizDiaryMom> wrapper = Wrappers.lambdaQuery();
        query.setPageSize(100);
        if (query.getIsShow() != null) {
            wrapper.eq(BizDiaryMom::getIsShow,query.getIsShow());
        }
        Page<BizDiaryMom> page = baseMapper.selectPage(query.build(), wrapper);
        return TableDataInfo.build(page);
    }

    @Override
    public DiaryMomVO detail(Long id,String uuid) {
        BizDiaryMom bizDiaryMom = null;
        if (id==null){
            bizDiaryMom = baseMapper.selectOne(new QueryWrapper<BizDiaryMom>().eq("uuid", uuid));
        }else {
            bizDiaryMom = baseMapper.selectById(id);
        }
        if (bizDiaryMom==null){
            return null;
        }
        DiaryMomVO diaryMomVO = MapstructUtils.convert(bizDiaryMom, DiaryMomVO.class);
        diaryMomVO.setAvatar(bizDiaryMom.getAvatar());
        LambdaQueryWrapper<BizDiaryMomTeam> lqw = Wrappers.lambdaQuery();
        lqw.in(BizDiaryMomTeam::getMomId, bizDiaryMom.getMomId());
        List<BizDiaryMomTeam> list = momTeamService.list(lqw);
        List<Long> employeeIds = list.stream().map(BizDiaryMomTeam::getEmployeeId).toList();
        List<EmployeeVO> employeeVOList = employeeService.queryListByIds(employeeIds);
        if (diaryMomVO != null) {
            diaryMomVO.setEmployees(employeeVOList);
        }
//        if (diaryMomVO != null) {
//            WechatUser wechatUser = wechatUserService.getByTel(diaryMomVO.getTel());
//            if (wechatUser != null){
//                diaryMomVO.setAvatar(wechatUser.getAvatar());
//            }
//        }
        return diaryMomVO;
    }

    @Override
    public BizDiaryMom detailByUserId(Long userId) {
        BizDiaryMom diaryMom = baseMapper.selectOne(new QueryWrapper<BizDiaryMom>().eq("user_id", userId));
        return diaryMom;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateBO(DiaryMomBO diaryMomBO) {
        BizDiaryMom bizDiaryMom = MapstructUtils.convert(diaryMomBO, BizDiaryMom.class);
        baseMapper.updateById(bizDiaryMom);
        List<Long> employeeIds = diaryMomBO.getEmployeeIds();
        LambdaQueryWrapper<BizDiaryMomTeam> lqw = Wrappers.lambdaQuery();
        lqw.in(BizDiaryMomTeam::getMomId, bizDiaryMom.getMomId());
        List<BizDiaryMomTeam> list = momTeamService.list(lqw);
        List<Long> momTeamIds = list.stream().map(BizDiaryMomTeam::getId).toList();
        bizDiaryMomTeamService.removeByIds(momTeamIds);
        List<BizDiaryMomTeam> diaryMomTeamList  = new ArrayList<>();
        for (Long employeeId : employeeIds) {
            BizDiaryMomTeam bizDiaryMomTeam = new BizDiaryMomTeam();
            bizDiaryMomTeam.setMomId(bizDiaryMom.getMomId());
            bizDiaryMomTeam.setEmployeeId(employeeId);
            diaryMomTeamList.add(bizDiaryMomTeam);
        }
        bizDiaryMomTeamService.saveBatch(diaryMomTeamList);
        //关联用户表
        WechatUser wechatUser = wechatUserService.getByTel(diaryMomBO.getTel());
        if (wechatUser == null) {
            wechatUser = new WechatUser();
            wechatUser.setTel(diaryMomBO.getTel());
            wechatUser.setNickname(diaryMomBO.getName());
            wechatUser.setUtype("1");
            wechatUserService.save(wechatUser);
        }
        wechatUser.setStatus("0");
        wechatUserService.updateById(wechatUser);
        wechatUserRoleService.createRole(wechatUser.getUserId(), AppUserRoleEnum.CUSTOMER.name());
        return true;
    }
}
