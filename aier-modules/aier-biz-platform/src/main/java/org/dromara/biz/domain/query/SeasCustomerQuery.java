package org.dromara.biz.domain.query;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class SeasCustomerQuery extends PageQuery {



    /**
     * 跟进人id
     */
    private Long followerId;

    /**
     * 意向度
     */
    private String intentLeve;
    /**
     * 客户姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String tel;

    /**
     * 是否分配
     */
    private Long isAllocation;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM")
    private Date createTime;
}
