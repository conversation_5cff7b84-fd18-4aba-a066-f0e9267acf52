package org.dromara.biz.domain.query.room;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 房间状态查询对象
 */
@Data
public class RoomStatusQuery {

    /**
     * 房型id
     */
    private Long suitesId;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
//    @NotNull(message = "开始时间不能为空")
    private Date startDate;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
//    @NotNull(message = "结束时间不能为空")
    private Date endDate;

    /**
     * 开始时间 yyyy-MM
     */
    @DateTimeFormat(pattern = "yyyy-MM")
    private Date start;

    /**
     * 结束时间 yyyy-MM
     */
    @DateTimeFormat(pattern = "yyyy-MM")
    private Date end;

    /**
     * 月份 month值范围为0-11
     */
    private Integer month;

    /**
     * 年份
     */
    private Integer year;

    /**
     * 是否空房
     */
    private Boolean isEmptyRoom;
}
