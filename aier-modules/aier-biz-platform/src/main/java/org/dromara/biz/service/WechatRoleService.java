package org.dromara.biz.service;

import org.dromara.biz.domain.WechatRole;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【biz_wechat_role(小程序角色表)】的数据库操作Service
* @createDate 2024-09-19 20:26:39
*/
public interface WechatRoleService extends IService<WechatRole> {

    /**
     * 通过角色code获取角色
     */
    WechatRole getByCode(String code);

    /**
     * 根据用户ID查询角色列表
     */
    Set<String> selectRolePermissionByUserId(Long userId);

    /**
     * 根据用户id查询权限列表
     */
    Set<String> selectPermsByUserId(Long userId);
}
