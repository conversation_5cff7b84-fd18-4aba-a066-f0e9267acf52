package org.dromara.biz.service.impl;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.BizBabyInfo;
import org.dromara.biz.domain.FeedPost;
import org.dromara.biz.domain.vo.nurse.BabyCareVO;
import org.dromara.biz.domain.vo.nurse.BabyInfoVO;
import org.dromara.biz.mapper.BizBabyInfoMapper;
import org.dromara.biz.service.BabyInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 宝宝信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-13
 */
@Service
@AllArgsConstructor
@Slf4j
public class BabyInfoServiceImpl extends ServiceImpl<BizBabyInfoMapper, BizBabyInfo> implements BabyInfoService {

    @Override
    public List<BizBabyInfo> getDetailByCustomerId(Long customerId) {
        LambdaQueryWrapper<BizBabyInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(BizBabyInfo::getCustomerId, customerId);
        return baseMapper.selectList(lqw);
    }

    @Override
    public BabyInfoVO getByVo(Long babyId) {
        BabyInfoVO babyInfoVO = baseMapper.getByVo(babyId);
        if (ObjectUtil.isNotNull(babyInfoVO)) {
            if (babyInfoVO.getBirthDate() != null) {
                long liveDays = DateUtil.betweenDay(babyInfoVO.getBirthDate(), DateUtil.date(), true);
                babyInfoVO.setAge(liveDays+"天");
            }
        }
        return babyInfoVO;
    }

    @Override
    public List<BabyCareVO> getBabyAnalysis(Long babyId, Date time) {
        DateTime startTime = DateUtil.beginOfMonth(time);
        DateTime endTime = DateUtil.endOfMonth(time);
        return baseMapper.getBabyAnalysis(babyId,startTime,endTime);
    }
}
