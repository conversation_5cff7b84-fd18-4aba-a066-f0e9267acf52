package org.dromara.biz.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.CustomerFollowUpLogs;
import org.dromara.biz.domain.vo.CustomerFollowUpLogsVO;

/**
* <AUTHOR>
* @description 针对表【biz_customer_follow_up_logs(客户跟进日志)】的数据库操作Service
* @createDate 2024-07-22 18:22:13
*/
public interface CustomerFollowUpLogsService extends IService<CustomerFollowUpLogs> {

    Page<CustomerFollowUpLogsVO> customizePage(Page<CustomerFollowUpLogs> page,
                                               QueryWrapper<CustomerFollowUpLogs> qw);
}
