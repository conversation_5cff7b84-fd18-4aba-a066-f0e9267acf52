package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.handler.type.StringListTypeHandler;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 商品配送表
 * @TableName biz_product_delivery
 */
@TableName(value ="biz_product_delivery", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class ProductDelivery extends TenantEntity implements Serializable {
    /**
     *
     */
    @TableId
    private Long deliveryId;

    /**
     * 商品描述
     */
    private String productDescription;

    /**
     * 商品照片
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> productPhotoUrl;

    /**
     * 视频链接
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> videos;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 图文详情
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> productDetails;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 商品价格
     */
    private BigDecimal productPrice;

    /**
     * 标签
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> tag;

    /**
     * 商品类型 咨询：0；购买：1；
     */
    private String productType;

    /**
     * 购买分类
     */
    private String groupType;

    /**
     * 使用次数
     */
    private Integer useCount;

    /**
     * 服务方式
     */
    private String serviceMode;

    /**
     * 签约礼品id
     */
    private Long contractGiftId;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 咨询次数
     */
    private Integer consultNumber;
}
