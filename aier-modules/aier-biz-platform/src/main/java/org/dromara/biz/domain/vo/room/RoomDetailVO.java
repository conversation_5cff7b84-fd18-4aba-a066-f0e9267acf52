package org.dromara.biz.domain.vo.room;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 房间详情
 */
@Data
public class RoomDetailVO {

    /**
     * 房间id
     */
    private Long roomId;

    /**
     * 房间号
     */
    private String roomNumber;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 客户的用户id
     */
    private Long customerUserId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 入住时间
     */
    private Date checkIn;

    /**
     * 离开时间
     */
    private Date checkOut;

    /**
     * 当前入住状态
     */
    private Integer status;
    /**
     * 房型名称
     */
    private String suiteName;

    /**
     * 服务人员详情列表
     */
    private List<ServicePersonDetail> servicePersonDetails;

    /**
     * 服务人员详情
     */
    @Data
    public static class ServicePersonDetail {

        /**
         * 员工id
         */
        private Long employeeId;

        /**
         * 名称
         */
        private String name;

        /**
         * 角色编码
         */
        private String roleCode;
    }
}
