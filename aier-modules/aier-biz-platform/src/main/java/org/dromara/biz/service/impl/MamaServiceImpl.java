package org.dromara.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.query.FeedPostWeekQuery;
import org.dromara.biz.domain.vo.FeedPostVO;
import org.dromara.biz.domain.vo.customer.MamaInfoVO;
import org.dromara.biz.domain.vo.feedpost.FeedPostDayVO;
import org.dromara.biz.domain.vo.feedpost.FeedPostWeekVO;
import org.dromara.biz.mapper.FeedPostMapper;
import org.dromara.biz.mapper.MamaMapper;
import org.dromara.biz.service.MamaService;
import org.dromara.common.core.enums.UgcSuggestEnum;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class MamaServiceImpl implements MamaService {

    private final MamaMapper mamaMapper;
    private final FeedPostMapper feedPostMapper;

    @Override
    public MamaInfoVO getMamaInfo(Long userId) {
        String tenantId = TenantHelper.getTenantId();
        return TenantHelper.ignore(()-> mamaMapper.getMamaInfo(userId, tenantId));
    }

    @Override
    public List<FeedPostWeekVO> getMamaFeedPostWeek(FeedPostWeekQuery query) {
        Long userId = LoginHelper.getUserId();
        QueryWrapper<FeedPostVO> wrapper = buildQueryWrapper(query);
        List<FeedPostVO> feedPostList = feedPostMapper.getFeedPostList(wrapper, userId);
        if(ObjectUtil.isNull(query.getCheckInTime())) {
            return null;
        }
        Map<Integer, List<FeedPostVO>> map = groupingByWeek(feedPostList, query.getCheckInTime());
        return map.entrySet().stream().map(item->{
            FeedPostWeekVO feedPostWeekVO = new FeedPostWeekVO();
            feedPostWeekVO.setWeek(StrUtil.format("第{}周", item.getKey()));
            feedPostWeekVO.setPosts(item.getValue());
            return feedPostWeekVO;
        }).toList();
    }

    @Override
    public List<FeedPostDayVO> getMamaDayPostList(FeedPostWeekQuery query){
        QueryWrapper<FeedPostVO> wrapper = buildQueryWrapper(query);
        List<FeedPostVO> feedPostList = feedPostMapper.getFeedPostList(wrapper, null);
        if(ObjectUtil.isNull(query.getCheckInTime())) {
            return null;
        }
        Map<Integer, List<FeedPostVO>> map = groupingByDay(feedPostList, query.getCheckInTime());
        return map.entrySet().stream()
                .filter(entry -> {
                    Integer day = entry.getKey();
                    if(ObjectUtil.isNotNull(query.getDayNum())){
                        return Objects.equals(day, query.getDayNum());
                    }
                    return true;
                })
                .map(item->{
            List<FeedPostDayVO.TagListHolder> tagListHolderList = item.getValue().stream()
                    .filter(t -> ObjectUtil.isNotNull(t.getPostTagInfo()))
                    .map(t -> {
                FeedPostDayVO.TagListHolder tagListHolder = new FeedPostDayVO.TagListHolder();
                tagListHolder.setName(t.getPostTagInfo().getTagName() == null ? "" : t.getPostTagInfo().getTagName());
                tagListHolder.setTaskNodeId(t.getPostTagInfo().getTagId());
                return tagListHolder;
            }).toList();

            FeedPostDayVO feedPostDay = new FeedPostDayVO();
            feedPostDay.setDays(StrUtil.format("第{}天", item.getKey()));
            feedPostDay.setDayNum(item.getKey());
            feedPostDay.setPosts(item.getValue());
            feedPostDay.setTagListHolderList(tagListHolderList);
            return feedPostDay;
        }).toList();
    }

    private QueryWrapper<FeedPostVO> buildQueryWrapper(FeedPostWeekQuery query) {
        QueryWrapper<FeedPostVO> qw = new QueryWrapper<>();
        if (CollUtil.isNotEmpty(query.getTaskNodeIdList())) {
            qw.in("tn.task_node_id", query.getTaskNodeIdList());
        }
        if (ObjectUtil.isNotNull(query.getWeekNum())) {
            Date checkInTime = query.getCheckInTime();
            if (ObjectUtil.isNull(checkInTime)) throw new ServiceException("checkInTime is null");
            Date startTime;
            Date endTime;
            if (query.getWeekNum() == 1) {
                startTime = DateUtil.beginOfDay(checkInTime);
                endTime = DateUtil.offsetDay(checkInTime, 6);
            } else {
                startTime = DateUtil.offsetDay(checkInTime, (query.getWeekNum() - 1) * 7);
                endTime = DateUtil.offsetDay(startTime, 6);
            }
            qw.between("fp.create_time", DateUtil.beginOfDay(startTime), DateUtil.endOfDay(endTime));
        }
        if (ObjectUtil.isNotNull(query.getUserId())) {
            qw.and(w ->
                    w.eq("ps.target_user_id", query.getUserId())
                            .or().eq("fp.author_id", query.getUserId()));
        }
        qw.eq("fp.suggest", UgcSuggestEnum.PASS.getSuggest());
        qw.orderByDesc("is_featured", "fp.create_time");
        return qw;
    }

    private Map<Integer, List<FeedPostVO>> groupingByWeek(List<FeedPostVO> feedPostList, Date checkInTime) {
        long checkInTimeMillis = getTimeInMillis(checkInTime);
        return feedPostList.stream()
                .filter(item -> item.getCreateTime().after(checkInTime))
                .collect(Collectors.groupingBy(post -> {
                    handlerConvert(post);
                    Calendar calendarData = Calendar.getInstance();
                    calendarData.setTime(post.getCreateTime());
                    long createTimeMillis = calendarData.getTimeInMillis();
                    long daysBetween = (createTimeMillis - checkInTimeMillis) / (1000 * 60 * 60 * 24);
                    return (int) (daysBetween / 7) + 1;
                }));
    }

    private Map<Integer, List<FeedPostVO>> groupingByDay(List<FeedPostVO> feedPostList, Date checkInTime) {
        long checkInTimeMillis = getTimeInMillis(checkInTime);
        return feedPostList.stream()
                .filter(item -> item.getCreateTime().after(checkInTime))
                .collect(Collectors.groupingBy(post -> {
                    handlerConvert(post);
                    Calendar calendarData = Calendar.getInstance();
                    calendarData.setTime(post.getCreateTime());
                    long createTimeMillis = calendarData.getTimeInMillis();
                    long daysBetween = (createTimeMillis - checkInTimeMillis) / (1000 * 60 * 60 * 24);
                    return (int) (daysBetween) + 1;
                }));
    }

    private long getTimeInMillis(Date date) {
        Calendar calendarCheckIn = Calendar.getInstance();
        calendarCheckIn.setTime(date);
        calendarCheckIn.set(Calendar.HOUR_OF_DAY, 0);
        calendarCheckIn.set(Calendar.MINUTE, 0);
        calendarCheckIn.set(Calendar.SECOND, 0);
        calendarCheckIn.set(Calendar.MILLISECOND, 0);
        return calendarCheckIn.getTimeInMillis();
    }

    private void handlerConvert(FeedPostVO post) {
        post.setHisTimeStr(DateUtils.formatTime(post.getCreateTime()));
        String type = post.getType();
        if ("USER".equals(type) && ObjectUtil.isNotNull(post.getCustomerInfo())) {
            FeedPostVO.CustomerInfo customerInfo = post.getCustomerInfo();
            Date checkIn = customerInfo.getCheckIn();
            if (ObjectUtil.isNotNull(checkIn)) {
                long checkInDays = DateUtil.betweenDay(checkIn, post.getCreateTime(), true);
                customerInfo.setCheckInDays(checkInDays + 1);
            }
        }
        if ("STAFF".equals(type) && ObjectUtil.isNotNull(post.getStaffInfo())) {
            //员工职位
            FeedPostVO.EmployeeInfo staffInfo = post.getStaffInfo();
            Date serviceTime = staffInfo.getServiceTime();
            if (ObjectUtil.isNotNull(serviceTime)) {
                long serviceDays = DateUtil.betweenDay(serviceTime, post.getCreateTime(), true);
                staffInfo.setServiceDays(serviceDays + 1);
            }
            Date practiceTime = staffInfo.getPracticeTime();
            if (ObjectUtil.isNotNull(practiceTime)) {
                Long betweenYear = DateUtil.betweenYear(practiceTime, DateUtil.date(), true);
                staffInfo.setYearsEmployment(String.format("%s年", betweenYear));
            }
        }
    }
}
