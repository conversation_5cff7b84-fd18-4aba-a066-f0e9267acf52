package org.dromara.biz.service;

import org.dromara.biz.domain.BizSytleDynamic;
import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.vo.TaskNodeVO;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * <p>
 * 动态样式表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-30
 */
public interface IBizStyleDynamicService extends IService<BizSytleDynamic> {
    TableDataInfo<BizSytleDynamic> queryPage(PageQuery query);
    Boolean saveB(BizSytleDynamic bizSytleDynamic);
    BizSytleDynamic queryShowStyle();
}
