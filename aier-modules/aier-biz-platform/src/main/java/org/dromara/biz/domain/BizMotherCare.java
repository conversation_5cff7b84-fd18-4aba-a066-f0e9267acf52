package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.dromara.common.mybatis.handler.type.StringListTypeHandler;
import org.dromara.common.tenant.core.TenantEntity;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 宝妈护理记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-24
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("biz_mother_care")
public class BizMotherCare extends TenantEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 客户ID
     */
    private Long customerId;

    /**
     * 记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date recordTime;

    /**
     * 入住体温（单位：摄氏度）
     */
    private String checkinTemperature;

    /**
     * 入住重量（单位：千克）
     */
    private String checkinWeight;

    /**
     * 收缩压（单位：mmHg）
     */
    private String systolicPressure;

    /**
     * 舒张压（单位：mmHg）
     */
    private String diastolicPressure;

    /**
     * 血糖（单位：mmol/L）
     */
    private String bloodSugar;

    /**
     * 血压（单位：mmHg）
     */
    private String bloodPressure;

    /**
     * 睡眠情况
     */
    private String sleepCondition;

    /**
     * 饮食情况
     */
    private String dietCondition;

    /**
     * 食欲
     */
    private String appetite;

    /**
     * 乳房情况
     */
    private String breastCondition;

    /**
     * 乳汁情况
     */
    private String milkCondition;

    /**
     * 乳头情况
     */
    private String nippleCondition;

    /**
     * 情绪情况
     */
    private String moodCondition;

    /**
     * 宫体情况
     */
    private String uterineCondition;

    /**
     * 恶露情况
     */
    private String lochiaCondition;

    /**
     * 恶露颜色
     */
    private String lochiaColor;

    /**
     * 腹直肌分离（单位：厘米）
     */
    private String diastasisRecti;

    /**
     * 伤口情况（顺/剖）
     */
    private String woundCondition;

    /**
     * 痔疮情况
     */
    private String hemorrhoids;

    /**
     * 下肢水肿情况
     */
    private String lowerLimbEdema;

    /**
     * 用药情况
     */
    private String medication;

    /**
     * 备注信息
     */
    private String notes;

    /**
     * 照片列表（存储图片路径或URL，JSON格式）
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> photoList;

}
