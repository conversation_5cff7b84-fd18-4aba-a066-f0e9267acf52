package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 笔记推送记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-22
 */
@Data
@TableName("biz_diary_reminders")
public class BizDiaryReminders implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    private String tenantId;

    /**
     * 提醒事项标题
     */
    private String title;

    /**
     * 提醒事项描述
     */
    private String description;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 笔记id
     */
    private Long diaryId;

    /**
     * 宝妈id
     */
    private Long momId;

    /**
     * 提醒日期
     */
    private Date reminderDate;

    /**
     * 提醒状态
     */
    private Boolean status;

    private Date createTime;

    private Date updateTime;
    /**
     * 日期 MM-dd
     */
    @TableField(exist = false)
    private String date;

    /**
     * 时间 HH:mm:ss
     */
    @TableField(exist = false)
    private String time;

    private String openid;

    private String appid;
}
