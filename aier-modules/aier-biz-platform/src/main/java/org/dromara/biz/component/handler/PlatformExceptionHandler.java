package org.dromara.biz.component.handler;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * 平台异常处理器
 */
@Slf4j
@RestControllerAdvice
public class PlatformExceptionHandler {

    /**
     * 微信异常
     */
//    @ExceptionHandler(WxErrorException.class)
//    public R<Void> handleWxException(WxErrorException e, HttpServletRequest request) {
//        String requestURI = request.getRequestURI();
//        log.error("请求地址'{}',微信api异常'{}'", requestURI, e.getMessage());
//        return R.fail(e.getMessage());
//    }
}
