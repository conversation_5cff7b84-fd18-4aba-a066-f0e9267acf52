package org.dromara.biz.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Date;

/**
 * 用户分析列表
 */
@Data
public class UserAnalyseStatsVO {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 名称
     */
    private String nickname;
    /**
     * 性别
     */
    private String sex;
    /**
     * 年龄
     */
    private Integer age;

    /**
     * 用户类型
     */
    private String type;
    /**
     * 手机号
     */
    private String tel;
    /**
     * 预产期
     */
    private Date dueDate;
    /**
     * 访问次数
     */
    private Integer pv;
    /**
     * 浏览时间
     */
    private Long viewTime;
    /**
     * 浏览时间字符串
     */
    private String viewTimeStr;

    /**
     * 页面访问量 （总共访问多少个页面）
     */
    private Integer pageNum;

    /**
     * 秒数转字符串
     * @return 字符串
     */
    @JsonIgnore
    public void convertSeconds() {
        Long hours = this.viewTime / 3600L;
        Long minutes = (this.viewTime % 3600) / 60L;
        Long remainingSeconds = this.viewTime % 60L;
        this.viewTimeStr =  hours + "小时 " + minutes + "分钟 " + remainingSeconds + "秒";
    }
}
