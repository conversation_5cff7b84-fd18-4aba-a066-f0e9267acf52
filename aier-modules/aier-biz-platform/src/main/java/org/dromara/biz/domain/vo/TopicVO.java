package org.dromara.biz.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.dromara.biz.domain.Topics;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 话题vo
 */
@Data
@AutoMapper(target = Topics.class)
public class TopicVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long topicId;

    /**
     * 话题名称
     */
    @NotBlank(message = "话题名称不能为空")
    private String topicName;

    /**
     * 话题照片
     */
    @NotEmpty(message = "话题照片不能为空")
    private List<String> topicPhotos;

    /**
     * 话题开始时间
     */
    @NotNull(message = "话题开始时间不能为空")
    private Date topicStartTime;

    /**
     * 话题结束时间
     */
    @NotNull(message = "话题结束时间不能为空")
    private Date topicEndTime;

    /**
     * 上线开始时间
     */
    private Date onlineStartTime;

    /**
     * 上线结束时间
     */
    private Date onlineEndTime;

    /**
     * 在线状态
     */
    private Boolean onlineStatus;

    /**
     * 话题状态 0=未开始 1=进行中 2=已结束
     */
    private String topicStatus;

    /**
     * 参与人列表
     */
    private List<WechatUserVO> userList;

    /**
     * 收藏数量
     */
    private Integer bookmarkNum;

    /**
     * 参与话题数量
     */
    private Integer postNum;

    /**
     * 话题描述&简介
     */
    private String topicDescription;

    /**
     * 是否订阅
     */
    private Boolean isSubscribe;

    private Date createTime;

    private Date updateTime;
}
