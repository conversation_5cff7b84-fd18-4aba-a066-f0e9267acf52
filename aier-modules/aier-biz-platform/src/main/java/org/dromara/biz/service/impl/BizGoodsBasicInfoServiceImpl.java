package org.dromara.biz.service.impl;

import org.dromara.biz.domain.BizGoodsBasicInfo;
import org.dromara.biz.mapper.BizGoodsBasicInfoMapper;
import org.dromara.biz.service.IBizGoodsBasicInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.common.core.domain.R;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 商品基础信息 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-09
 */
@Service
public class BizGoodsBasicInfoServiceImpl extends ServiceImpl<BizGoodsBasicInfoMapper, BizGoodsBasicInfo> implements IBizGoodsBasicInfoService {

    @Override
    public R<Boolean> saveGoodsBasicInfo(List<BizGoodsBasicInfo> bizGoodsBasicInfo) {
        for (BizGoodsBasicInfo bizGoodsBasicInfo1 : bizGoodsBasicInfo) {
            if (bizGoodsBasicInfo1.getId() == null) {
                this.save(bizGoodsBasicInfo1);
            }else {
                this.updateById(bizGoodsBasicInfo1);
            }
        }
        return R.ok(true);
    }
}
