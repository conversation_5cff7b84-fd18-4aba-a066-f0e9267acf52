package org.dromara.biz.service;

import org.dromara.biz.domain.FeedPostComment;
import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.vo.FeedPostCommentVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【biz_feed_post_comment(会所朋友圈动态评论表)】的数据库操作Service
* @createDate 2024-03-15 10:57:12
*/
public interface FeedPostCommentService extends IService<FeedPostComment> {

    /**
     * 获取动态评论列表
     * @param postSet 动态ID集合
     * @return 动态评论列表
     */
    Map<Long, List<FeedPostCommentVO>> getGroupMap(Set<Long> postSet);

    Map<Long, FeedPostComment> feedPostCommentService(List<Long> commentIds2);
}
