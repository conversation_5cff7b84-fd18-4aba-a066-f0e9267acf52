package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 优惠券
 * @TableName biz_coupons
 */
@TableName(value ="biz_coupons")
@Data
@EqualsAndHashCode(callSuper = true)
public class Coupons extends TenantEntity implements Serializable {

    /**
     *
     */
    @TableId
    private Long couponId;

    /**
     * 名称
     */
    private String couponName;

    /**
     * 描述
     */
    private String description;

    /**
     * 金额
     */
    private BigDecimal price;

    /**
     * 图片
     */
    private String image;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
