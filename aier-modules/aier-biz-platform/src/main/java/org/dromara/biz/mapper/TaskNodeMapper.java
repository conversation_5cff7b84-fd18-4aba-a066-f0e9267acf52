package org.dromara.biz.mapper;

import org.apache.ibatis.annotations.Param;
import org.dromara.biz.domain.TaskNode;
import org.dromara.biz.domain.query.node.TaskNodeQuery;
import org.dromara.biz.domain.vo.TaskNodeVO;
import org.dromara.common.mybatis.core.mapper.BaseMapperPlus;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_task_node(服务节点表)】的数据库操作Mapper
* @createDate 2024-05-18 16:30:24
* @Entity org.dromara.biz.domain.TaskNode
*/
public interface TaskNodeMapper extends BaseMapperPlus<TaskNode, TaskNodeVO> {

    List<TaskNodeVO> queryList(@Param("params") TaskNodeQuery params);
}




