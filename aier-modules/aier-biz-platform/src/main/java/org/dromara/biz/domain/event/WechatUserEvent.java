package org.dromara.biz.domain.event;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;
import org.springframework.core.ResolvableType;
import org.springframework.core.ResolvableTypeProvider;

/**
 * Created with DummyPuppy
 * 用户事件绑定事件
 * @Author: vw-DummyPuppy
 * @Date: 2024-03-22-10:29
 * @Description: 绑定用户客户以及员工的事件实现
 */
@Getter
@Setter
public class WechatUserEvent<T> extends ApplicationEvent implements ResolvableTypeProvider {

    private T data;

    public WechatUserEvent(Object source) {
        super(source);
    }
    public WechatUserEvent(Object source, T data) {
        super(source);
        this.data = data;
    }

    /**
     * 处理泛型类型
     * @return
     */
    @Override
    public ResolvableType getResolvableType() {
        return ResolvableType.forClassWithGenerics(getClass(), ResolvableType.forInstance(getData()));
    }
}
