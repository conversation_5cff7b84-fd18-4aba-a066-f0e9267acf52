package org.dromara.biz.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;
import org.dromara.biz.domain.ProductDelivery;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商品配送vo
 */
@Data
@AutoMapper(target = ProductDelivery.class)
public class ProductDeliveryVO implements Serializable {

    /**
     * 商品配送id
     */
    private Long deliveryId;

    /**
     * 商品描述
     */
    private String productDescription;

    /**
     * 商品照片
     */
    @NotEmpty(message = "商品照片不能为空")
    private List<String> productPhotoUrl;

    /**
     * 视频链接
     */
    private List<String> videos;

    /**
     * 商品名称
     */
    @NotBlank(message = "商品名称不能为空")
    private String productName;

    /**
     * 图文详情
     */
    @NotEmpty(message = "图文详情不能为空")
    private List<String> productDetails;

    /**
     * 标签
     */
    @NotEmpty(message = "标签")
    private List<String> tag;

    /**
     * 商品价格
     */
    private BigDecimal productPrice;

    /**
     * 商品类型 咨询：0；购买：1；
     */
    private String productType;

    /**
     * 购买分类
     */
    private String groupType;

    /**
     * 使用次数
     */
    private Integer useCount;

    /**
     * 服务方式
     */
    private String serviceMode;

    /**
     * 签约礼品id
     */
    private Long contractGiftId;

    /**
     * 签约礼品
     */
    private ContractGiftVO contractGift;

    /**
     * 是否领取签约礼品
     */
    private Boolean isExistContractGift;

    private Long productId;

    private Date createTime;

    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 咨询次数
     */
    private Integer consultNumber;
}
