package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * 默认节点表
 * @TableName biz_default_nodes
 */
@TableName(value ="biz_default_nodes")
@Data
public class DefaultNodes implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 排序
     */
    private String shortOrder;

    /**
     * 节点类型
     */
    private String type;

    /**
     * 角色编码
     */
    private String roleCode;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}