package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 客户标签表
 * @TableName biz_customer_tag
 */
@TableName(value ="biz_customer_tag")
@Data
public class CustomerTag implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 标签描述
     */
    private String tagDesc;

    /**
     * 排序
     */
    private Integer tagSort;

    /**
     * 
     */
    private Date createTime;

    /**
     * 
     */
    private Date updateTime;

    /**
     * 
     */
    private String tenantId;

    /**
     * 标签类型（REGULAR：普通标签；OPPORTUNITY：机会标签）
     */
    private String tagType;

    /**
     *
     * 标签意向度
     */
    private String tagIntentLevel;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}