package org.dromara.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.PregnancyTracker;
import org.dromara.biz.domain.query.PregnancyTrackerQuery;
import org.dromara.biz.domain.vo.GestationWeekVO;
import org.dromara.biz.domain.vo.PregnancyTrackerVO;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
* <AUTHOR>
* @description 针对表【biz_pregnancy_tracker(孕期追踪表)】的数据库操作Service
* @createDate 2024-04-09 16:09:17
*/
public interface PregnancyTrackerService extends IService<PregnancyTracker> {

    TableDataInfo<PregnancyTrackerVO> queryPage(PregnancyTrackerQuery query);

    PregnancyTrackerVO getInfoById(Long trackerId);

    Boolean save(PregnancyTrackerVO pregnancyTrackerVO);

    Boolean update(PregnancyTrackerVO pregnancyTrackerVO);

    GestationWeekVO queryGestationWeeks();

    PregnancyTrackerVO queryByGestationWeeks(Integer gestationWeek);
}
