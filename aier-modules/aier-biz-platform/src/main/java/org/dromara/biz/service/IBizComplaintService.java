package org.dromara.biz.service;

import org.dromara.biz.domain.BizComplaint;
import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.vo.ComplaintVo;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;

/**
 * <p>
 * 用户投诉表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-22
 */
public interface IBizComplaintService extends IService<BizComplaint> {
    /**
     * 获取某个用户投诉列表
     * @param userId
     * @return
     */
    List<ComplaintVo> queryComplaintVoByUser(Long userId);

    /**
     * 分页查询所有投诉
     * @param query
     * @return
     */
    TableDataInfo<ComplaintVo> queryPage(PageQuery query);

    /**
     * 处理投诉
     * @param complaintVo
     * @return
     */
    Boolean handle(ComplaintVo complaintVo);
}
