package org.dromara.biz.domain.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;

@Data
@Accessors(chain = true)
public class PackageHomeVO {

    /**
     * 主键id
     */
    private Long packageId;

    /**
     * 套餐名称
     */
    private String packageName;

    /**
     * 套餐价格
     */
    private BigDecimal packagePrice;

    /**
     * 入住天数
     */
    private Integer stayDays;

    /**
     * 房型
     */
    private String roomType;

    /**
     * 朝向
     */
    private String orientation;

    /**
     * 室外景观
     */
    private List<String> outdoorFeatures;

    /**
     * 是否支持家人陪住
     */
    private Boolean isFamilyAccommodation;

    /**
     * 母婴照护模式
     */
    private String maternityCareModel;

    /**
     * 陪护人员类型
     */
    private String attendantType;

    /**
     * 陪护人员配置
     */
    private String attendantConfiguration;
}
