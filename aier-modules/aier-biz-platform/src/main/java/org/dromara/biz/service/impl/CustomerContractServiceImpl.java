package org.dromara.biz.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.biz.domain.CustomerContract;
import org.dromara.biz.domain.bo.customer.CollectContractPaymentBO;
import org.dromara.biz.domain.query.customer.CustomerContractQuery;
import org.dromara.biz.domain.vo.customer.CustomerContractVO;
import org.dromara.biz.mapper.CustomerContractMapper;
import org.dromara.biz.service.CustomerContractService;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_customer_contract(客户签约表)】的数据库操作Service实现
* @createDate 2024-09-04 17:08:52
*/
@Service
public class CustomerContractServiceImpl extends ServiceImpl<CustomerContractMapper, CustomerContract>
    implements CustomerContractService{

    @Override
    public List<CustomerContractVO> getContractList(CustomerContractQuery query) {
        autoUpdateOrder();
        return baseMapper.getContractList(buildWrapper(query));
    }

    @Override
    public CustomerContractVO getContractDetail(Long customerContractId) {
        return baseMapper.getContractDetail(customerContractId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean collectContractPayment(CollectContractPaymentBO collectContractPaymentBO){
        LambdaUpdateWrapper<CustomerContract> luw = Wrappers.lambdaUpdate();
        luw.eq(CustomerContract::getId, collectContractPaymentBO.getCustomerContractId());
        luw.set(CustomerContract::getContractAmount, collectContractPaymentBO.getAmount());
        luw.set(CustomerContract::getActualPaymentType, 1);
        luw.setSql("payment_amount = payment_amount + {0}", collectContractPaymentBO.getAmount());
        return update(luw);
    }

    @Override
    public TableDataInfo<CustomerContractVO> getContractPage(CustomerContractQuery query) {
        autoUpdateOrder();
        if(StpUtil.hasRole("SALES")){
            Long userId = LoginHelper.getUserId();
            query.setUserId(userId);
        }
//        update()
        Page<CustomerContractVO> page = baseMapper.getContractPage(query.build(), buildWrapper(query));
        return TableDataInfo.build(page);
    }

    /**
     * 自动更新订单
     */
    public void autoUpdateOrder(){
        baseMapper.autoUpdateOrder();
        baseMapper.autoUpdateOrderRes();
        baseMapper.autoUpdateOrderOcc();
    }

    @Override
    public CustomerContract getCustomerContract(Long customerId, Long roomId) {
        QueryWrapper<CustomerContract> qw = new QueryWrapper<>();
        if (customerId != null) {
            qw.eq("customer_id", customerId);
        }
        if (roomId != null) {
            qw.eq("room_id", roomId);
        }
        List<CustomerContract> customerContracts = baseMapper.selectList(qw);
        if (customerContracts!=null && customerContracts.size()>0){
            return customerContracts.get(0);
        }
        return null;
    }

    /**
     * 构造查询条件
     */
    private QueryWrapper<CustomerContract> buildWrapper(CustomerContractQuery query) {
        QueryWrapper<CustomerContract> qw = Wrappers.query();
        qw.isNull("cc.is_cancel");
        if(ObjectUtil.isNotNull(query.getActualPaymentType())){
            qw.eq("cc.actual_payment_type", query.getActualPaymentType());
        }
        if(ObjectUtil.isNotNull(query.getUserId())){
            qw.eq("c.follower_id", query.getUserId());
        }
        if(ObjectUtil.isNotNull(query.getTagId())){
            qw.eq("c.customer_tag_id", query.getTagId());
        }
        if(ObjectUtil.isNotNull(query.getPackageId())){
            qw.eq("p.package_id", query.getPackageId());
        }
        if(ObjectUtil.isNotNull(query.getSource())){
            qw.eq("c.source", query.getSource());
        }
        if(ObjectUtil.isNotNull(query.getContractSignDate())){
            qw.ge("cc.contract_sign_date", DateUtil.beginOfDay(query.getContractSignDate()));
            qw.le("cc.contract_sign_date", DateUtil.endOfDay(query.getContractSignDate()));
        }

        //月份筛选
        if(ObjectUtil.isNotNull(query.getMonthDate())){
            DateTime startDate = DateUtil.beginOfMonth(query.getMonthDate());
            DateTime endDate = DateUtil.endOfMonth(query.getMonthDate());
            qw.between("cc.contract_sign_date", DateUtil.beginOfDay(startDate), DateUtil.endOfDay(endDate));
        }

        Date now = DateUtil.date();

        if(ObjectUtil.isNotNull(query.getDateType()) && 1 == query.getDateType()){
            // 近一个月
            DateTime startDate = DateUtil.offsetDay(now, -30);
            qw.between("cc.contract_sign_date", DateUtil.beginOfDay(startDate), DateUtil.endOfDay(now));
        }else if(ObjectUtil.isNotNull(query.getDateType()) && 2 == query.getDateType()){
            // 近三个月
            DateTime startDate = DateUtil.offsetDay(now, -90);
            qw.between("cc.contract_sign_date", DateUtil.beginOfDay(startDate), DateUtil.endOfDay(now));
        } else if(ObjectUtil.isNotNull(query.getDateType()) && 3 == query.getDateType()){
            // 近半年
            DateTime startDate = DateUtil.offsetDay(now, -180);
            qw.between("cc.contract_sign_date", DateUtil.beginOfDay(startDate), DateUtil.endOfDay(now));
        } else if(ObjectUtil.isNotNull(query.getDateType()) && 4 == query.getDateType()){
            // 近一年
            DateTime startDate = DateUtil.offsetDay(now, -360);
            qw.between("cc.contract_sign_date", DateUtil.beginOfDay(startDate), DateUtil.endOfDay(now));
        } else if(ObjectUtil.isNotNull(query.getDateType()) && 5 == query.getDateType()){
            // 自定时间范围查询
            if(ObjectUtil.isNull(query.getStartDate()) || ObjectUtil.isNull(query.getEndDate())){
                throw new ServiceException("自定义查询时开始时间和结束时间不能为空！");
            }
            qw.between("cc.contract_sign_date",
                    DateUtil.beginOfDay(query.getStartDate()), DateUtil.endOfDay(query.getEndDate()));
        }
        if(StrUtil.isNotBlank(query.getKeyword())){
            qw.and(wrapper->
                    wrapper.like("c.name",query.getKeyword()).or().like("c.tel", query.getKeyword()));
        }
        if(ObjectUtil.isNotNull(query.getCheckinStatus())){
            qw.eq("c.is_arrived_at_store", query.getCheckinStatus());
        }
        qw.orderByDesc("cc.contract_sign_date");
        return qw;
    }
}




