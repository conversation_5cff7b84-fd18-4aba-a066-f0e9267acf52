package org.dromara.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.biz.domain.FeedPostPhotos;
import org.dromara.biz.mapper.FeedPostPhotosMapper;
import org.dromara.biz.service.FeedPostPhotosService;
import org.dromara.common.core.constant.UgcConstants;
import org.dromara.common.core.enums.UgcSuggestEnum;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【biz_feed_post_photos(朋友圈图片表)】的数据库操作Service实现
 * @createDate 2024-06-24 19:57:41
 */
@Service
public class FeedPostPhotosServiceImpl extends ServiceImpl<FeedPostPhotosMapper, FeedPostPhotos>
    implements FeedPostPhotosService {

    @Override
    public Map<Long, List<String>> getPhotosByPostIds(Set<Long> postIds) {
        if (CollectionUtils.isEmpty(postIds)) {
            return Map.of();
        }
        LambdaQueryWrapper<FeedPostPhotos> lqw = Wrappers.lambdaQuery();
        lqw.in(FeedPostPhotos::getPostId, postIds);
        List<FeedPostPhotos> photos = list(lqw);
        return photos.stream()
            .collect(Collectors.toMap(FeedPostPhotos::getPostId, photo -> {
                String suggest = photo.getSuggest();
                //待审查或者审查中
                if (UgcSuggestEnum.REVIEW.getSuggest().equals(suggest)
                    || UgcSuggestEnum.PENDING.getSuggest().equals(suggest)) {
                    return new ArrayList<>(List.of(UgcConstants.IMG_REVIEW));
                }
                //审查不通过
                if (UgcSuggestEnum.RISKY.getSuggest().equals(suggest)) {
                    return new ArrayList<>(List.of(UgcConstants.IMG_RISKY));
                }
                return new ArrayList<>(List.of(photo.getUrl()));
            }, (o1, o2) -> {
                o1.addAll(o2);
                return o1;
            }));
    }
}




