package org.dromara.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.Employee;
import org.dromara.biz.domain.Rooms;
import org.dromara.biz.domain.bo.employee.EmployeeAuditBO;
import org.dromara.biz.domain.bo.employee.EmployeeBO;
import org.dromara.biz.domain.bo.employee.EmployeeRegisterBO;
import org.dromara.biz.domain.bo.employee.EmployeeResetPwdBO;
import org.dromara.biz.domain.query.employee.EmployeeQuery;
import org.dromara.biz.domain.vo.employee.EmployeeVO;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;

/**
 * <p>
 * 用户人员表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
public interface IBizEmployeeService extends IService<Employee> {

    /**
     * 创建员工
     */
    Boolean create(EmployeeBO employeeBO);

    /**
     * 修改员工
     */
    Boolean update(EmployeeBO employeeBO);

    /**
     * 查询员工列表
     */
    TableDataInfo<EmployeeVO> queryPage(EmployeeQuery query);

    /**
     * 通过手机号查询员工
     */
    EmployeeVO queryByPhone(String phone);

    /**
     * 通过id查询员工详细信息
     */
    EmployeeVO detail(Long id);
    /**
     * 通过id查询员工详细信息
     */
    EmployeeVO detailByUserId(Long userId);

    /**
     * 构建销售端登录用户
     */
    LoginUser buildLoginUser(EmployeeVO employeeVO);

    /**
     * 查询普通销售员工列表
     */
    List<EmployeeVO> getSalesOptions();

    /**
     * 查询当前登录员工详情
     */
    EmployeeVO loginDetail();

    /**
     * 删除员工
     */
    Boolean delete(Long id);

    /**
     * 禁用员工
     */
    Boolean disable(Long id, Boolean disable);

    /**
     * 重置密码
     */
    Boolean resetPassword(EmployeeResetPwdBO employeeResetPwdBO);

    /**
     * 员工注册
     */
    Boolean register(EmployeeRegisterBO registerBO);

    /**
     * 员工审核
     */
    Boolean audit(EmployeeAuditBO auditBO);

    /**
     * 查询员工列表
     */
    List<EmployeeVO> queryList(EmployeeQuery query);

    /**
     * 通过用户id获取员工绑定房间
     */
    List<Rooms> getEmployeeRoomsByUserId(Long userId);

    /**
     * 获取员工列表ids、
     * @param employeeIds
     * @return
     */
    List<EmployeeVO> queryListByIds(List<Long> employeeIds);
}
