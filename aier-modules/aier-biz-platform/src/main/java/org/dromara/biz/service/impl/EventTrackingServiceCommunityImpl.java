package org.dromara.biz.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.biz.domain.EventTracking;
import org.dromara.biz.domain.EventTrackingCommunity;
import org.dromara.biz.domain.ServiceProcedureSteps;
import org.dromara.biz.domain.TaskNode;
import org.dromara.biz.domain.bo.EventTrackingBO;
import org.dromara.biz.domain.bo.EventTrackingCommunityBO;
import org.dromara.biz.domain.bo.EventTrackingEndBO;
import org.dromara.biz.domain.query.EventTrackingBaseQuery;
import org.dromara.biz.domain.query.ModuleStatsQuery;
import org.dromara.biz.domain.query.UserVisitTotalStatsQuery;
import org.dromara.biz.domain.vo.*;
import org.dromara.biz.domain.vo.UserTotalStatsVO.ModuleStats;
import org.dromara.biz.mapper.EventTrackingCommunityMapper;
import org.dromara.biz.mapper.EventTrackingMapper;
import org.dromara.biz.service.*;
import org.dromara.common.core.enums.DynamicEnum;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_event_tracking(小程序事件追踪表)】的数据库操作Service实现
* @createDate 2024-04-12 10:07:51
*/
@Service
@AllArgsConstructor
public class EventTrackingServiceCommunityImpl extends ServiceImpl<EventTrackingCommunityMapper, EventTrackingCommunity>
    implements EventTrackingCommunityService {

    private final WechatUserService wechatUserService;
    private final ServiceProcedureStepsService serviceProcedureStepsService;
    private final TaskNodeService taskNodeService;

    @Override
    public Long start(EventTrackingCommunityBO eventTrackingBO) {
        Long userId = LoginHelper.getUserId();
        String sessionId = StpUtil.getSession().getId();
        EventTrackingCommunity eventTracking = MapstructUtils.convert(eventTrackingBO, EventTrackingCommunity.class);
        eventTracking.setSessionId(sessionId);
        eventTracking.setUserId(userId);
        if (StrUtil.isNotBlank(eventTrackingBO.getAdditionalData())){
            eventTracking.setAdditionalData(eventTrackingBO.getAdditionalData());
            ServiceProcedureSteps procedureSteps = serviceProcedureStepsService.getByPostId(Long.valueOf(eventTrackingBO.getAdditionalData()));
            if (null != procedureSteps){
                TaskNode taskNde = taskNodeService.getById(procedureSteps.getTaskNodeId());
                if (taskNde!=null){
                    for (DynamicEnum dynamicEnum : DynamicEnum.values()) {
                        if (taskNde.getNurseType().equals(dynamicEnum.name())){
                            eventTracking.setModule(dynamicEnum.getDescription());
                            eventTracking.setPageTitle("动态详情");
                        }
                    }
                }
            }
        }
        baseMapper.insert(eventTracking);
        return eventTracking.getEventId();
    }

    @Override
    public Boolean end(EventTrackingEndBO eventId) {
        LambdaUpdateWrapper<EventTrackingCommunity> luw = Wrappers.lambdaUpdate();
        luw.eq(EventTrackingCommunity::getEventId, eventId.getEventId());
        luw.set(EventTrackingCommunity::getLeaveTime, eventId.getLeaveTime());
        return baseMapper.update(luw) > 0;
    }

    @Override
    public Boolean report(EventTrackingCommunityBO eventTrackingBO) {
        Long userId = LoginHelper.getUserId();
        String sessionId = StpUtil.getSession().getId();
        EventTrackingCommunity eventTracking = MapstructUtils.convert(eventTrackingBO, EventTrackingCommunity.class);
        eventTracking.setSessionId(sessionId);
        eventTracking.setUserId(userId);
        eventTracking.setAdditionalData(eventTrackingBO.getAdditionalData());
        return save(eventTracking);
    }


    /**
     * 转换为百分比字符串
     * @param rate 百分比数字
     * @return
     */
    private String convertToPercentageString(BigDecimal rate) {
        return rate.abs().setScale(2, RoundingMode.HALF_UP) + "%";
    }

    /**
     * 获取较昨日比较百分比更改类型
     * @param rate 百分比数据
     * @return 更改类型
     */
    private Integer getChangeType(BigDecimal rate){
        if (rate.compareTo(BigDecimal.ZERO) > 0) {
            return 0;
        }else if (rate.compareTo(BigDecimal.ZERO) < 0) {
            return 1;
        }else {
            return 2;
        }
    }

    /**
     * 秒数转字符串
     * @param seconds 秒数
     * @return 字符串
     */
    private String convertSeconds(Long seconds) {
        return DateUtils.formatSeconds(seconds);
    }
}




