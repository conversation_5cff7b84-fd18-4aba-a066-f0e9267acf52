package org.dromara.biz.domain.vo.nurse;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.biz.domain.BizBabyCare;
import org.dromara.common.mybatis.handler.type.StringListTypeHandler;

import java.util.Date;
import java.util.List;

@Data
@AutoMapper(target = BizBabyCare.class)
public class BabyCareVO {
    /**
     * 护理ID
     */
    private Long id;

    /**
     * 宝宝ID
     */
    private Long babyId;

    /**
     * 体温
     */
    private String temperature;

    /**
     * 黄疸值
     */
    private String jaundiceValue;

    /**
     * 身高
     */
    private String height;

    /**
     * 体重
     */
    private String weight;

    /**
     * 头部情况
     */
    private String head;

    /**
     * 囟门情况
     */
    private String fontanelle;

    /**
     * 眼部情况
     */
    private String eyes;

    /**
     * 鼻部情况
     */
    private String nose;

    /**
     * 口腔情况
     */
    private String mouth;

    /**
     * 面部情况
     */
    private String face;

    /**
     * 颈部情况
     */
    private String neck;

    /**
     * 腹部情况
     */
    private String abdomen;

    /**
     * 脐部情况
     */
    private String umbilicus;

    /**
     * 生殖器情况
     */
    private String genitals;

    /**
     * 四肢情况
     */
    private String limbs;

    /**
     * 肌张力
     */
    private String muscleTone;

    /**
     * 红疹部位
     */
    private String rashLocation;

    /**
     * 血管瘤部位
     */
    private String hemangiomaLocation;

    /**
     * 蒙古斑部位
     */
    private String mongolianSpotLocation;

    /**
     * 备注
     */
    private String notes;

    /**
     * 图片信息
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> imageInfo;

    /**
     * 入住时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    private Date createTime;

    /**
     * 宝宝姓名
     */
    private String babyName;

    /**
     * 操作人
     */
    private String authorName;
}
