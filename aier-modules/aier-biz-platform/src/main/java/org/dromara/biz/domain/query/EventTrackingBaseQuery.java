package org.dromara.biz.domain.query;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 事件追踪统计查询base
 */
@Data
public class EventTrackingBaseQuery {

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 查询类型 日=day; 周=week; 月=month; 自定义=custom; 所有=all; tips:当查询类型为custom时 开始时间和结束时间为必填
     */
    @NotBlank(message = "查询类型不能为空")
    private String queryType;

    /**
     * 构造查询条件
     */
    @JsonIgnore
    public void buildQuery(){
        String queryType = this.queryType;
        Date now = DateUtil.date();
        if("day".equals(queryType)){
            this.startDate = DateUtil.beginOfDay(now);
            this.endDate = DateUtil.endOfDay(now);
        }else if("week".equals(queryType)){
            Date weekStart = DateUtil.offsetDay(now, -7);
            this.startDate = DateUtil.beginOfDay(weekStart);
            this.endDate = DateUtil.endOfDay(now);
        }else if("month".equals(queryType)){
            Date monthStart = DateUtil.offsetDay(now, -30);
            this.startDate = DateUtil.beginOfDay(monthStart);
            this.endDate = DateUtil.endOfDay(now);
        }else if("custom".equals(queryType)){
            this.startDate = DateUtil.beginOfDay(this.startDate);
            this.endDate = DateUtil.endOfDay(this.endDate);
        }else if("all".equals(queryType)){
            this.startDate = null;
            this.endDate = null;
        }
    }

    /**
     * 是否为一天
     * @return 结果
     */
    @JsonIgnore
    public Boolean isDay(){
        long l = DateUtil.betweenWeek(this.startDate, this.endDate, true);
        return l == 1 || "day".equals(this.queryType);
    }
}
