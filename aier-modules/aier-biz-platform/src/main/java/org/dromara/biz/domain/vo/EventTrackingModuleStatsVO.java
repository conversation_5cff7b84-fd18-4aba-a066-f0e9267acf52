package org.dromara.biz.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;

@Data
public class EventTrackingModuleStatsVO {

    /**
     * 模块标识
     */
    private String module;
    /**
     * 模块名称
     */
    @Translation(type = TransConstant.DICT_TYPE_TO_LABEL, mapper = "module", other = "tracking_module_type")
    private String moduleName;

    /**
     * 模块访问时间 单位：分
     */
    private Long viewTime;

    /**
     * 访问时间较前一日 类型 0新增 1下降 2无变化
     */
    private Integer viewTimeDayBeforeType;

    /**
     * 访问时间较前一日比例
     */
    private String viewTimeRate;

    /**
     * 模块访问时间字符串
     */
    private String viewTimeStr;

    /**
     * 模块访问次数
     */
    private Integer viewNum;
    /**
     * 访问人数
     */
    private Integer userNum;

    /**
     * 分享次数
     */
    private Integer shareNum;

    /**
     * 分享人数
     */
    private Integer shareUserNum;

    /**
     * 分钟转字符串
     */
    @JsonIgnore
    public void viewTimeToStr() {
        Long hours = this.viewTime / 60;
        Long minutes = this.viewTime % 60;
        Long seconds = minutes * 60;
        this.viewTimeStr =  hours + "小时 " + minutes + "分钟 " + seconds + "秒";
    }
}
