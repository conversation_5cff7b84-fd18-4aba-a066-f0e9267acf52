package org.dromara.biz.service;

import org.dromara.biz.domain.PackageNursing;
import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.vo.PackageNursingVO;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【biz_package_nursing(套餐护理服务表)】的数据库操作Service
* @createDate 2024-03-12 14:27:07
*/
public interface PackageNursingService extends IService<PackageNursing> {

    List<PackageNursingVO> queryList(Long packageId);

    Map<Long, List<PackageNursingVO>> getMapByPackageIds(Set<Long> packageIds);
}
