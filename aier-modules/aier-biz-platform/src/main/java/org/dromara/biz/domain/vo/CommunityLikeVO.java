package org.dromara.biz.domain.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.dromara.biz.domain.CommunityLikes;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 社区动态点赞表
 */
@Data
@AutoMapper(target = CommunityLikes.class)
public class CommunityLikeVO implements Serializable {
    /**
     *
     */
    @TableId
    private Long likeId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 动态id
     */
    @NotNull(message = "社区动态id不能为空")
    private Long postId;

    private Date createTime;

    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}
