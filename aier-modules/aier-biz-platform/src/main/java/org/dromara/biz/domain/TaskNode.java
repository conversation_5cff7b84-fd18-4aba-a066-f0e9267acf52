package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.io.Serializable;

/**
 * 服务节点表
 * @TableName biz_task_node
 */
@TableName(value ="biz_task_node")
@Data
@EqualsAndHashCode(callSuper = true)
public class TaskNode extends TenantEntity implements Serializable {
    /**
     *
     */
    @TableId
    private Long taskNodeId;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 是否启用
     */
    private Boolean enable;

    /**
     * 是否精选
     */
    private Boolean isFeatured;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 节点类型
     */
    private String nurseType;

    /**
     * 节点角色
     */
    private String nurseRole;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
