package org.dromara.biz.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.biz.domain.RoomCheckouts;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 房间入住记录vo
 */
@Data
@AutoMapper(target = RoomCheckouts.class)
public class RoomCheckoutVO implements Serializable {

    /**
     * 主键id
     */
    private Long checkoutId;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 手机号
     */
    private String tel;

    /**
     * 服务人员名称列表
     */
    private List<String> staffList;

    /**
     * 服务人员名称
     */
    private String staffName;

    /**
     * 房间号
     */
    private String roomNumber;

    /**
     * 套房名称
     */
    private String suiteName;

    /**
     * 入住时间
     */
    private Date checkinDate;

    /**
     * 退房日期
     */
    private Date checkoutDate;

    @Serial
    private static final long serialVersionUID = 1L;
}
