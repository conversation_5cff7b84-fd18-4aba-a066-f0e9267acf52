package org.dromara.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.dromara.biz.domain.BizCommunityCommentLikes;
import org.dromara.biz.domain.CommunityComment;
import org.dromara.biz.domain.CommunityCommentLikes;
import org.dromara.biz.domain.MessageNotification;
import org.dromara.biz.mapper.BizCommunityCommentLikesMapper;
import org.dromara.biz.service.IBizCommunityCommentLikesService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.common.core.enums.MessageSourceEnum;
import org.dromara.common.core.enums.NotificationTypeEnum;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 社区动态评论点赞表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-01-13
 */
@Service
public class BizCommunityCommentLikesServiceImpl extends ServiceImpl<BizCommunityCommentLikesMapper, BizCommunityCommentLikes> implements IBizCommunityCommentLikesService {

    @Override
    public Boolean commentLike(Long commentId) {

//        Long userId = LoginHelper.getUserId();
//        LambdaQueryWrapper<CommunityCommentLikes> commentLikeLqw = Wrappers.lambdaQuery();
//        commentLikeLqw.eq(CommunityCommentLikes::getUserId, userId);
//        commentLikeLqw.eq(CommunityCommentLikes::getCommentId, commentId);
//        long count = communityCommentLikesService.count(commentLikeLqw);
//        LambdaUpdateWrapper<CommunityComment> commentLuw = Wrappers.lambdaUpdate();
//        if(count > 0){
//            communityCommentLikesService.remove(commentLikeLqw);
//            commentLuw.setSql("like_num = like_num - 1");
//        }else {
//            //点赞记录
//            CommunityCommentLikes communityCommentLikes = new CommunityCommentLikes();
//            communityCommentLikes.setCommentId(commentId);
//            communityCommentLikes.setUserId(userId);
//            communityCommentLikesService.save(communityCommentLikes);
//            commentLuw.setSql("like_num = like_num + 1");
//            //消息通知
//            CommunityComment comment = communityCommentService.getById(commentId);
//            MessageNotification messageNotification = new MessageNotification();
//            messageNotification.setType(NotificationTypeEnum.LIKE_COMMENT.getCode());
//            messageNotification.setSenderId(userId);
//            messageNotification.setReceiverId(comment.getUserId());
//            messageNotification.setPostId(comment.getPostId());
//            messageNotification.setCommentId(commentId);
//            messageNotification.setMessage("赞了你的评论");
//            messageNotification.setStatus(false);
//            messageNotification.setSource(MessageSourceEnum.COMMUNITY.getCode());
//            messageNotificationService.save(messageNotification);
//        }
//        commentLuw.eq(CommunityComment::getCommentId, commentId);
//        communityCommentService.update(commentLuw);
        return true;
    }
}
