package org.dromara.biz.controller.pc;

import lombok.RequiredArgsConstructor;
import org.dromara.biz.domain.RoomTodos;
import org.dromara.biz.domain.bo.RoomFeedbackExtendsBO;
import org.dromara.biz.domain.query.RoomFeedbackQuery;
import org.dromara.biz.domain.vo.RoomFeedbackVO;
import org.dromara.biz.service.RoomFeedbacksService;
import org.dromara.biz.service.RoomTodosService;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 房间反馈相关接口
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/room_feedback")
public class RoomFeedbackController {

    private final RoomFeedbacksService roomFeedbacksService;
    private final RoomTodosService roomTodosService;

    /**
     * 查询房间反馈列表
     */
    @GetMapping("/page")
    public TableDataInfo<RoomFeedbackVO> page(RoomFeedbackQuery query) {
        return roomFeedbacksService.queryPage(query);
    }

    /**
     * 查询房间反馈回复列表
     */
    @GetMapping("/reply_list/{feedbackId}")
    public R<List<RoomTodos>> queryReplyList(@PathVariable Long feedbackId) {
        return R.ok(roomTodosService.queryReplyList(feedbackId));
    }

    /**
     * 新增房间反馈信息
     * @param feedbackExtendsBO 反馈信息
     * @return 新增结果
     */
    @PostMapping("/create")
    @RepeatSubmit
    public R<Boolean> createFeedback(@RequestBody @Validated RoomFeedbackExtendsBO feedbackExtendsBO) {
        return R.ok(roomFeedbacksService.saveFeedback(feedbackExtendsBO));
    }
}
