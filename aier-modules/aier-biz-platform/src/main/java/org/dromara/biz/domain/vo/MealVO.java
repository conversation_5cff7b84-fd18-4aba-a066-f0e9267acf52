package org.dromara.biz.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import lombok.Getter;
import lombok.Setter;
import org.dromara.biz.domain.Meal;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 月子餐基本信息
 */
@Getter
@Setter
@AutoMapper(target = Meal.class)
public class MealVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 月子餐id
     */
    private Long mealId;

    /**
     * 月子餐简介
     */
//    @NotBlank(message = "月子餐简介不能为空")
    private String mealDescription;

    /**
     * 资历证书
     */
//    @NotEmpty(message = "资历证书不能为空")
    private List<String> certificateUrl;

    /**
     * 菜品信息列表
     */
//    @NotEmpty(message = "菜品信息不能为空")
//    @Valid
    private List<MealItemVO> mealItems;

    /**
     * 月子餐图片列表
     */
//    @NotEmpty(message = "月子餐图片列表不能为空")
    private List<String> mealPhotos;

    /**
     * 视频链接
     */
    private List<String> videos;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 膳食科普图片
     */
    private List<String> mealTags;

    /**
     * 是否展示膳食科普图片
     */
    private Boolean isTags;

    /**
     * 咨询次数
     */
    private Integer consultNumber;
}
