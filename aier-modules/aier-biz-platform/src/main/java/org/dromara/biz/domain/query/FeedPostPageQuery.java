package org.dromara.biz.domain.query;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.page.PageQuery;

@Data
@EqualsAndHashCode(callSuper = true)
public class FeedPostPageQuery extends PageQuery {

    /**
     * 排除的类型
     */
    private String excludeType;

    /**
     * 动态类型
     */
    private String postType;

    /**
     * 作者id
     */
    private Long authorId;

    /**
     * 是否有附件
     */
    private Boolean isAttachments;

    /**
     * 是否精选动态
     */
    private Boolean isFeatured;

    /**
     * 节点id
     */
    private Long taskNodeId;

}
