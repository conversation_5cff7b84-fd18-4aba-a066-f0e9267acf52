package org.dromara.biz.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.dromara.biz.domain.PregnancyTracker;
import org.dromara.biz.domain.query.PregnancyTrackerQuery;
import org.dromara.biz.domain.vo.GestationWeekVO;
import org.dromara.biz.domain.vo.PregnancyTrackerVO;
import org.dromara.biz.domain.vo.WechatUserVO;
import org.dromara.biz.mapper.PregnancyTrackerMapper;
import org.dromara.biz.service.PregnancyTrackerService;
import org.dromara.biz.service.WechatUserService;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【biz_pregnancy_tracker(孕期追踪表)】的数据库操作Service实现
* @createDate 2024-04-09 16:09:17
*/
@Service
@AllArgsConstructor
public class PregnancyTrackerServiceImpl extends ServiceImpl<PregnancyTrackerMapper, PregnancyTracker>
    implements PregnancyTrackerService{

    private final WechatUserService wechatUserService;

    @Override
    public TableDataInfo<PregnancyTrackerVO> queryPage(PregnancyTrackerQuery query) {
        IPage<PregnancyTrackerVO> page = baseMapper.selectVoPage(query.build(), this.buildQueryWrapper(query));
        return TableDataInfo.build(page);
    }

    private LambdaQueryWrapper<PregnancyTracker> buildQueryWrapper(PregnancyTrackerQuery query) {
        LambdaQueryWrapper<PregnancyTracker> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(PregnancyTracker::getCreateTime);
        return lqw;
    }

    @Override
    public PregnancyTrackerVO getInfoById(Long trackerId) {
        return baseMapper.selectVoById(trackerId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean save(PregnancyTrackerVO pregnancyTrackerVO) {
        PregnancyTracker pregnancyTracker = MapstructUtils.convert(pregnancyTrackerVO, PregnancyTracker.class);
        PregnancyTrackerVO exist = this.queryByGestationWeeks(pregnancyTrackerVO.getGestationWeek());
        if(ObjectUtil.isNotNull(exist)){
            throw new ServiceException("当前周期已经存在, 无法新增！");
        }
        return baseMapper.insert(pregnancyTracker) > 0;
    }

    @Override
    public Boolean update(PregnancyTrackerVO pregnancyTrackerVO) {
        pregnancyTrackerVO.setGestationWeek(null);
        PregnancyTracker pregnancyTracker = MapstructUtils.convert(pregnancyTrackerVO, PregnancyTracker.class);
        return baseMapper.updateById(pregnancyTracker) > 0;
    }

    @Override
    public GestationWeekVO queryGestationWeeks() {
        Long userId = LoginHelper.getUserId();
        WechatUserVO user = wechatUserService.getByUserId(userId);
        GestationWeekVO gestationWeekVO = new GestationWeekVO();
        Date now = DateUtil.date();
        Date dueDate = user.getDueDate();
        LambdaQueryWrapper<PregnancyTracker> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(PregnancyTracker::getGestationWeek);

        if(ObjectUtil.isNull(dueDate) || now.after(dueDate)){
            gestationWeekVO.setDays(-1L);
            gestationWeekVO.setDueData(dueDate);
            gestationWeekVO.setGestationWeek(null);
            return gestationWeekVO;
        }
        long daysUntilDue = DateUtil.betweenDay(now, dueDate, true);//距离预产期还有多少天
        gestationWeekVO.setDays(daysUntilDue);

        Date startDate = DateUtil.offsetWeek(dueDate, -42); //42周前的日期
        long weekTotal = DateUtil.betweenWeek(dueDate, startDate, true);//总周数

        //计算当前时间处于预产期之前的第几周
        Date startWeek = startDate;
        Date endWeek = DateUtil.offsetWeek(startDate, 1);
        for (int i = 0; i < weekTotal; i++) {
            if(now.before(endWeek) && now.after(startWeek)){
                gestationWeekVO.setGestationWeek(i+1);//当前时间处于预产期之前的第几周
                break;
            }
            startWeek = endWeek;
            endWeek = DateUtil.offsetWeek(endWeek, 1);
        }
        gestationWeekVO.setDueData(dueDate);
        return gestationWeekVO;
    }

    @Override
    public PregnancyTrackerVO queryByGestationWeeks(Integer gestationWeek) {
        LambdaQueryWrapper<PregnancyTracker> lqw = Wrappers.lambdaQuery();
        lqw.eq(PregnancyTracker::getGestationWeek, gestationWeek);
        lqw.last("limit 1");
        return baseMapper.selectVoOne(lqw);
    }
}




