package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.handler.type.StringListTypeHandler;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 产后康复项目主表
 * @TableName biz_postpartum_recovery_projects
 */
@TableName(value ="biz_postpartum_recovery_projects", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class PostpartumRecoveryProjects extends TenantEntity implements Serializable {

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 服务方式
     */
    private String serviceMode;

    /**
     * 服务分类
     */
    private String serviceCategory;

    /**
     * 服务次数
     */
    private Integer serviceCount;

    /**
     * 单次时常
     */
    private Integer singleDuration;

    /**
     * 服务功效
     */
    private String serviceEffect;

    /**
     * 是否上架
     */
    private Boolean isOnShelf;

    /**
     * 产后康复项目照片
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> displayPhotos;

    /**
     * 视频链接
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> videos;

    /**
     * 产后康复图文详情照片
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> descriptionPhotos;

    /**
     * 背景照片
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> backgroundPhotos = new ArrayList<>();;


    /**
     * 产后康复标签
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> tag;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 描述
     */
    private String description;

    /**
     * 服务项目
     */
    private String serviceItem;

    /**
     * 签约礼品id
     */
    private Long contractGiftId;

    /**
     * 咨询次数
     */
    private Integer consultNumber;
}
