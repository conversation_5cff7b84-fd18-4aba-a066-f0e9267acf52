package org.dromara.biz.service;

import org.dromara.biz.domain.WechatUserRole;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【biz_wechat_user_role(微信用户角色绑定表)】的数据库操作Service
* @createDate 2024-10-28 16:01:02
*/
public interface WechatUserRoleService extends IService<WechatUserRole> {

    /**
     * 根据用户id创建角色
     */
    void createRole(Long userId, String roleCode);
}
