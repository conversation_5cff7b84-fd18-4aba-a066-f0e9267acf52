package org.dromara.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.CommunityPosts;
import org.dromara.biz.domain.bo.CommunityCommentBO;
import org.dromara.biz.domain.bo.CommunityPostBO;
import org.dromara.biz.domain.query.CommunityMessageQuery;
import org.dromara.biz.domain.query.CommunityPostQuery;
import org.dromara.biz.domain.vo.*;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;

/**
* 这个接口为CommunityPosts实体提供服务层方法。
* 它扩展了MyBatis Plus的IService接口，该接口为实体提供了CRUD方法。
*
* <AUTHOR>
* @description 用于操作biz_community_posts表（社区帖子）的服务
* @createDate 2024-03-25 15:43:35
*/
public interface CommunityPostsService extends IService<CommunityPosts> {

    /**
    * 保存一个新的社区帖子。
    * @param communityPostBO 要保存的社区帖子。
    * @return 表示操作成功的布尔值。
    */
    Boolean save(CommunityPostBO communityPostBO);

    /**
    * 对社区帖子添加评论。
    * @param commentBO 要添加的评论。
    * @return 表示操作成功的布尔值。
    */
    Boolean comment(CommunityCommentBO commentBO);

    /**
    * 点赞社区帖子。
    * @param postId 宝妈社区动态id
    * @return 表示操作成功的布尔值。
    */
    Boolean like(Long postId);

    /**
    * 使用分页查询社区帖子。
    * @param query 搜索的查询参数。
    * @return 包含查询结果的TableDataInfo对象。
    */
    TableDataInfo<CommunityPostVO> queryPage(CommunityPostQuery query);

    /**
    * 获取特定帖子的信息。
    * @param postId 帖子的ID。
    * @return 包含帖子信息的CommunityPostVO对象。
    */
    CommunityPostVO getInfo(Long postId);

    /**
    * 对社区帖子的评论点赞。
    * @param commentId 要点赞的评论的ID。
    * @return 表示操作成功的布尔值。
    */
    Boolean commentLike(Long commentId);

    /**
     * 将帖子添加到用户的收藏中。
     * @param postId 社区动态id。
     * @return 表示操作成功的布尔值。
     */
    Boolean communityBookmark(Long postId);

    /**
     * 删除动态及所有动态评论
     * @param postIds 动态ids
     * @return 表示操作成功的布尔值。
     */
    Boolean remove(Long[] postIds);

    List<CommunityPostVO> queryList(CommunityPostQuery query);

    /**
     * 查询我收藏的社区动态列表
     * @param query 查询参数
     * @return 社区动态列表
     */
    TableDataInfo<CommunityPostVO> queryBookmark(CommunityPostQuery query);

    /**
     * 查询宝妈社区我的消息评论通知
     * @param query 查询参数
     * @return 消息通知列表
     */
    List<CommunityPostMessageVO> queryCommunityMessageList(CommunityMessageQuery query);

    /**
     * 查询宝妈社区动态我的消息点赞通知
     * @param query 查询参数
     * @return 结果
     */
    List<CommunityLikeMessageVO> queryCommunityLikeMessageList(PageQuery query);

    List<MyCommentVO> myComment(Long userId);

    List<MyUpCommunityVO> myUpCommunity(Long userId);

    List<MyUpCommunityVO> myUpPost(Long userId);
}
