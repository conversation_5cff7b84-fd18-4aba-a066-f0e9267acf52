package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDate;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.dromara.common.mybatis.handler.type.StringListTypeHandler;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 宝宝信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("biz_baby_info")
public class BizBabyInfo extends TenantEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 宝宝ID（主键）
     */
    @TableId
    private Long babyId;

    /**
     * 客户ID（关联客户表）
     */
    private Long customerId;

    /**
     * 胎数（1-单胎，2-双胎等）
     */
    private String fetusNumber;

    /**
     * 宝宝姓名
     */
    private String babyName;

    /**
     * 性别
     */
    private String gender;

    /**
     * 分娩医院
     */
    private String deliveryHospital;

    /**
     * 出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthDate;

    /**
     * 出生体重（kg）
     */
    private String birthWeight;

    /**
     * 出生身高（cm）
     */
    private String birthHeight;

    /**
     * 头围（cm）
     */
    private String headCircumference;

    /**
     * 出生方式
     */
    private String deliveryMethod;

    /**
     * 接种疫苗记录
     */
    private String vaccinationRecords;

    /**
     * 备注信息
     */
    private String remarks;

    /**
     * 照片存储路径
     */

    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> photoPath;
}
