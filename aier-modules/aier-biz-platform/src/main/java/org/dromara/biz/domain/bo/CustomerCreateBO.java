package org.dromara.biz.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.dromara.biz.domain.Customers;
import org.dromara.common.web.annotation.MobilePhone;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 添加客户bo
 */
@Data
@AutoMapper(target = Customers.class, reverseConvertGenerate = false)
public class CustomerCreateBO {

    /**
     * 客户姓名
     */
    @NotBlank(message = "客户姓名不能为空")
    private String name;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 性别
     */
    @Length(max = 10, message = "客户性别超出最大长度10")
    private String gender;

    /**
     * 妈妈生日
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date momBirthday;

    /**
     * 联系方式
     */
//    @NotBlank(message = "联系方式不能为空")
//    @MobilePhone(message = "联系方式格式错误")
    private String tel;

    /**
     * 来源
     */
    @Length(max = 10, message = "客户来源超出最大长度10")
    private String source;

    /**
     * 预产期 yyyy-MM-dd
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date dueDate;

    /**
     * 客户意向度、客户标签、跟进记录
     */
    @Valid
    private CustomerOperateBO customerOperateBO;

    /**
     * 地址
     */
    private String address;

    /**
     * 跟进人id 管理者新建可以选择线索的负责人其他人默认自己
     */
    private Long followerId;

    /**
     * 是否有效客户
     */
    private Boolean isValid = true;

    /**
     * 微信号
     */
    private String wechat;

    /**
     * 渠道id
     */
    private Long channelId;

    /**
     * 是否到店
     */
    private Boolean isArrivedAtStore;

    /**
     * 是否到店
     */
    private Boolean isToStore;


    /**
     * 到店次数
     */
    private String arrivedAtStoreNumber;

    /**
     * 身份证
     */
    private String idCard;
}
