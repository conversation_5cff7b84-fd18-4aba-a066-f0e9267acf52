package org.dromara.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.biz.domain.CommunityBookmarks;
import org.dromara.biz.mapper.CommunityBookmarksMapper;
import org.dromara.biz.service.CommunityBookmarksService;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【biz_community_collections(社区动态收藏表)】的数据库操作Service实现
* @createDate 2024-03-25 15:43:35
*/
@Service
public class CommunityBookmarksServiceImpl extends ServiceImpl<CommunityBookmarksMapper, CommunityBookmarks>
    implements CommunityBookmarksService {

    @Override
    public Boolean isCollectionByUserId(Long userId, Long postId) {
        LambdaQueryWrapper<CommunityBookmarks> lqw = Wrappers.lambdaQuery();
        lqw.eq(CommunityBookmarks::getUserId, userId);
        lqw.eq(CommunityBookmarks::getPostId, postId);
        return baseMapper.selectCount(lqw) > 0;
    }

    @Override
    public Map<Long, CommunityBookmarks> mapByUserId(Long userId) {
        return baseMapper
            .selectList(Wrappers.<CommunityBookmarks>lambdaQuery()
                .eq(CommunityBookmarks::getUserId, userId))
            .stream()
            .collect(Collectors.toMap(CommunityBookmarks::getPostId, Function.identity(), (o, n) -> o));
    }
}




