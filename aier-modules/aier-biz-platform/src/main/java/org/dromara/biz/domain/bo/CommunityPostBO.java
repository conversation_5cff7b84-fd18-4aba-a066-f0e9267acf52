package org.dromara.biz.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.dromara.biz.domain.CommunityPosts;

import java.util.ArrayList;
import java.util.List;

/**
 * 新增宝妈社区动态业务对象
 */
@Data
@AutoMapper(target = CommunityPosts.class, reverseConvertGenerate = false)
public class CommunityPostBO {

    /**
     * 话题id
     */
    @NotNull(message = "话题id不能为空")
    private Long topicId;

    /**
     * 动态内容
     */
    @NotBlank(message = "话题内容不能为空")
    private String content;

    /**
     * 视频链接
     */
    private List<String> videos = new ArrayList<>();

    /**
     * 动态图片
     */
    private List<String> imageUrl = new ArrayList<>();
}
