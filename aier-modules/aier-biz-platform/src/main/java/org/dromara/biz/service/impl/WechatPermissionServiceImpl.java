package org.dromara.biz.service.impl;

import lombok.RequiredArgsConstructor;
import org.dromara.biz.service.WechatPermissionService;
import org.dromara.biz.service.WechatRoleService;
import org.springframework.stereotype.Service;

import java.util.Set;

/**
 * 微笑小程序用户权限处理
 */
@RequiredArgsConstructor
@Service
public class WechatPermissionServiceImpl implements WechatPermissionService {

    private final WechatRoleService wechatRoleService;

    @Override
    public Set<String> getRolePermission(Long userId) {
        return wechatRoleService.selectRolePermissionByUserId(userId);
    }

    @Override
    public Set<String> getMenuPermission(Long userId) {
        return wechatRoleService.selectPermsByUserId(userId);
    }
}
