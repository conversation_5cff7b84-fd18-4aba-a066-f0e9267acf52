package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.io.Serializable;

/**
 * 用户消息通知表
 * @TableName biz_message_notification
 */
@TableName(value ="biz_message_notification")
@Data
@EqualsAndHashCode(callSuper = true)
public class MessageNotification extends TenantEntity implements Serializable {

    /**
     *
     */
    @TableId
    private Long notificationId;

    /**
     * 通知类型（1=点赞、2=评论、3=回复）
     */
    private String type;

    /**
     * 发送者id
     */
    private Long senderId;

    /**
     * 接收者id
     */
    private Long receiverId;

    /**
     * 具体的内容id
     */
    private Long postId;

    /**
     * 评论id
     */
    private Long commentId;

    /**
     * 消息
     */
    private String message;

    /**
     * 通知状态（未读/已读）
     */
    private Boolean status;

    /**
     * 消息来源（1=社区、2=朋友圈）
     */
    private String source;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
