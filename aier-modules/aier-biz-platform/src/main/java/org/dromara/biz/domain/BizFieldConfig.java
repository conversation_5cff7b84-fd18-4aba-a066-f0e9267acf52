package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.dromara.common.tenant.core.TenantEntity;

import java.time.LocalDateTime;
import java.io.Serializable;

/**
 * <p>
 * 字段配置表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("biz_field_config")
public class BizFieldConfig extends TenantEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 配置ID（主键）
     */
    @TableId
    private Integer configId;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 字段名（驼峰命名）
     */
    private String fieldName;

    /**
     * 字段显示名称
     */
    private String displayName;

    /**
     * 是否启用（1-启用，0-禁用）
     */
    private Boolean isEnabled;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    public BizFieldConfig() {
    }

    public BizFieldConfig( String tableName, String fieldName, String displayName, Boolean isEnabled) {
        this.tableName = tableName;
        this.fieldName = fieldName;
        this.displayName = displayName;
        this.isEnabled = isEnabled;
    }
}
