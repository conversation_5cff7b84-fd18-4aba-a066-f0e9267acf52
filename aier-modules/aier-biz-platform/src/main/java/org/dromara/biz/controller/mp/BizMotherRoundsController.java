package org.dromara.biz.controller.mp;


import cn.dev33.satoken.annotation.SaIgnore;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.BizMotherCare;
import org.dromara.biz.domain.BizMotherRounds;
import org.dromara.biz.domain.vo.CustomerVO;
import org.dromara.biz.domain.vo.nurse.MotherCareVO;
import org.dromara.biz.domain.vo.nurse.MotherRoundsVO;
import org.dromara.biz.service.IBizBabyInfoService;
import org.dromara.biz.service.IBizMotherCareService;
import org.dromara.biz.service.IBizMotherRoundsService;
import org.dromara.common.core.domain.R;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.web.bind.annotation.*;

import org.springframework.stereotype.Controller;

import java.util.List;


/**
 * 宝妈查房表 前端控制器
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping("/mp/mother/rounds")
public class BizMotherRoundsController {
    private final IBizMotherRoundsService motherRoundsService;
    private final IBizBabyInfoService babyInfoService;
    /**
     * 新增宝妈查房记录
     * @param motherRounds
     * @return
     */
    @PostMapping("/insert")
    public R<Boolean> insert(@RequestBody BizMotherRounds motherRounds) {

        return R.ok(motherRoundsService.save(motherRounds));
    }

    /**
     * 修改宝妈查房记录
     * @param motherRounds
     * @return
     */
    @PostMapping("/update")
    public R<Boolean> update(@RequestBody BizMotherRounds motherRounds) {
        return R.ok(motherRoundsService.updateById(motherRounds));
    }

    /**
     * 查房记录列表
     * @param customerId
     * @return
     */
    @GetMapping("/list")
    @SaIgnore
    public R<List<MotherRoundsVO>> list(@RequestParam("customerId") Long customerId){
        return R.ok(motherRoundsService.listVo(customerId));
    }

    /**
     * 我的查房记录列表
     * @return
     */
    @GetMapping("/myList")
    @SaIgnore
    public R<List<MotherRoundsVO>> myList(){
        Long userId = LoginHelper.getUserId();
        if (userId != null) {
            Long customerId = babyInfoService.getByLoginUser();
            if (customerId != null) {
                return  R.ok(motherRoundsService.listVo(customerId));
            }
        }
        return R.ok(null);
    }



    /**
     * 获取记录详情 （宝妈查房）
     * @param id
     * @return
     */
    @GetMapping("/getDetail")
    @SaIgnore
    public R<MotherRoundsVO> getDetail(@RequestParam("id") Long id){
        return R.ok(motherRoundsService.getDetail(id));
    }

    /**
     * 删除记录
     * @param id
     * @return
     */
    @GetMapping("/delete")
    public R<Boolean> delete(@RequestParam("id") Long id){
        return R.ok(motherRoundsService.removeById(id));
    }}
