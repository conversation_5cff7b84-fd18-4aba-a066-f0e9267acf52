package org.dromara.biz.domain.vo;

import lombok.Data;

import java.util.List;

/**
 * 客户服务过程步骤VO
 */
@Data
public class CustomerServiceStepVO {

    /**
     * 入住天数名称 第n天
     */
    private String dayName;

    /**
     * 开始日期 查询为周或月时使用
     */
    private String startDate;

    /**
     * 结束日期 查询为周或月时使用
     */
    private String endDate;

    /**
     * 查询组 day-天查询 week-周查询 month-月查询
     */
    private String group;

    /**
     * 服务过程步骤列表
     */
    private List<NodeStep> stepList;

    /**
     * 节点id
     */
    private Long taskNodeId;

    /**
     * 节点名称
     */
    private String nodeName;

    @Data
    public static class NodeStep {
        /**
         * 节点id
         */
        private Long taskNodeId;

        /**
         * 节点名称
         */
        private String nodeName;

        /**
         * 是否精选
         */
        private Boolean isFeatured;
    }
}
