package org.dromara.biz.controller.pc;

import cn.hutool.core.util.ObjectUtil;
import lombok.RequiredArgsConstructor;
import org.dromara.biz.domain.DefaultNodes;
import org.dromara.biz.domain.query.DefaultNodeQuery;
import org.dromara.biz.service.DefaultNodesService;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 默认节点controller
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/default_node")
public class DefaultNodeController {

    private final DefaultNodesService defaultNodesService;

    /**
     * 分页查询列表
     */
    @GetMapping("/page")
    public TableDataInfo<DefaultNodes> page(DefaultNodeQuery query) {
        return TableDataInfo.build(defaultNodesService.page(query.build()));
    }

    /**
     * 查询详情
     */
    @GetMapping("/detail/{id}")
    public R<DefaultNodes> detail(@PathVariable Long id) {
        return R.ok(defaultNodesService.getById(id));
    }

    /**
     * 新增默认节点
     * @param defaultNode 节点信息
     * @return 新增结果
     */
    @PostMapping("/add")
    @RepeatSubmit
    public R<Boolean> add(@RequestBody @Validated DefaultNodes defaultNode) {
        String shortOrder = defaultNode.getShortOrder();
        if(ObjectUtil.isNull(shortOrder)) {
            defaultNode.setShortOrder("0");
        }
        return R.ok(defaultNodesService.save(defaultNode));
    }

    /**
     * 修改默认节点
     * @param defaultNode 节点信息
     * @return 修改结果
     */
    @PutMapping("/update")
    @RepeatSubmit
    public R<Boolean> update(@RequestBody @Validated DefaultNodes defaultNode) {
        return R.ok(defaultNodesService.updateById(defaultNode));
    }

    /**
     * 删除默认节点
     * @param id 节点id
     * @return 结果
     */
    @DeleteMapping("/{id}")
    public R<Boolean> remove(@PathVariable Long id){
        return R.ok(defaultNodesService.removeById(id));
    }
}
