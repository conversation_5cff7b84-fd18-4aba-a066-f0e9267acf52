package org.dromara.biz.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import lombok.Getter;
import lombok.Setter;
import org.dromara.biz.domain.Supplies;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 用品vo
 */
@Getter
@Setter
@AutoMapper(target = Supplies.class)
public class SuppliesVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private Long supplyId;

    /**
     * 用品名称
     */
    @NotBlank(message = "用品名称不能为空")
    private String supplyName;

    /**
     * 用品品牌
     */
    private String brandName;

    /**
     * 用品类别 MAMA=妈妈用品 BABY=宝宝用品
     */
    private String category;

    /**
     *
     */
    private Date createTime;

    /**
     *
     */
    private Date updateTime;
}
