package org.dromara.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.InvitationTemplate;
import org.dromara.biz.domain.query.invitation.InvitationQuery;
import org.dromara.biz.domain.vo.invitation.InvitationTemplateVO;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
* <AUTHOR>
* @description 针对表【biz_invitation_template(请柬模版表)】的数据库操作Service
* @createDate 2024-07-30 11:25:40
*/
public interface InvitationTemplateService extends IService<InvitationTemplate> {

    /**
     * 分页查询请帖模版列表
     */
    TableDataInfo<InvitationTemplateVO> queryPage(InvitationQuery query);

    /**
     * 查询请帖模版详情
     */
    InvitationTemplateVO getTemplateDetail(Long id);

    /**
     * 新增请帖模版
     */
    void save(InvitationTemplateVO templateVO);

    /**
     * 修改请帖模版
     */
    void update(InvitationTemplateVO templateVO);

    /**
     * 更新请帖模版状态
     */
    void updateTemplateStatus(Long id, Boolean status);

    /**
     * 收藏请帖
     */
    void templateCollect(Long id);

    /**
     * 分页查询指定用户收藏的请帖列表
     */
    TableDataInfo<InvitationTemplateVO> getTemplateCollectPage(PageQuery pageQuery, Long userId);
}
