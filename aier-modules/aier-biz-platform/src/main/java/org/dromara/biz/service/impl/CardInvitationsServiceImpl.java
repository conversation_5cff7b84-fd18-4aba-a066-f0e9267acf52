package org.dromara.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.biz.domain.CardInvitations;
import org.dromara.biz.mapper.CardInvitationsMapper;
import org.dromara.biz.service.CardInvitationsService;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【biz_card_invitations(用户请帖邀请信息)】的数据库操作Service实现
* @createDate 2025-04-11 11:37:13
*/
@Service
public class CardInvitationsServiceImpl extends ServiceImpl<CardInvitationsMapper, CardInvitations>
    implements CardInvitationsService{

    @Override
    public Boolean saveInvitation(CardInvitations invitations) {
        return saveOrUpdate(invitations);
    }

    @Override
    public CardInvitations getInvitation(Long cardId) {
        return lambdaQuery().eq(CardInvitations::getCardId, cardId).one();
    }
}




