package org.dromara.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.biz.domain.PackageNursing;
import org.dromara.biz.domain.vo.PackageNursingVO;
import org.dromara.biz.mapper.PackageNursingMapper;
import org.dromara.biz.service.PackageNursingService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【biz_package_nursing(套餐护理服务表)】的数据库操作Service实现
* @createDate 2024-03-12 14:27:07
*/
@Service
public class PackageNursingServiceImpl extends ServiceImpl<PackageNursingMapper, PackageNursing>
    implements PackageNursingService{

    @Override
    public List<PackageNursingVO> queryList(Long packageId) {
        LambdaQueryWrapper<PackageNursing> lqw = Wrappers.lambdaQuery();
        lqw.eq(PackageNursing::getPackageId, packageId);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public Map<Long, List<PackageNursingVO>> getMapByPackageIds(Set<Long> packageIds) {
        if(CollectionUtils.isEmpty(packageIds)){
            packageIds.add(-1L);
        }
        LambdaQueryWrapper<PackageNursing> lqw = Wrappers.lambdaQuery();
        lqw.in(PackageNursing::getPackageId, packageIds);
        List<PackageNursingVO> packageNursingList = baseMapper.selectVoList(lqw);
        return packageNursingList.stream().collect(Collectors.groupingBy(PackageNursingVO::getPackageId));
    }
}




