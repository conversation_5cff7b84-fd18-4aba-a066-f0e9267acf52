package org.dromara.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.BizTemplateDynamic;
import org.dromara.biz.domain.query.TemplateQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;

/**
 * <p>
 * 动态模板表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-30
 */
public interface IBizTemplateDynamicService extends IService<BizTemplateDynamic> {
    TableDataInfo<BizTemplateDynamic> queryPage(TemplateQuery query);
    Boolean saveB(BizTemplateDynamic templateDynamic);
    List<BizTemplateDynamic> listByLabel(String label,String contentStyle);

    List<BizTemplateDynamic> getTemplateByNodeId(Long nodeId);
}
