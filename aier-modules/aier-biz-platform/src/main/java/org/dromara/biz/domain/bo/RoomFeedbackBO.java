package org.dromara.biz.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.dromara.biz.domain.RoomFeedbacks;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 房间客户评价bo
 */
@Data
@AutoMapper(target = RoomFeedbacks.class)
public class RoomFeedbackBO implements Serializable {

    /**
     * 服务人员id
     */
    @NotNull(message = "服务人员不能为空")
    private Long staffId;

    /**
     * 评价内容
     */
    @NotBlank(message = "评价内容不能为空")
    private String content;

    /**
     * 评价满意度 2=满意 3=一般 4=不满意
     */
    @NotBlank(message = "评价满意度不能为空")
    private String satisfaction;

    /**
     * 评价标签
     */
    private List<String> tags;

    @Serial
    private static final long serialVersionUID = 1L;
}
