package org.dromara.biz.controller.pc;

import cn.dev33.satoken.annotation.SaCheckPermission;
import lombok.RequiredArgsConstructor;
import org.dromara.biz.domain.WechatRole;
import org.dromara.biz.domain.WechatUser;
import org.dromara.biz.domain.bo.wechat.WechatUserBO;
import org.dromara.biz.domain.query.WechatUserQuery;
import org.dromara.biz.domain.vo.SelectOptionsVO;
import org.dromara.biz.domain.vo.WechatUserVO;
import org.dromara.biz.service.WechatRoleService;
import org.dromara.biz.service.WechatUserService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 微信用户相关接口
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/wechat_user")
public class WechatUserController {

    private final WechatUserService wechatUserService;
    private final WechatRoleService wechatRoleService;

    /**
     * 获取微信用户下拉选项
     */
    @GetMapping("/options")
    public R<List<SelectOptionsVO>> getSelectOptions() {
        List<WechatUser> wechatUsers = wechatUserService.list();
        List<SelectOptionsVO> result = wechatUsers.stream().map(user -> {
            SelectOptionsVO options = new SelectOptionsVO();
            if(StringUtils.isNotBlank(user.getTel())){
                options.setLabel(user.getNickname() + "(" + user.getTel() + ")");
            }else {
                options.setLabel(user.getNickname() + "(未绑定手机号)");
            }

            options.setValue(user.getUserId());
            return options;
        }).distinct().toList();
        return R.ok(result);
    }

    /**
     * 获取微信用户下拉选项
     */
    @GetMapping("/list")
    public R<List<WechatUser>> getWechatUserList() {
        List<WechatUser> list = wechatUserService.list();
        return R.ok(list);
    }

    /**
     * 查询微信用户角色列表
     */
    @GetMapping("/roles")
    public R<List<WechatRole>> getWechatRoles() {
        return R.ok(wechatRoleService.list());
    }

    /**
     * 新增微信小程序用户
     */
    @PostMapping("/save")
    @RepeatSubmit
    public R<Void> create(@RequestBody WechatUserBO wechatUser){
        wechatUserService.create(wechatUser);
        return R.ok();
    }

    /**
     * 删除微信小程序用户
     */
    @DeleteMapping("/remove/{userId}")
    @RepeatSubmit
    public R<Void> remove(@PathVariable Long userId){
        wechatUserService.remove(userId);
        return R.ok();
    }

    /**
     * 修改微信小程序用户
     * @param user 修改小程序用户信息
     * @return 修改结果
     */
    @PutMapping("/update")
    @RepeatSubmit
//    @SaCheckPermission("platform:wechat:user:update")
    public R<Boolean> update(@RequestBody WechatUserBO user){
        return R.ok(wechatUserService.update(user));
    }

    /**
     * 查询微信小程序用户详情
     * @param userId 微信小程序用户id
     * @return 微信小程序用户详情
     */
    @GetMapping("/info/{userId}")
    public R<WechatUserVO> getInfo(@PathVariable Long userId) {
        return R.ok(wechatUserService.getByUserId(userId));
    }

    /**
     * 分页查询微信小程序配置列表
     */
    @GetMapping("/page")
    @SaCheckPermission("platform:wechat:user:page")
    public TableDataInfo<WechatUserVO> queryPage(WechatUserQuery query) {
        return wechatUserService.queryPage(query);
    }

    /**
     * 更改微信用户账号状态
     * @param status 状态
     * @param userId 微信用户id
     * @return 状态更改结果
     */
    @PostMapping("/change/{status}/{userId}")
    @RepeatSubmit
//    @SaCheckRole(TenantConstants.SUPER_ADMIN_ROLE_KEY)
    public R<Boolean> change(@PathVariable String status, @PathVariable Long userId) {
        return R.ok(wechatUserService.statusChange(status, userId));
    }
}
