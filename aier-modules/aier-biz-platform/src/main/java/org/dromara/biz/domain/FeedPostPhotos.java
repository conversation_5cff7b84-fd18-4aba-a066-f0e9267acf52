package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.biz.domain.base.UgcEntity;

import java.io.Serial;
import java.io.Serializable;

/**
 * 朋友圈图片表
 * @TableName biz_feed_post_photos
 */
@TableName(value ="biz_feed_post_photos")
@Data
@EqualsAndHashCode(callSuper = true)
public class FeedPostPhotos extends UgcEntity implements Serializable {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     * 动态id
     */
    private Long postId;

    /**
     * 链接
     */
    private String url;

    /**
     * 内容审查消息id
     */
    private String traceId;

    /**
     *
     */
    private String tenantId;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
