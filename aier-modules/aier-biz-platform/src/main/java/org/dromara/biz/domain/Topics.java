package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.mybatis.handler.type.StringListTypeHandler;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 话题表
 * @TableName biz_topics
 */
@TableName(value ="biz_topics", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class Topics extends BaseEntity implements Serializable {
    /**
     *
     */
    @TableId
    private Long topicId;

    /**
     * 话题名称
     */
    private String topicName;

    /**
     * 话题照片
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> topicPhotos;

    /**
     * 话题开始时间
     */
    private Date topicStartTime;

    /**
     * 话题结束时间
     */
    private Date topicEndTime;

    /**
     * 上线开始时间
     */
    private Date onlineStartTime;

    /**
     * 上线结束时间
     */
    private Date onlineEndTime;

    /**
     * 在线状态
     */
    private Boolean onlineStatus;

    /**
     * 收藏数量
     */
    private Integer bookmarkNum;

    /**
     * 话题参与数量
     */
    private Integer postNum;

    /**
     * 话题描述&简介
     */
    private String topicDescription;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * 话题未开始状态
     */
    @TableField(exist = false)
    public static final String STATUS_NOT_STARTED = "0";
    /**
     * 话题进行中状态
     */
    @TableField(exist = false)
    public static final String STATUS_ONGOING = "1";
    /**
     * 话题结束状态
     */
    @TableField(exist = false)
    public static final String STATUS_END = "2";
}
