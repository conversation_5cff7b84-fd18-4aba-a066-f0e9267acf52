package org.dromara.biz.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.dromara.biz.domain.CommunityComment;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 社区动态评论vo
 */
@Data
@AutoMapper(target = CommunityComment.class)
public class CommunityCommentVO implements Serializable {

    /**
     *
     */
    private Long commentId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 动态id
     */
    @NotNull(message = "社区动态id不能为空")
    private Long postId;

    /**
     * 父级评论，如果是一级评论则为空
     */
    private Long parentCommentId;

    /**
     * 评论
     */
    @NotBlank(message = "社区动态评论内容不能为空")
    private String content;

    /**
     * 子级评论
     */
    private List<CommunityCommentVO> subCommentList;

    /**
     * 用户昵称
     */
    private String nickname;

    /**
     * 头像
     */
    private String avatar;

    /**
     * ip属地
     */
    private String ip;

    /**
     * 评论点赞数量
     */
    private Integer likeNum;

    /**
     * 评论回复数量
     */
    private Integer commentNum;

    /**
     * 当前用户对该条评论是否点赞
     */
    private Boolean isLike;

    private Date createTime;

    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}
