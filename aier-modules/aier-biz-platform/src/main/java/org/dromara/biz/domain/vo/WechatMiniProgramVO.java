package org.dromara.biz.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.dromara.biz.domain.WechatMiniProgram;
import org.dromara.common.core.validate.EditGroup;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 微信小程序配置
 */
@Data
@AutoMapper(target = WechatMiniProgram.class)
public class WechatMiniProgramVO implements Serializable {

    /**
     *
     */
    @NotNull(message = "miniProgramId不能为空", groups = {EditGroup.class})
    private Long miniProgramId;

    /**
     * 名称
     */
    private String name;

    /**
     * 类型
     */
    @NotBlank(message = "类型不能为空")
    private String type;

    /**
     * 微信小程序的appid
     */
    @NotBlank(message = "微信小程序的appid不能为空")
    private String appid;

    /**
     * 微信小程序的Secret
     */
    @NotBlank(message = "微信小程序的Secret不能为空")
    private String secret;

    /**
     * 微信小程序消息服务器配置的token
     */
    private String token;

    /**
     * 微信小程序消息服务器配置的EncodingAESKey
     */
    private String aesKey;

    /**
     * 是否使用
     */
    @NotNull(message = "是否使用不能为空")
    private Boolean issued;

    /**
     * 消息格式，XML或者JSON
     */
    private String msgDataFormat;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    @Serial
    private static final long serialVersionUID = 1L;
}
