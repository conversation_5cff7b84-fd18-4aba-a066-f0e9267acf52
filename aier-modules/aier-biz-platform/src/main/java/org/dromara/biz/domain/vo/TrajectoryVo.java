package org.dromara.biz.domain.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 轨迹分析
 */
@Data
public class TrajectoryVo implements Serializable {
    /**
     * 次数
     */
    private Integer number;
    /**
     * 标题
     */
    private String title;
    /**
     * 内容/描述
     */
    private String description;
    /**
     * 时长
     */
    private Long duration;
    /**
     * 浏览时长字符串
     */
    private String durationStr;
    /**
     * 浏览时长字符串-时分秒
     */
    private String durationStrMinute;
    /**
     * 时间
     */
    private Date lastTime;

    /**
     * 秒数转字符串
     * @return 字符串
     */
    @JsonIgnore
    public void convertSeconds() {
        Long hours = this.duration / 3600L;
        Long minutes = (this.duration % 3600) / 60L;
        Long remainingSeconds = this.duration % 60L;
        this.durationStr =  hours + "小时 " + minutes + "分钟 " + remainingSeconds + "秒";
    }

    public String getDurationStr() {
        if (duration == null) {
            return "";
        }
        Long hours = this.duration / 3600L;
        Long minutes = (this.duration % 3600) / 60L;
        Long remainingSeconds = this.duration % 60L;
        this.durationStr =  hours + "小时 " + minutes + "分钟 " + remainingSeconds + "秒";
        return this.durationStr;
    }
    public String getDurationStrMinute() {
        if (duration == null) {
            return "";
        }
        Long remainingSeconds = this.duration % 60L;
        Long minutes = (this.duration % 3600) / 60L;
        if (remainingSeconds == 0 || duration < 60L) {
            this.durationStrMinute =  remainingSeconds + "秒 ";
        }else if (minutes == 0 || minutes < 60L) {
//            Long minutes = this.duration / 60L;
            this.durationStrMinute = minutes + "分钟 " + remainingSeconds + "秒";
        }else {
            Long hours = this.duration / 3600L;
            this.durationStrMinute =  hours + "小时 " + minutes + "分钟 " + remainingSeconds + "秒";
        }

        return this.durationStrMinute;
    }
}
