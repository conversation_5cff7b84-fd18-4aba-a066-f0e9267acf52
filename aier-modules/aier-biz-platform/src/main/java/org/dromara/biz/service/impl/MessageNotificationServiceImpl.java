package org.dromara.biz.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.biz.domain.MessageNotification;
import org.dromara.biz.service.MessageNotificationService;
import org.dromara.biz.mapper.MessageNotificationMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【biz_message_notification(会所朋友圈动态通知表)】的数据库操作Service实现
* @createDate 2024-03-27 10:24:11
*/
@Service
public class MessageNotificationServiceImpl extends ServiceImpl<MessageNotificationMapper, MessageNotification>
    implements MessageNotificationService{

}




