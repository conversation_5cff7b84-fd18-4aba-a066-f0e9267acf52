package org.dromara.biz.controller.pc;


import cn.dev33.satoken.annotation.SaIgnore;
import lombok.RequiredArgsConstructor;
import org.dromara.biz.domain.BizSytleDynamic;
import org.dromara.biz.service.IBizStyleDynamicService;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 动态样式表 相关接口
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@Controller
@RequestMapping("/platform/styleDynamic")
public class StyleDynamicController {
    private final IBizStyleDynamicService bizStyleDynamicService;
    /**
     * 分页查询样式列表
     */
    @GetMapping("/page")
    public TableDataInfo<BizSytleDynamic> queryPage(PageQuery query) {
        return bizStyleDynamicService.queryPage(query);
    }

    /**
     * 新增修改动态样式
     * @param sytleDynamic 动态样式
     * @return 新增结果
     */
    @PostMapping("/save")
    @RepeatSubmit
    public R<Boolean> insert(@RequestBody @Validated BizSytleDynamic sytleDynamic) {
        return R.ok(bizStyleDynamicService.saveB(sytleDynamic));
    }

    /**
     * 删除动态样式
     * @param id id
     * @return 删除结果
     */
    @GetMapping("/remove")
    public R<Boolean> delete(@RequestParam("id") Long id){
        return R.ok(bizStyleDynamicService.removeById(id));
    }

    /**
     * 查询详情
     * @param id id
     * @return 结果
     */
    @GetMapping("/info")
    public R<BizSytleDynamic> infoMp(@RequestParam("id") Long id){
        return R.ok( bizStyleDynamicService.getById(id));
    }

    @GetMapping
    @SaIgnore
    public R<BizSytleDynamic> queryShowStyle(){
        return R.ok(bizStyleDynamicService.queryShowStyle());
    }

}
