package org.dromara.biz.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.poi.util.StringUtil;
import org.dromara.biz.domain.BizUserGuideRecord;
import org.dromara.biz.mapper.BizUserGuideRecordMapper;
import org.dromara.biz.service.IBizUserGuideRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 指引记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-03
 */
@Service
public class BizUserGuideRecordServiceImpl extends ServiceImpl<BizUserGuideRecordMapper, BizUserGuideRecord> implements IBizUserGuideRecordService {

    @Override
    public Boolean getRecord(String module) {
        Long userId = LoginHelper.getUserId();
        if (ObjectUtil.isNotNull(userId)  && StringUtils.isNotEmpty(module)) {
            LambdaQueryWrapper<BizUserGuideRecord> lqw = Wrappers.lambdaQuery();
            lqw.eq(BizUserGuideRecord::getUserId, userId);
            lqw.eq(BizUserGuideRecord::getModule, module);
            BizUserGuideRecord guideRecord = baseMapper.selectOne(lqw);
            if(ObjectUtil.isNull(guideRecord)){
                //没有找到记录  新增后返回
                guideRecord = new BizUserGuideRecord();
                guideRecord.setUserId(userId);
                guideRecord.setModule(module);
                baseMapper.insert(guideRecord);
                return true;
            }
        }

        return false;
    }
}
