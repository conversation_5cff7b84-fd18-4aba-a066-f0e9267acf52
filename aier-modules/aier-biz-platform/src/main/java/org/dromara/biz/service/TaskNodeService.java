package org.dromara.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.TaskNode;
import org.dromara.biz.domain.vo.CustomerServiceStepVO;
import org.dromara.biz.domain.vo.TaskNodeVO;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【biz_task_node(服务节点表)】的数据库操作Service
* @createDate 2024-05-18 16:30:24
*/
public interface TaskNodeService extends IService<TaskNode> {

    /**
     * 分页查询
     */
    TableDataInfo<TaskNodeVO> queryPage(PageQuery query);

    /**
     * 查询所有列表
     */
    List<TaskNodeVO> queryList();

    /**
     * 通过用户id获取用户关联的
     */
    List<TaskNodeVO> getListByUserId(Long userId);

    TaskNodeVO queryInfo(Long taskNodeId);

    Boolean save(TaskNodeVO taskNodeVO);

    Boolean update(TaskNodeVO taskNodeVO);

    Map<Long, TaskNodeVO> getMapByIds(Set<Long> taskNodeIds);

    /**
     * 更改节点精选状态
     */
    Boolean changeFeatured(TaskNodeVO taskNodeVO);

    List<TaskNodeVO> listByType(String type,String role);

    List<TaskNodeVO> getOptions();

    /**
     * 删除节点（标签）如果已经被动态使用则无法删除标签
     */
    Boolean remove(Long id);

    /**
     * 获取用户相关的服务过程标签 按照分组格式返回
     */
    List<CustomerServiceStepVO> getCustomerServiceStepList(Long userId, String group);
}
