package org.dromara.biz.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.dromara.biz.domain.Packages;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@AutoMapper(target = Packages.class)
public class PackageBo implements Serializable {
    /**
     * 主键id
     */
    private Long packageId;

    /**
     * 咨询次数
     */
    private Integer consultNumber;

    /**
     * 套餐名称
     */
    @NotBlank(message = "名称不能为空")
    private String packageName;

    /**
     * 套餐价格
     */
    private BigDecimal packagePrice;

    /**
     * 入住天数
     */
//    @NotNull(message = "入住天数不能为空")
    private Integer stayDays;

    /**
     * 房型id
     */
    private Long suiteId;

    /**
     * 房型名称id
     */
    private String suiteName;
}
