package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.io.Serializable;

/**
 * 签到礼物领取记录表
 * @TableName biz_checkin_gifts_record
 */
@TableName(value ="biz_checkin_gifts_record")
@Data
@EqualsAndHashCode(callSuper = true)
public class CheckinGiftsRecord extends TenantEntity implements Serializable {
    /**
     *
     */
    @TableId
    private Long checkGiftsId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 礼物id
     */
    private Long giftsId;

    /**
     * 签到天数
     */
    private Long checkInDays;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
