package org.dromara.biz.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.*;
import org.dromara.biz.domain.bo.diary.DiaryBO;
import org.dromara.biz.domain.query.diary.DiaryQuery;
import org.dromara.biz.domain.vo.ClubVO;
import org.dromara.biz.domain.vo.DiaryMomVO;
import org.dromara.biz.domain.vo.DiaryVO;
import org.dromara.biz.domain.vo.WechatUserVO;
import org.dromara.biz.mapper.BizDiaryMapper;
import org.dromara.biz.service.*;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.enums.AppUserRoleEnum;
import org.dromara.common.core.enums.UgcSuggestEnum;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.service.ClubService;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.jetbrains.annotations.NotNull;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 宝妈笔记表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-31.
 */
@Service
@AllArgsConstructor
@Slf4j
public class BizDiaryServiceImpl extends ServiceImpl<BizDiaryMapper, BizDiary> implements IBizDiaryService {

    private final IBizDiaryMomService momService;
    private final ClubsService clubService;
    private final WechatRoleService wechatRoleService;
    private final IBizDiaryRemindersService remindersService;
    private final WxMaService wxMaService;
    private final WechatUserService wechatUserService;
    private final IBizDiaryItemService itemService;


    @Override
    public Boolean publish(DiaryBO diaryBO) {
//        if(StpUtil.hasRole(AppUserRoleEnum.CUSTOMER.name()) ||
//            StpUtil.hasRole(AppUserRoleEnum.USER.name())){
//            throw new ServiceException("权限不足！");
//        }

        BizDiary bizDiary = MapstructUtils.convert(diaryBO, BizDiary.class);
        baseMapper.insert(bizDiary);
        return true;
    }

    @Override
    public DiaryVO getDetail(String diaryId) {
        DiaryVO  diaryVO =  baseMapper.getDetail(diaryId);
        if (diaryVO == null) {
            return null;
        }
        diaryVO.setRoleName(AppUserRoleEnum.getValueByKey(diaryVO.getRoleCode()));
        //宝妈信息
        DiaryMomVO detail = momService.detail(diaryVO.getMomId(),null);
        diaryVO.setAge(detail.getAge());
        diaryVO.setAvatar(detail.getAvatar());
        diaryVO.setServiceStartDate(detail.getServiceStartDate());
        diaryVO.setBabyWeight(detail.getBabyWeight());
        //海报信息
        ClubVO info = clubService.getInfo();
        diaryVO.setLogo(info.getLogo());
        diaryVO.setPosterSlogan(info.getPosterSlogan());
            if (StpUtil.hasRoleOr(AppUserRoleEnum.NURSE.name())){
                //护理
                diaryVO.setChefPosterSlogan(info.getNursePosterSlogan());
            } else if (StpUtil.hasRoleOr(AppUserRoleEnum.CHEF.name())) {
                //厨师
                diaryVO.setChefPosterSlogan(info.getChefPosterSlogan());
            }else if (StpUtil.hasRoleOr(AppUserRoleEnum.POSTPARTUM.name())) {
                //产康
                diaryVO.setChefPosterSlogan(info.getPostpartumPosterSlogan());
            }
        return diaryVO;
    }

    @Override
    public TableDataInfo<DiaryVO> queryPage(DiaryQuery query) {
        QueryWrapper<BizDiary> qw = Wrappers.query();
        if(ObjectUtil.isNotNull(query.getTaskNodeId())){
            qw.eq("d.task_node_id", query.getTaskNodeId());
        }
        if(ObjectUtil.isNotNull(query.getMomId())){
             qw.eq("d.mom_id", query.getMomId());
        }
        if(StringUtils.isNotEmpty(query.getUuid())){
            qw.eq("mom.uuid", query.getUuid());
        }
        if(ObjectUtil.isNotNull(query.getTimeNumber())){
            qw.eq("d.time_number", query.getTimeNumber());
        }
        if(StringUtils.isNotEmpty(query.getTel())){
            qw.eq("mom.tel", query.getTel());
        }
        if(ObjectUtil.isNotNull(query.getUserId())){
            qw.eq("d.create_by", query.getUserId());
        }
        //取消优质宝妈则不显示她的笔记
        qw.eq("mom.is_show",1);
        qw.orderByAsc("d.time_number");
        Page<DiaryVO> page = baseMapper.selectDiaryPage(query.build(), qw);
        if(ObjectUtil.isNull(query.getTaskNodeId())){
            List<DiaryVO> records = page.getRecords();
            if (records == null || records.isEmpty()) {
                //没有服务笔记的时候仅显示服务项
                List<BizDiaryItem> list = itemService.list();
                records = new ArrayList<>();
                records = getDiaryItem(records, list);
                page.setRecords(records);
                return TableDataInfo.build(page);
            }
            List<Integer> timeNumberList = records.stream().map(DiaryVO::getTimeNumber).distinct().toList();
            if (timeNumberList!=null && timeNumberList.size()>0){
                List<BizDiaryItem> list = itemService.lambdaQuery().notIn(BizDiaryItem::getTimeNumber, timeNumberList).list();
                records = getDiaryItem( records, list);
                page.setRecords(records);
            }
        }
        return TableDataInfo.build(page);
    }

    /**
     * 服务项转换记录
     * @param records
     * @param list
     * @return
     */
    private List<DiaryVO> getDiaryItem( List<DiaryVO> records, List<BizDiaryItem> list) {
        for (BizDiaryItem item : list) {
            DiaryVO diaryVO = new DiaryVO();
            diaryVO.setTimeNumber(item.getTimeNumber());
            diaryVO.setItemName(StringUtils.splitList(item.getName()));
            records.add(diaryVO);
        }
        records = records.stream().sorted(Comparator.comparing(DiaryVO::getTimeNumber)).collect(Collectors.toList());
        return records;
    }

    @Override
    public Boolean updateDiary(DiaryBO diaryBO) {
        BizDiary bizDiary = MapstructUtils.convert(diaryBO, BizDiary.class);
        baseMapper.updateById(bizDiary);
        return true;
    }

    @Override
    public Boolean createReminder(RemindersDiaryParams remindersDiaryParams) {

        List<WechatUserVO> users = null;
        //客资宝妈
        users =  wechatUserService.selectAllMom();
//        if (remindersDiaryParams.getReceivingType().equals("1")){
//
//        }
        if (users == null || users.isEmpty()) {
            return false;
        }
        String datePart = remindersDiaryParams.getDate();
        String timePart = remindersDiaryParams.getTime();
        Date parse;
        try{
            // 获取当前年份
            int currentYear = Calendar.getInstance().get(Calendar.YEAR);
            Calendar dateCalendar = wechatUserService.getCalendar(datePart, currentYear, timePart);
            parse = dateCalendar.getTime();
        }catch (Exception e){
            e.printStackTrace();
            throw new ServiceException("日期时间格式不正确");
        }
        DiaryVO diary = getDetail(String.valueOf(remindersDiaryParams.getDiaryId()));
        if(ObjectUtil.isNull(diary)){
            throw new ServiceException("笔记不存在,无法创建提醒事项。");
        }
        List<BizDiaryReminders>  remindersList = new ArrayList<>();
        for (WechatUserVO user : users) {
            LambdaQueryWrapper<BizDiaryReminders> lqw = Wrappers.lambdaQuery();
            lqw.eq(BizDiaryReminders::getDiaryId, remindersDiaryParams.getDiaryId());
            lqw.eq(BizDiaryReminders::getStatus, false);
            lqw.eq(BizDiaryReminders::getUserId, user.getUserId());
            long count = remindersService.count(lqw);
            if(count > 0L){
                continue;
            }
            BizDiaryReminders reminders = new BizDiaryReminders();
            reminders.setDiaryId(diary.getDiaryId());
            reminders.setMomId(diary.getMomId());
            reminders.setReminderDate(parse);
            reminders.setUserId(user.getUserId());
            reminders.setOpenid(user.getOpenid());
            reminders.setAppid(user.getAppid());
            reminders.setDescription(remindersDiaryParams.getDescription());
            reminders.setTitle(StrUtil.format("宝妈{}，发布新笔记啦！", diary.getMomName()));
            reminders.setStatus(false);
            remindersList.add(reminders);
        }

        remindersService.saveBatch(remindersList);
        LambdaQueryWrapper<BizDiaryReminders> lqw = Wrappers.lambdaQuery();
        lqw.eq(BizDiaryReminders::getDiaryId, remindersDiaryParams.getDiaryId());
        remindersList =  remindersService.list(lqw);
        handlerSetAlarm(parse,remindersList);
        return true;
    }

    @Async
    protected void handlerSetAlarm(Date time, List<BizDiaryReminders> remindersList) {

        LocalDateTime targetTime = DateUtil.toLocalDateTime(time);
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 计算当前时间和目标时间之间的差值（以秒为单位）
        long delay = Duration.between(now, targetTime).getSeconds();
        if (delay <= 0) {
            throw new ServiceException("目标时间已过，无法设置提醒!", 100012);
        }
        for (BizDiaryReminders reminders : remindersList) {
            ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
            scheduler.schedule(() -> {
                if(ObjectUtil.isNotNull(reminders)){

                    // 微信通知
                    if (!wxMaService.switchover(reminders.getAppid())) {
                        throw new ServiceException(String.format("未找到对应appid=[%s]的配置，请核实！", reminders.getAppid()));
                    }
                    String time1 = DateUtil.format(reminders.getReminderDate(), "yyyy-MM-dd HH:mm");
                    String thing2 = null;
                    if (StringUtils.isNotEmpty(reminders.getDescription())){
                        thing2 = StringUtils.substring(reminders.getDescription(),0,10);
                    }else {
                        thing2 = "我们发布了一条消息，请查收";
                    }

                    WxMaSubscribeMessage subscribeMessage = new WxMaSubscribeMessage();
                    subscribeMessage.setTemplateId("4ozrWQOn4Z0lxEfPNXyVzNaKCWaanMNYypzOu8PH9I0");
                    subscribeMessage.setToUser(reminders.getOpenid());
                    subscribeMessage.setPage("pageA/pageB/community/note/notedetail?momId="+reminders.getMomId());
                    List<WxMaSubscribeMessage.MsgData> msgData = new ArrayList<>();
                    msgData.add(new WxMaSubscribeMessage.MsgData("time3", time1));
                    msgData.add(new WxMaSubscribeMessage.MsgData("thing4", reminders.getTitle()));
                    msgData.add(new WxMaSubscribeMessage.MsgData("thing5", thing2));
                    subscribeMessage.setData(msgData);
                    try {
                        wxMaService.getMsgService().sendSubscribeMsg(subscribeMessage);
                    } catch (Exception e) {
                        log.error("微信模版订阅消息推送失败", e);
                    }
                    // 更新提醒状态
                    LambdaUpdateWrapper<BizDiaryReminders> luw = Wrappers.lambdaUpdate();
                    luw.eq(BizDiaryReminders::getId, reminders.getId());
                    luw.set(BizDiaryReminders::getStatus, true);
                    remindersService.update(luw);
                }
            }, delay, TimeUnit.SECONDS);
            scheduler.shutdown();
        }

    }
}
