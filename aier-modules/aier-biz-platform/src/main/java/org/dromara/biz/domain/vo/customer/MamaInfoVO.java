package org.dromara.biz.domain.vo.customer;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 宝妈详情vo
 */
@Data
public class MamaInfoVO {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 角色code
     */
    private String roleCode;

    /**
     * 入住时间
     */
    private Date checkInTime;

    /**
     * 房间id
     */
    private Long roomId;

    /**
     * 服务人员列表
     */
    private List<EmployeeInfo> employeeInfoList;

    /**
     * 宝妈相关标签列表
     */
    private List<TagInfo> tagInfoList;

    /**
     * 服务人员列表
     */
    @Data
    public static class  EmployeeInfo {

        /**
         * 员工id
         */
        private Long employeeId;

        /**
         * staffId
         */
        private Long staffId;
        /**
         * 头像
         */
        private String avatar;
        /**
         * 员工绑定的用户id
         */
        private Long userId;
        /**
         * 房间id
         */
        private Long roomId;
        /**
         * 名称+角色
         */
        private String name;
    }

    /**
     * 相关标签信息
     */
    @Data
    public static class TagInfo {
        /**
         * 标签id
         */
        private Long id;
        /**
         * 标签名称
         */
        private String name;
    }
}
