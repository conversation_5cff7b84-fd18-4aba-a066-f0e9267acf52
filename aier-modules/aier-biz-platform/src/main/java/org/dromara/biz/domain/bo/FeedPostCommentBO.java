package org.dromara.biz.domain.bo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * 动态评论bo
 */
@Data
public class FeedPostCommentBO implements Serializable {

    /**
     * 动态id
     */
    @NotNull(message = "动态id不能为空")
    private Long postId;

    /**
     * 评论内容
     */
    @NotBlank(message = "评论内容不能为空")
    private String comment;

    /**
     * 父级评论，如果是一级评论则为空
     */
    private Long parentCommentId;
}
