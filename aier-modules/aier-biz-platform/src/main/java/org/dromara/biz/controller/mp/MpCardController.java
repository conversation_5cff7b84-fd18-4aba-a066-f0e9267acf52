package org.dromara.biz.controller.mp;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.Card;
import org.dromara.biz.domain.CardCollect;
import org.dromara.biz.domain.CardInvitations;
import org.dromara.biz.domain.CardSetting;
import org.dromara.biz.domain.query.card.CardQuery;
import org.dromara.biz.domain.vo.card.CardEditStatusVO;
import org.dromara.biz.domain.vo.card.CardShareParams;
import org.dromara.biz.domain.vo.card.CardTagVO;
import org.dromara.biz.domain.vo.card.CardTemplateVO;
import org.dromara.biz.domain.vo.card.UserCardVO;
import org.dromara.biz.service.*;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * h5模版相关接口
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping("/mp/card")
public class MpCardController {

    private final CardTemplateService cardTemplateService;
    private final CardService cardService;
    private final CardCollectService cardCollectService;
    private final CardInvitationsService cardInvitationsService;
    private final CardSettingService cardSettingService;

    /**
     * 获取h5模版列表
     */
    @GetMapping("/getList")
    public TableDataInfo<CardTemplateVO> getList(CardQuery query) {
        return cardTemplateService.queryPage(query);
    }

    /**
     * 获取用户编辑的h5模版列表
     */
    @GetMapping("/getUserCardList")
    public TableDataInfo<Card> getUserCardList(CardQuery query) {
        return cardTemplateService.getUserCardList(query);
    }

    /**
     * 获取h5模版详情
     */
    @GetMapping("/getDetail")
    public R<CardTemplateVO> getDetail(@RequestParam("id") Long templateId) {
        return R.ok(cardTemplateService.getTemplateDetail(templateId));
    }

    /**
     * 创建用户的h5模版
     */
    @GetMapping("/create")
    public R<Long> create(@RequestParam("templateId") Long templateId) {
        return R.ok(cardTemplateService.createCard(templateId));
    }

    /**
     * 获取用户创建的h5模版详情
     */
    @GetMapping("/getUserCardDetail")
    public R<UserCardVO> getUserCardDetail(@RequestParam("id") Long cardId) {
        return R.ok(cardTemplateService.getUserCardDetail(cardId));
    }

    /**
     * 保存用户的h5模版
     */
    @PostMapping("/saveUserCard")
    public R<Long> saveUserCard(@RequestBody Card card) {
        return R.ok(cardTemplateService.saveUserCard(card));
    }

    /**
     * 分享用户编辑的h5
     */
    @PostMapping("/share")
    public R<Boolean> cardShare(@RequestBody CardShareParams cardShareParams) {
        return R.ok(cardTemplateService.cardShare(cardShareParams));
    }

    /**
     * 获取某个模版的用户编辑状态 true=之前编辑过该模版并且没有分享；false=没有编辑
     */
    @GetMapping("/getEditStatus")
    public R<CardEditStatusVO> getEditStatus(@RequestParam("templateId") Long templateId) {
        return R.ok(cardTemplateService.getEditStatus(templateId));
    }

    /**
     * 删除用户的h5模版
     */
    @GetMapping("/delete")
    @Deprecated
    public R<Boolean> delete(@RequestParam("id") Long id) {
        return R.ok(cardService.removeById(id));
    }

    /**
     * 删除用户制作的请帖
     * @param cardId 请帖id
     */
    @DeleteMapping("/removeByCardId")
    public R<Boolean> removeByCardId(@RequestParam("cardId") Long cardId){
        return R.ok(cardService.removeByCardId(cardId));
    }

    /**
     * 我的收藏
     * @return
     */
    @GetMapping("/myCollect")
    public R<List<UserCardVO>> myCollect() {
        List<UserCardVO> cardVOList = cardService.myCollect();
        return R.ok(cardVOList);
    }

    /**
     * 收藏h5模版
     */
    @PostMapping("/collect")
    @RepeatSubmit(interval = 1000)
    public R<Boolean> collect(@RequestBody CardCollect cardCollect) {
        return R.ok(cardCollectService.collect(cardCollect));
    }

    /**
     * 查询用户是否收藏了h5模版
     * @param templateId h5模版id
     * @return 是否收藏
     */
    @GetMapping("/getUserFavorites")
    public R<Boolean> getUserFavorites(@RequestParam("templateId") Long templateId) {
        return R.ok(cardCollectService.getUserFavorites(templateId));
    }

    /**
     * 保存请帖邀请人和分享信息
     */
    @PostMapping("/saveInvitation")
    @RepeatSubmit(interval = 1000)
    public R<Boolean> saveInvitation(@RequestBody CardInvitations invitations) {
        return R.ok(cardInvitationsService.saveInvitation(invitations));
    }

    /**
     * 查询请帖邀请人和分享信息
     */
    @GetMapping("/getInvitation")
    public R<CardInvitations> getInvitation(@RequestParam("cardId") Long cardId) {
        return R.ok(cardInvitationsService.getInvitation(cardId));
    }

    /**
     * 保存请帖功能设置
     */
    @PostMapping("/saveSetting")
    @RepeatSubmit(interval = 1000)
    public R<Boolean> saveCardSetting(@RequestBody CardSetting cardSetting) {
        return R.ok(cardSettingService.saveCardSetting(cardSetting));
    }

    /**
     * 查询请帖功能设置信息
     */
    @GetMapping("/getSetting")
    public R<CardSetting> getCardSetting(@RequestParam("cardId") Long cardId) {
        return R.ok(cardSettingService.getCardSetting(cardId));
    }

    /**
     * 获取所有标签列表（用于筛选）
     */
    @GetMapping("/getAllTags")
    public R<List<CardTagVO>> getAllTags() {
        return R.ok(cardTemplateService.getAllTags());
    }
}
