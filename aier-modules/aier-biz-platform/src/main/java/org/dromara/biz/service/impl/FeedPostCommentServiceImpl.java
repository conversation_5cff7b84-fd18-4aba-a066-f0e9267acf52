package org.dromara.biz.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.biz.domain.CommunityComment;
import org.dromara.biz.domain.FeedPostComment;
import org.dromara.biz.domain.vo.FeedPostCommentVO;
import org.dromara.biz.domain.vo.WechatUserVO;
import org.dromara.biz.mapper.FeedPostCommentMapper;
import org.dromara.biz.service.FeedPostCommentService;
import org.dromara.biz.service.WechatUserService;
import org.dromara.common.core.enums.UgcSuggestEnum;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【biz_feed_post_comment(会所朋友圈动态评论表)】的数据库操作Service实现
* @createDate 2024-03-15 10:57:12
*/
@Service
@AllArgsConstructor
public class FeedPostCommentServiceImpl extends ServiceImpl<FeedPostCommentMapper, FeedPostComment>
    implements FeedPostCommentService{

    private final WechatUserService wechatUserService;

    @Override
    public Map<Long, List<FeedPostCommentVO>> getGroupMap(Set<Long> postSet) {
        if(CollectionUtils.isEmpty(postSet)){
            return Map.of();
        }
        LambdaQueryWrapper<FeedPostComment> lqw = Wrappers.lambdaQuery();
        lqw.eq(FeedPostComment::getSuggest, UgcSuggestEnum.PASS.getSuggest());
        lqw.in(FeedPostComment::getPostId, postSet);
        List<FeedPostComment> commentList = list(lqw);
        List<Long> userIds = commentList.stream().map(FeedPostComment::getUserId).distinct().toList();
        Map<Long, WechatUserVO> userMap = wechatUserService.queryMapByIds(userIds);

        return commentList.stream().map(comment -> {
            Long userId = comment.getUserId();
            WechatUserVO user = userMap.getOrDefault(userId, new WechatUserVO());
            FeedPostCommentVO commentVo = new FeedPostCommentVO();
            commentVo.setCommentId(comment.getCommentId());
            commentVo.setComment(comment.getComment());
            commentVo.setNickname(user.getNickname());
            commentVo.setPostId(comment.getPostId());
            commentVo.setAvatar(user.getAvatar());
            commentVo.setUserId(userId);
            comment.setCreateTime(comment.getCreateTime());
            return commentVo;
        }).collect(Collectors.groupingBy(FeedPostCommentVO::getPostId));
    }

    @Override
    public Map<Long, FeedPostComment> feedPostCommentService(List<Long> commentIds2) {
        if(CollUtil.isEmpty(commentIds2)){
            return MapUtil.empty();
        }
        LambdaQueryWrapper<FeedPostComment> lqw = Wrappers.lambdaQuery();
        lqw.in(FeedPostComment::getCommentId, commentIds2);
        List<FeedPostComment> communityComments = baseMapper.selectList(lqw);
        return communityComments.stream()
            .collect(Collectors.toMap(FeedPostComment::getCommentId, Function.identity(), (o1, o2) -> o1));
    }
}




