package org.dromara.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.BizComplaint;
import org.dromara.biz.domain.FeedPost;
import org.dromara.biz.domain.TaskNode;
import org.dromara.biz.domain.vo.ComplaintVo;
import org.dromara.biz.domain.vo.WechatUserVO;
import org.dromara.biz.mapper.BizComplaintMapper;
import org.dromara.biz.service.IBizComplaintService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.biz.service.WechatUserService;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <p>
 * 用户投诉表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-22
 */
@Service
@AllArgsConstructor
@Slf4j
public class BizComplaintServiceImpl extends ServiceImpl<BizComplaintMapper, BizComplaint> implements IBizComplaintService {
    private final WechatUserService wechatUserService;
    @Override
    public List<ComplaintVo> queryComplaintVoByUser(Long userId) {
        LambdaUpdateWrapper<BizComplaint> luw = Wrappers.lambdaUpdate();
        luw.eq(BizComplaint::getUserId, userId);
        return baseMapper.selectVoList(luw);
    }

    @Override
    public TableDataInfo<ComplaintVo> queryPage(PageQuery query) {
        LambdaQueryWrapper<BizComplaint> lqw = new LambdaQueryWrapper<>();
        lqw.orderByAsc(BizComplaint::getStatus);
        lqw.orderByDesc(BizComplaint::getCreateTime);
        IPage<ComplaintVo> complaintVoIPage = baseMapper.selectVoPage(query.build(), lqw);
        return TableDataInfo.build(complaintVoIPage.getRecords(), complaintVoIPage.getTotal());
    }

    @Override
    public Boolean handle(ComplaintVo complaintVo) {
        Long loginUserId = LoginHelper.getUserId();
        WechatUserVO userVO = wechatUserService.getByUserId(loginUserId);
        BizComplaint complaint = baseMapper.selectById(complaintVo.getComplaintId());
        //已处理
        complaint.setStatus(2);
        complaint.setFeedbackContent(complaintVo.getFeedbackContent());
        complaint.setFeedbackTime(new Date());
        complaint.setFeedbackTransactorName(userVO.getNickname());
        complaint.setFeedbackTransactor(loginUserId);
        baseMapper.updateById(complaint);
        return true;
    }
}
