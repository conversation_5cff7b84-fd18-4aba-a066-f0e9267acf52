package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.dromara.common.mybatis.handler.type.StringListTypeHandler;
import org.dromara.common.tenant.core.TenantEntity;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 宝宝用药记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("biz_baby_medication")
public class BizBabyMedication extends TenantEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 宝宝ID
     */
    private Long babyId;

    /**
     * 记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date recordTime;

    /**
     * 药名
     */
    private String medicineName;

    /**
     * 用量
     */
    private String dosage;

    /**
     * 用药方法（如：口服、外用等）
     */
    private String administrationMethod;

    /**
     * 用水冲服（单位：毫升）
     */
    private String waterVolume;

    /**
     * 异常情况
     */
    private String abnormalCondition;

    /**
     * 备注
     */
    private String notes;

    /**
     * 照片列表（存储图片路径或URL，JSON格式）
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> photoList;
}
