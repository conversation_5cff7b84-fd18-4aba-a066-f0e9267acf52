package org.dromara.biz.controller.pc;

import lombok.RequiredArgsConstructor;
import org.dromara.biz.domain.Supplies;
import org.dromara.biz.domain.query.SuppliesQuery;
import org.dromara.biz.domain.vo.SuppliesVO;
import org.dromara.biz.service.SuppliesService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 用品相关接口
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/supplies")
public class SuppliesController {

    private final SuppliesService suppliesService;

    /**
     * 分页查询用品列表
     * @param query 查询参数
     * @return 结果
     */
    @GetMapping("/page")
    public TableDataInfo<SuppliesVO> queryPage(SuppliesQuery query){
        return suppliesService.queryPage(query);
    }

    /**
     * 查询用品信息
     * @param supplyId 用品id
     * @return 结果
     */
    @GetMapping("/info/{supplyId}")
    public R<SuppliesVO> info(@PathVariable Long supplyId){
        Supplies supplies = suppliesService.getById(supplyId);
        return R.ok(MapstructUtils.convert(supplies, SuppliesVO.class));
    }

    /**
     * 新增妈妈用品
     * @param vo 妈妈用品信息
     * @return 新增结果
     */
    @PutMapping("/save_mama")
    @RepeatSubmit
    public R<Boolean> saveMama(@RequestBody @Validated SuppliesVO vo) {
        Supplies supplies = MapstructUtils.convert(vo, Supplies.class);
        supplies.setCategory(Supplies.MAMA);
        return R.ok(suppliesService.save(supplies));
    }

    /**
     * 新增宝宝用品
     * @param vo 宝宝用品信息
     * @return 新增结果
     */
    @PutMapping("/save_baby")
    @RepeatSubmit
    public R<Boolean> saveBaby(@RequestBody @Validated SuppliesVO vo) {
        Supplies supplies = MapstructUtils.convert(vo, Supplies.class);
        supplies.setCategory(Supplies.BABY);
        return R.ok(suppliesService.save(supplies));
    }

    /**
     * 修改用品
     * @param suppliesvo 用品信息
     * @return 结果
     */
    @PostMapping("/update")
    @RepeatSubmit
    public R<Boolean> update(@RequestBody @Validated SuppliesVO suppliesvo){
        Supplies supplies = MapstructUtils.convert(suppliesvo, Supplies.class);
        return R.ok(suppliesService.updateById(supplies));
    }

    /**
     * 删除用品
     * @param suppliesId 用品id
     * @return 删除结果
     */
    @DeleteMapping("/{suppliesId}")
    public R<Boolean> update(@PathVariable Long suppliesId) {
        return R.ok(suppliesService.removeById(suppliesId));
    }
}
