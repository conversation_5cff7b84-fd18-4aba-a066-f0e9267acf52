package org.dromara.biz.domain;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class RemindersDiaryParams {
    /**
     * 日期 MM-dd
     */
    @NotNull(message = "日期不能为空")
    private String date;
    /**
     * 时间 HH:mm
     */
    @NotNull(message = "时间不能为空")
    private String time;

    /**
     * 宝妈id
     */
    @NotNull(message = "宝妈id不能为空")
    private Long momId;

    /**
     * 笔记id
     */
    @NotNull(message = "笔记id不能为空")
    private Long diaryId;

    /**
     * 描述or备注
     */
    private String description;

    /**
     * 提醒事项id
     */
    private Long reminderId;

    /**
     * 接收类型  0全部，1客资宝妈
     */
    private String receivingType;
}
