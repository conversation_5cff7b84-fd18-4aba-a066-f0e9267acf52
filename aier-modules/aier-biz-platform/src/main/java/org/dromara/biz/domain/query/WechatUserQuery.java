package org.dromara.biz.domain.query;

import lombok.Getter;
import lombok.Setter;
import org.dromara.common.mybatis.core.page.PageQuery;

@Getter
@Setter
public class WechatUserQuery extends PageQuery {

    /**
     * 模块类型
     */
    private String module;

    /**
     * 排序方式 view_time：访问时间最长 view_num：访问次数最多
     */
    private String orderBy;

    /**
     * 关键字
     */
    private String keyword;
}
