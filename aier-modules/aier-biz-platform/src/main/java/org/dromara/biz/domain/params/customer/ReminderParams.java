package org.dromara.biz.domain.params.customer;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 创建提醒事项参数
 */
@Data
public class ReminderParams {

    /**
     * 日期 MM-dd
     */
    @NotNull(message = "日期不能为空")
    private String date;
    /**
     * 时间 HH:mm
     */
    @NotNull(message = "时间不能为空")
    private String time;

    /**
     * 客户id
     */
    @NotNull(message = "客户id不能为空")
    private Long customerId;

    /**
     * 描述or备注
     */
    private String description;

    /**
     * 提醒事项id
     */
    private Long reminderId;
}
