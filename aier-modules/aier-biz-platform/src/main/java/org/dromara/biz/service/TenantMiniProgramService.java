package org.dromara.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.TenantMiniProgram;
import org.dromara.biz.domain.query.TenantMiniProgramQuery;
import org.dromara.biz.domain.vo.TenantMiniProgramVO;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
* <AUTHOR>
* @description 针对表【biz_tenant_mini_program(租户小程序表)】的数据库操作Service
* @createDate 2024-06-25 20:18:28
*/
public interface TenantMiniProgramService extends IService<TenantMiniProgram> {

    /**
     * 获取租户小程序信息
     * @param tenantId 租户id
     * @param miniProgramId 小程序appid
     * @return TenantMiniProgram
     */
    TenantMiniProgram getTenantProgram(String tenantId, Long miniProgramId);

    /**
     * 获取租户小程序信息
     * @param tenantId 租户id
     * @return TenantMiniProgram
     */
    TenantMiniProgram getTenantProgram(String tenantId);

    /**
     * 分页查询小程序商户列表
     * @param query 查询条件
     * @return 结果
     */
    TableDataInfo<TenantMiniProgramVO> queryPage(TenantMiniProgramQuery query);

    /**
     * 保存租户小程序信息
     * @param tenantMiniProgram 租户小程序信息
     * @return 结果
     */
    Boolean saveTenantMiniProgram(TenantMiniProgram tenantMiniProgram);

    /**
     * 生成小程序码 跳转页面为主页
     * @param tenantConfigId 小程序商户配置id
     * @return 结果
     */
    void generateQrCode(Long tenantConfigId);

    /**
     * 生成小程序码 指定路径
     * @param page 页面路径
     * @param tenantConfigId 小程序商户配置id
     */
    void generateQrCodeByPage(String page, Long tenantConfigId);

    /**
     * 生成URL LIKE
     * @param tenantConfigId 小程序商户配置id
     */
    void generateUrlLike(Long tenantConfigId);

    /**
     * 生成URL SCHEME
     * @param tenantConfigId 小程序商户配置id
     */
    void generateUrlScheme(Long tenantConfigId);
    /**
     * 生成小程序码 指定路径-返回url
     * @param pathUrl 页面路径
     */
    String generateQrCodeUrl(String pathUrl,String uuid);
}
