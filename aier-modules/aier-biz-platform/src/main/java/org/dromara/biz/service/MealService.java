package org.dromara.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.Meal;
import org.dromara.biz.domain.query.MealQuery;
import org.dromara.biz.domain.vo.MealVO;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
* <AUTHOR>
* @description 针对表【biz_meal(月子餐基本信息表)】的数据库操作Service
* @createDate 2024-03-11 16:54:27
*/
public interface MealService extends IService<Meal> {

    Boolean save(MealVO meal);

    Boolean update(MealVO meal);

    TableDataInfo<MealVO> queryPage(MealQuery query);

    MealVO getById(Long mealId);

    Boolean removeByMealId(Long mealId);

    MealVO getByMerchants();

    Boolean saveOrUpdate(MealVO meal);

    Boolean isMealTags(Long mealId);
}
