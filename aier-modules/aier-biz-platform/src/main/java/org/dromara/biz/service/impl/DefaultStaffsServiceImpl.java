package org.dromara.biz.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.dromara.biz.domain.DefaultCustomers;
import org.dromara.biz.domain.DefaultCustomersStaff;
import org.dromara.biz.domain.DefaultStaffs;
import org.dromara.biz.domain.bo.DefaultCustomerStaffBO;
import org.dromara.biz.service.DefaultCustomersService;
import org.dromara.biz.service.DefaultCustomersStaffService;
import org.dromara.biz.service.DefaultStaffsService;
import org.dromara.biz.mapper.DefaultStaffsMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_default_staffs(默认员工表)】的数据库操作Service实现
* @createDate 2024-08-15 17:54:54
*/
@Service
@RequiredArgsConstructor
public class DefaultStaffsServiceImpl extends ServiceImpl<DefaultStaffsMapper, DefaultStaffs>
    implements DefaultStaffsService{

    private final DefaultCustomersStaffService defaultCustomersStaffService;
    private final DefaultCustomersService defaultCustomersService;

    @Override
    public Boolean bindCustomer(DefaultCustomerStaffBO defaultCustomerStaffBO) {
        LambdaQueryWrapper<DefaultCustomersStaff> lqw = Wrappers.lambdaQuery();
        lqw.eq(DefaultCustomersStaff::getDefaultStaffId, defaultCustomerStaffBO.getDefaultStaffId());
        defaultCustomersStaffService.remove(lqw);

        List<Long> defaultCustomerIds = defaultCustomerStaffBO.getDefaultCustomerIds();
        List<DefaultCustomersStaff> defaultCustomersStaffList = defaultCustomerIds.parallelStream()
                .map(defaultCustomerId -> {
                        DefaultCustomersStaff defaultCustomersStaff = new DefaultCustomersStaff();
                        defaultCustomersStaff.setDefaultStaffId(defaultCustomerStaffBO.getDefaultStaffId());
                        defaultCustomersStaff.setDefaultCuatomerId(defaultCustomerId);
                        return defaultCustomersStaff;
        }).toList();
        return defaultCustomersStaffService.saveBatch(defaultCustomersStaffList);
    }

    @Override
    public List<DefaultCustomersStaff> getCustomersStaffList(Long defaultStaffId) {
        LambdaQueryWrapper<DefaultCustomersStaff> lqw = Wrappers.lambdaQuery();
        lqw.eq(DefaultCustomersStaff::getDefaultStaffId, defaultStaffId);
        List<DefaultCustomersStaff> list = defaultCustomersStaffService.list(lqw);
        return list.stream().peek(item->{
            Long defaultCuatomerId = item.getDefaultCuatomerId();
            DefaultCustomers customer = defaultCustomersService.getById(defaultCuatomerId);
            if(ObjectUtil.isNotNull(customer)){
                item.setCustomerName(customer.getName());
            }
        }).toList();
    }
}




