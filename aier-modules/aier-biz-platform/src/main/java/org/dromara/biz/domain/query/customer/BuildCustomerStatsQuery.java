package org.dromara.biz.domain.query.customer;

import lombok.Data;

import java.util.Date;

/**
 * 客户统计通用查询参数
 */
@Data
public class BuildCustomerStatsQuery {

    /**
     * 开始时间
     */
    private Date startDate;

    /**
     * 结束时间
     */
    private Date endDate;

    /**
     * 签约开始时间
     */
    private Date contractStartDate;

    /**
     * 签约结束时间
     */
    private Date contractEndDate;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 来源
     */
    private String source;

    /**
     * 跟进人id
     */
    private Long followerId;
}
