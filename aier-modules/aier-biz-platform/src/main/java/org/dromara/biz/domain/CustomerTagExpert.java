package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 客户标签专家咨询表
 * @TableName biz_customer_tag_expert
 */
@TableName(value ="biz_customer_tag_expert")
@Data
public class CustomerTagExpert implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 专家名称
     */
    private String expertName;

    /**
     * 专家图片
     */
    private String expertImg;

    /**
     * 内容 富文本格式
     */
    private String content;

    /**
     * 
     */
    private Date createTime;

    /**
     * 
     */
    private Date updateTime;

    /**
     * 客户标签id
     */
    private Long customerTagId;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}