package org.dromara.biz.controller.mp;


import com.dtflys.forest.annotation.Post;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.BizBabyInfo;
import org.dromara.biz.domain.vo.nurse.BabyCareVO;
import org.dromara.biz.domain.vo.nurse.BabyInfoVO;
import org.dromara.biz.service.BabyInfoService;
import org.dromara.biz.service.IBizBabyInfoService;
import org.dromara.common.core.domain.R;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import org.springframework.stereotype.Controller;

import java.util.Date;
import java.util.List;


/**
 * 宝宝信息表 前端控制器
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping("/mp/baby/info")
public class BizBabyInfoController {
    private final BabyInfoService babyInfoService;
    private final IBizBabyInfoService babyInfoServiceI;

    /**
     * 创建宝宝信息
     * @param babyInfo
     * @return
     */
    @PostMapping("/createInfo")
    public R<Boolean> createInfo(@RequestBody BizBabyInfo babyInfo) {
        return R.ok(babyInfoService.save(babyInfo));
    }

    /**
     * 修改宝宝信息
     * @param babyInfo
     * @return
     */
    @PostMapping("/updateInfo")
    public R<Boolean> updateInfo(@RequestBody BizBabyInfo babyInfo) {
        return R.ok(babyInfoService.updateById(babyInfo));
    }

    /**
     * 获取宝宝信息
     * @param babyId
     * @return
     */
    @GetMapping("/getDetail")
    public R<BabyInfoVO> getDetail(@RequestParam("babyId") Long babyId) {
        return R.ok(babyInfoService.getByVo(babyId));
    }
    /**
     * 获取我的宝宝信息
     * @return
     */
    @GetMapping("/getMyDetail")
    public R<BabyInfoVO> getMyDetail() {
        Long  babyId = babyInfoServiceI.getByLoginUser();
        if (babyId != null) {
            List<BizBabyInfo> babyInfoList = babyInfoService.getDetailByCustomerId(babyId);
            if (babyInfoList != null && babyInfoList.size() > 0) {
                babyId = babyInfoList.get(0).getBabyId();
                return R.ok(babyInfoService.getByVo(babyId));
            }
        }
        return R.ok(null);
    }

    /**
     * 获取宝妈全部宝宝信息
     * @param customerId
     * @return
     */
    @GetMapping("/getDetailByCustomerId")
    public R<List<BizBabyInfo>> getDetailByCustomerId(@RequestParam("customerId") Long customerId) {
        return R.ok(babyInfoService.getDetailByCustomerId(customerId));
    }

    /**
     * 获取当前登陆人全部宝宝信息
     * @return
     */
    @GetMapping("/getMyDetailList")
    public R<List<BizBabyInfo>> getMyDetailList() {
        Long  customerId = babyInfoServiceI.getByLoginUser();
        if (customerId != null) {
            return R.ok(babyInfoService.getDetailByCustomerId(customerId));
        }
        return R.ok(null);
    }

    /**
     * 获取宝宝数据分析
     * @param babyId
     * @param time
     * @return
     */
    @GetMapping("/getBabyAnalysis")
    public R<List<BabyCareVO>> getBabyAnalysis(@RequestParam("babyId") Long babyId, @RequestParam("time")@DateTimeFormat(pattern = "yyyy-MM") Date time) {
        return R.ok(babyInfoService.getBabyAnalysis(babyId,time));
    }





}
