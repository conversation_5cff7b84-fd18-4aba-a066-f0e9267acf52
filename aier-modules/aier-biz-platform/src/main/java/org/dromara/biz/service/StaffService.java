package org.dromara.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.Staff;
import org.dromara.biz.domain.bo.staff.BindEmployeeBO;
import org.dromara.biz.domain.query.StaffQuery;
import org.dromara.biz.domain.vo.StaffVO;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【biz_service_staff(员工信息表)】的数据库操作Service
* @createDate 2024-03-15 10:57:13
*/
public interface StaffService extends IService<Staff> {

    /**
     * 根据手机号查询员工信息
     * @param tel 手机号
     * @return 员工信息
     */
    Staff getByTel(String tel);

    /**
     * 分页查询员工信息
     * @param query 查询条件
     * @return 员工信息列表
     */
    TableDataInfo<StaffVO> queryPage(StaffQuery query);

    /**
     * 根据员工id查询员工信息
     * @param staffId 员工id
     * @return 员工信息
     */
    StaffVO getByStaffId(Long staffId);

    Boolean save(StaffVO vo);

    Boolean update(StaffVO vo);

    List<StaffVO> queryByStaffIds(List<String> staffIds);

    Map<Long, StaffVO> getMapByIds(List<String> staffIds);

    /**
     * 根据用户ids查询员工信息
     * @param userIds 用户id
     * @return 员工信息
     */
    Map<Long, StaffVO> getMapByUserIds(List<Long> userIds);

    /**
     * 查询房间员工列表
     * @return 员工列表
     */
    List<StaffVO> queryRoomStaffList(List<String> staffIds);

    /**
     * 更新服务人员数量
     * @param staffIds 服务人员ids
     * @return 更新结果
     */
    Boolean updateServiceNum(List<String> staffIds);

    /**
     * 根据用户id查询员工信息
     * @param userId 用户id
     * @return 员工信息
     */
    StaffVO getByUserId(Long userId);

    /**
     * 将staff表的数据与实际的员工用户绑定
     */
    Boolean bindEmployee(BindEmployeeBO bindEmployeeBO);
}
