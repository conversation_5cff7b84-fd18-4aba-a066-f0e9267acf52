package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 微信用户表
 * @TableName biz_wechat_user
 */
@TableName(value ="biz_wechat_user")
@Data
public class WechatUser implements Serializable {

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId
    private Long userId;

    /**
     * unionid
     */
    private String unionid;

    /**
     * openid
     */
    private String openid;

    /**
     * 用户类型 1小程序 2公众号 3虚拟用户 等等
     */
    private String utype;

    /**
     * 手机
     */
    private String tel;

    /**
     * 年龄
     */
    private String age;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 性别 0=男；1=女
     */
    private String sex;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 城市
     */
    private String city;

    /**
     * 地址
     */
    private String address;

    /**
     * 最近登录时间
     */
    private Date lastLoginTime;

    /**
     * 最近登录ip
     */
    private String lastLoginIp;

    /**
     * 预产期
     */
    private Date dueDate;

    /**
     * 出生日期
     */
    private Date birthDate;

    /**
     * 账号状态 0正常；1禁用
     */
    private String status;

    /**
     * appid
     */
    private String appid;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 租户编号
     */
    private String tenantId;

    /**
     * 用户名
     */
    private String username;
}
