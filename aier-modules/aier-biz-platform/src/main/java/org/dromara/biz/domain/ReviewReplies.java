package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.io.Serializable;

/**
 * 总体评价商家回复表
 * @TableName biz_review_replies
 */
@TableName(value ="biz_review_replies")
@Data
@EqualsAndHashCode(callSuper = true)
public class ReviewReplies extends TenantEntity implements Serializable {
    /**
     *
     */
    @TableId
    private Long replyId;

    /**
     * 商家总体评价id
     */
    private Long reviewId;

    /**
     * 商家回复内容
     */
    private String replyContent;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
