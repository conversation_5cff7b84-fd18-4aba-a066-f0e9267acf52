package org.dromara.biz.service;

import org.dromara.biz.domain.InvitationLikes;
import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.common.core.domain.R;
import org.springframework.web.bind.annotation.RequestParam;

/**
* <AUTHOR>
* @description 针对表【biz_invitation_likes(点赞表)】的数据库操作Service
* @createDate 2024-07-30 10:50:39
*/
public interface InvitationLikesService extends IService<InvitationLikes> {

    public Boolean addLike(Long masterId);
    public InvitationLikes getLikes(Long masterId);

}
