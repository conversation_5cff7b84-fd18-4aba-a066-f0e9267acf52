package org.dromara.biz.dto.customerservice;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WebSocketMessage {
    private String id;          // 消息ID
    private Integer type;       // 消息类型
    private String tenantId;      // 租户ID
    private String sessionId;   // 会话ID
    private Long fromId;        // 发送者ID
    private Long toId;          // 接收者ID
    private Integer contentType; // 内容类型
    private Object content;     // 消息内容
    private Long timestamp;     // 时间戳
    
    public WebSocketMessage(Integer type, Object content) {
        this.type = type;
        this.content = content;
        this.timestamp = System.currentTimeMillis();
    }
}