package org.dromara.biz.controller.mp;

import cn.dev33.satoken.annotation.SaIgnore;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.bo.EventTrackingCommunityBO;
import org.dromara.biz.domain.bo.EventTrackingEndBO;
import org.dromara.biz.service.EventTrackingCommunityService;
import org.dromara.common.core.domain.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 小程序事件追踪（埋点）相关接口
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping("/mp/event_tracking_community")
@SaIgnore
public class MpEventTrackingCommunityController {

    private EventTrackingCommunityService eventTrackingService;

    /**
     * 开始事件上报
     * @param eventTrackingBO 埋点事件
     * @return 结果
     */
    @PostMapping("/report_start")
    @SaIgnore
    public R<Long> start(@RequestBody @Validated EventTrackingCommunityBO eventTrackingBO){
        return R.ok("上报成功",eventTrackingService.start(eventTrackingBO));
    }

    /**
     * 事件上报
     * @param eventTrackingBO 埋点事件
     * @return 结果
     */
    @PostMapping("/report")
    @SaIgnore
    public R<Boolean> report(@RequestBody @Validated EventTrackingCommunityBO eventTrackingBO){
        return R.ok("上报成功",eventTrackingService.report(eventTrackingBO));
    }

    /**
     * 结束事件上报
     * @param eventTrackingEndBO 埋点事件结束
     * @return 结果
     */
    @PostMapping("/report_end")
    @SaIgnore
    public R<Boolean> end(@RequestBody EventTrackingEndBO eventTrackingEndBO){
        return R.ok(eventTrackingService.end(eventTrackingEndBO));
    }
}
