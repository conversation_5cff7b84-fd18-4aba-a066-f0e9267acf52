package org.dromara.biz.domain.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 用户访问统计vo
 */
@Data
public class UserVisitTotalStatsVO {

    /**
     * 访问量
     */
    private Integer uv;
    /**
     * 访问量较前一日类型 0新增 1下降 2无变化
     */
    private Integer uvDayBeforeType;
    /**
     * 访问量较前一日数据
     */
    private String uvDayBeforeNum;

    /**
     * 访问次数
     */
    private Integer pv;
    /**
     * 访问次数较前一日类型 0新增 1下降 2无变化
     */
    private Integer pvDayBeforeType;
    /**
     * 访问次数较前一日数据
     */
    private String pvDayBeforeRate;

    /**
     * 人均浏览量
     */
    private Integer avgUv;
    /**
     * 人均浏览量较前一日类型 0新增 1下降 2无变化
     */
    private Integer avgUvDayBeforeType;
    /**
     * 人均浏览量较前一日数据
     */
    private String avgUvDayBeforeRate;

    /**
     * 跳失率
     */
    private String lossRate;

    /**
     * 原始跳失率百分比
     */
    private BigDecimal lossRateSource = BigDecimal.ZERO;
    /**
     * 跳失率较前一日类型 0新增 1下降 2无变化
     */
    private Integer lossDayBeforeType;
    /**
     * 跳失率较前一日数据
     */
    private String lossDayBeforeRate;

    /**
     * 人均浏览时长
     */
    private Long avgDurationSeconds = 0L;
    /**
     * 人均浏览时常
     */
    private String avgDurationStr;
    /**
     * 人均浏览时长较前一日类型 0新增 1下降 2无变化
     */
    private Integer avgDurationChangeType;
    /**
     * 人均浏览时长较前一日数据
     */
    private String avgDurationSecondsRate;

    /**
     * 每日用户访问量
     */
    List<UserDayViewStatsVO> userAccessAnalysisStats;
}
