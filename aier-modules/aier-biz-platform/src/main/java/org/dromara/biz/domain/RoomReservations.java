package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 房间预定表
 * @TableName biz_room_reservations
 */
@TableName(value ="biz_room_reservations")
@Data
public class RoomReservations implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 房间id
     */
    private Long roomId;

    /**
     * 预定开始时间
     */
    private Date startDate;

    /**
     * 预定结束时间
     */
    private Date endDate;

    /**
     * 预定状态（0=待处理；1=已确认；2=已取消）
     */
    private Integer status;

    /**
     * 实收类型
     */
    private Integer actualPaymentType;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}