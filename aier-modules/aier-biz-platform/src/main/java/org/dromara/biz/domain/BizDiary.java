package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.dromara.common.mybatis.core.domain.BaseEntity;
import org.dromara.common.mybatis.handler.type.StringListTypeHandler;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 宝妈笔记表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("biz_diary")
public class BizDiary extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId
    private Long diaryId;

    /**
     * 服务天数
     */
    private Integer timeNumber;

    /**
     * 角色编码
     */
    private String roleCode;

    /**
     * 内容
     */
    private String content;

    /**
     * 图片
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> imgs;


    /**
     * 视频链接
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> videos;

    /**
     * 标签id
     */
    private Long taskNodeId;

    /**
     * 发布人user_id
     */
    private Long userId;

    /**
     * 宝妈id
     */
    private Long momId;
}
