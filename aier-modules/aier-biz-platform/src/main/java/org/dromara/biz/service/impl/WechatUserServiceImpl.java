package org.dromara.biz.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.binarywang.wx.miniapp.bean.security.WxMaMsgSecCheckCheckResponse;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.subscribe.WxMpSubscribeMessage;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.biz.domain.*;
import org.dromara.biz.domain.bo.MessageNotificationBO;
import org.dromara.biz.domain.bo.wechat.WechatUserBO;
import org.dromara.biz.domain.bo.wechat.WechatUserProfile;
import org.dromara.biz.domain.event.WechatUserEvent;
import org.dromara.biz.domain.event.WechatUserRemoveEvent;
import org.dromara.biz.domain.query.UserAnalyseQuery;
import org.dromara.biz.domain.query.UserStatsQuery;
import org.dromara.biz.domain.query.WechatUserQuery;
import org.dromara.biz.domain.vo.*;
import org.dromara.biz.mapper.EventTrackingMapper;
import org.dromara.biz.mapper.WechatUserMapper;
import org.dromara.biz.service.*;
import org.dromara.common.core.constant.UgcConstants;
import org.dromara.common.core.enums.UgcBusinessType;
import org.dromara.common.core.enums.UgcSceneEnum;
import org.dromara.common.core.enums.UgcSuggestEnum;
import org.dromara.common.core.enums.UserStatus;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.SpringUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Nonnull;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【biz_wechat_user(微信用户表)】的数据库操作Service实现
 * @createDate 2024-03-13 20:35:08
 */
@Service
@Slf4j
@AllArgsConstructor
public class WechatUserServiceImpl extends ServiceImpl<WechatUserMapper, WechatUser>
    implements WechatUserService {

    private final CustomersService customersService;
    private final EventTrackingMapper eventTrackingMapper;
    private final WechatService wechatService;
    private final WxMaService wxMaService;
    private final WxMpService wxMpService;
    private final WechatNotificationsService wechatNotificationsService;
    private final TenantMiniProgramService tenantMiniProgramService;
    private final WechatMiniProgramService wechatMiniProgramService;
    private final WechatUserRoleService wechatUserRoleService;
    private final WechatRoleService wechatRoleService;

    @Override
    public WechatUserVO queryByOpenIdAndTenantId(@Nonnull String openId, @Nonnull String tenantId) {
        return TenantHelper.ignore(() -> baseMapper.selectByOpenidAndTenantId(openId, tenantId));
    }

    public WechatUserVO queryByPhoneAndTenantId(@Nonnull String phone, @Nonnull String tenantId){
        return TenantHelper.ignore(() -> baseMapper.queryByPhoneAndTenantId(phone, tenantId));
    }

    @Override
    public WechatUserVO queryByUserId(Long userId) {
        WechatUserVO wechatUserVO = baseMapper.selectByUserId(userId);
        String tel = wechatUserVO.getTel();
        wechatUserVO.setHasBindPhone(StrUtil.isNotBlank(tel));
        return wechatUserVO;
    }

    private WechatUserVO convertHandler(WechatUser wechatUser) {
        if(ObjectUtil.isNull(wechatUser)) {
            return new WechatUserVO();
        }
        WechatUserVO user = MapstructUtils.convert(wechatUser, WechatUserVO.class);
        user.setHasBindPhone(StrUtil.isNotBlank(user.getTel()));
        return user;
    }

    @Override
    public Boolean save(WechatUserVO user) {
        WechatUser wechatUser = MapstructUtils.convert(user, WechatUser.class);
        wechatUser.setStatus(UserStatus.OK.getCode());
        wechatUser.setUtype("3");
        return save(wechatUser);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByUserId(WechatUser wechatUser) {

        String oldAvatar = wechatUser.getAvatar();

        if (StringUtils.isNotBlank(wechatUser.getNickname())) {
            WxMaMsgSecCheckCheckResponse.ResultBean messageCheck = wechatService
                .messageCheck(wechatUser.getNickname(), UgcSceneEnum.DATA.getScene());
            String suggest = messageCheck.getSuggest();
            if (!UgcSuggestEnum.PASS.getSuggest().equals(suggest)) {
                wechatUser.setNickname("违规昵称");
            }
        }
        if (StringUtils.isNotBlank(oldAvatar)) {
            wechatUser.setAvatar(UgcConstants.IMG_REVIEW);
            wechatService.imageCheckForData(oldAvatar, UgcBusinessType.USER_IMG.getType(), wechatUser.getUserId());
        }
        Boolean flag = updateById(wechatUser);
        WechatUserVO user = getByUserId(wechatUser.getUserId());
        if(StringUtils.isNotBlank(user.getTel())){
            wechatUser.setTel(user.getTel());
//            publisher.publishEvent(new WechatUserEvent<>(this, wechatUser));
        }
        return flag;
    }

    @Override
    public WechatUser getByTel(String tel) {
        LambdaQueryWrapper<WechatUser> lqw = Wrappers.lambdaQuery();
        lqw.eq(WechatUser::getTel, tel);
        return baseMapper.selectOne(lqw);
    }

    @Override
    public List<WechatUserVO> queryList(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return List.of();
        }
        LambdaQueryWrapper<WechatUser> wechatUserLqw = Wrappers.lambdaQuery();
        wechatUserLqw.in(WechatUser::getUserId, userIds);
        return baseMapper.selectVoList(wechatUserLqw);
    }

    @Override
    public Map<Long, WechatUserVO> queryMapByIds(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return Map.of();
        }
        List<WechatUserVO> userList = this.queryList(userIds);
        return userList.stream()
            .collect(Collectors.toMap(WechatUserVO::getUserId, Function.identity(), (n1, n2) -> n1));
    }

    @Override
    public WechatUserVO getByUserId(Long userId) {
        LambdaQueryWrapper<WechatUser> wechatUserLqw = Wrappers.lambdaQuery();
        wechatUserLqw.eq(WechatUser::getUserId, userId);
        WechatUser wechatUser = baseMapper.selectOne(wechatUserLqw);
        WechatUserVO result = this.convertHandler(wechatUser);

        LambdaQueryWrapper<WechatUserRole> wechatUserRoleLqw = Wrappers.lambdaQuery();
        wechatUserRoleLqw.eq(WechatUserRole::getUserId, userId);
        List<WechatUserRole> roles = wechatUserRoleService.list(wechatUserRoleLqw);
        if(CollUtil.size(roles) > 0){
            result.setRoleId(roles.get(0).getRoleId());
        }

        List<Long> roomList = baseMapper.getUserRoomList(userId);
        result.setRoomIds(roomList);

        Date checkInTime = baseMapper.getCheckInTime(userId);
        result.setCheckInTime(checkInTime);
        return result;
    }


    @Override
    public List<WechatUserVO> getArriveStoreUserList() {
        LambdaQueryWrapper<Customers> lqw = Wrappers.lambdaQuery();
        lqw.eq(Customers::getStatus, "2");
        List<Customers> customerList = customersService.list(lqw);
        List<Long> userIds = customerList.stream().map(Customers::getUserId).distinct().toList();
        if (CollectionUtils.isEmpty(userIds)) {
            return List.of();
        }
        return this.queryList(userIds);
    }

    @Override
    public UserStatsVO getUserStats(UserStatsQuery query) {
        query.buildQuery();//构造查询条件
        Date startDate = query.getStartDate();
        Date endDate = query.getEndDate();
        UserStatsVO userStatsVO = baseMapper.selectUserStats(startDate, endDate);
        List<UserSexStatsVO> sexStatsList = baseMapper.selectUserSexStats(startDate, endDate);
        List<UserDayViewStatsVO> dayViewStatsList = eventTrackingMapper.selectVisitStatsGroupDay(startDate, endDate);
        userStatsVO.setDayViewStatsList(dayViewStatsList);
        userStatsVO.setSexStatsList(sexStatsList);
        return userStatsVO;
    }

    @Override
    public TableDataInfo<UserAnalyseStatsVO> queryUserAnalysePage(UserAnalyseQuery query) {
        IPage<UserAnalyseStatsVO> page = baseMapper.queryUserAnalysePage(query.build(), query);
        List<UserAnalyseStatsVO> records = page.getRecords();
        records.forEach(UserAnalyseStatsVO::convertSeconds);
        return TableDataInfo.build(page);
    }

    public UserDueDateStatsVO queryDueDateStats() {
        return baseMapper.selectDueDateStats();
    }

    @Override
    public List<UserDueListStatsVO> queryDueListStats() {
        return baseMapper.queryDueListStats();
    }

    @Override
    public TableDataInfo<WechatUserVO> queryPage(WechatUserQuery query) {
        LambdaQueryWrapper<WechatUser> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(WechatUser::getCreateTime);
        lqw.and(StrUtil.isNotBlank(query.getKeyword()), wrapper->{
            wrapper.like(WechatUser::getNickname, query.getKeyword());
            wrapper.or();
            wrapper.like(WechatUser::getTel, query.getKeyword());
        });
        IPage<WechatUserVO> page = baseMapper.selectVoPage(query.build(), lqw);
        return TableDataInfo.build(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean update(WechatUserBO user) {
        WechatUser wechatUser = new WechatUser();
        wechatUser.setUserId(user.getUserId());
        wechatUser.setNickname(user.getNickname());
        wechatUser.setAvatar(user.getAvatar());
        wechatUser.setSex(user.getSex());
        updateById(wechatUser);

        Long roleId = user.getRoleId();
        Long userId = user.getUserId();
        LambdaQueryWrapper<WechatRole> roleLqw = Wrappers.lambdaQuery();
        roleLqw.eq(WechatRole::getId, roleId);
        WechatRole role = wechatRoleService.getOne(roleLqw);
        if(ObjectUtil.isNull(role)){
            throw new ServiceException("角色不存在");
        }
        wechatUserRoleService.createRole(userId, role.getCode());
        user.setUserId(userId);
        user.setRoleCode(role.getCode());
        SpringUtils.context().publishEvent(new WechatUserEvent<>(this, user));
        return true;
    }

    @Override
    public TableDataInfo<WechatUserVO> getLastList(WechatUserQuery query) {
        //最近访问模块
        QueryWrapper<EventTracking> qw = Wrappers.query();
        qw.select("DISTINCT user_id");
        LambdaQueryWrapper<EventTracking> lqw = qw.lambda();
        lqw.orderByDesc(EventTracking::getCreateTime);
        Page<EventTracking> page = eventTrackingMapper.selectPage(query.build(), lqw);
        List<EventTracking> records = page.getRecords();
        //转换用户对象
        List<Long> userIds = records.parallelStream().map(EventTracking::getUserId).toList();
        QueryWrapper<EventTracking> newQw  = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(userIds)){
            newQw.in("user_id",userIds);
        }
        newQw.orderByDesc("create_time");
        newQw.groupBy("user_id");
        List<EventTracking> newRecords = eventTrackingMapper.selectList(newQw);
        Map<Long, WechatUserVO> map = queryMapByIds(userIds);
        List<WechatUserVO> result = newRecords.parallelStream().map(tracking -> {
            Long userId = tracking.getUserId();
            WechatUserVO orDefault = map.getOrDefault(userId, new WechatUserVO());
            WechatUserVO user = new WechatUserVO();
            user.setUserId(userId);
            user.setAvatar(orDefault.getAvatar());
            user.setNickname(orDefault.getNickname());
            user.setTel(orDefault.getTel());
            user.setLastLoginIp(orDefault.getLastLoginIp());
            user.setLastLoginTime(tracking.getEventTime());
            return user;
        }).toList();
        return TableDataInfo.build(result, page.getTotal());
    }

    @Override
    public TableDataInfo<WechatUserVO> getModuleUserList(WechatUserQuery query) {
        Page<WechatUserVO> page = eventTrackingMapper.selectModuleUserList(query.build(), query);
        return TableDataInfo.build(page);
    }

    @Override
    public List<TenantVO> queryTenantsByOpenId(String openId) {
        return TenantHelper.ignore(() -> baseMapper.queryTenantsByOpenId(openId));
    }

    @Override
    public Boolean statusChange(String status, Long userId) {
        return TenantHelper.ignore(() -> {
            LambdaUpdateWrapper<WechatUser> luw = Wrappers.lambdaUpdate();
            luw.eq(WechatUser::getUserId, userId);
            luw.set(WechatUser::getStatus, status);
            return update(luw);
        });
    }

    @Override
    public Boolean sendMessage(MessageNotificationBO messageNotificationBO) {
        String tenantId = LoginHelper.getTenantId();
        LambdaQueryWrapper<WechatUser> lqw = Wrappers.lambdaQuery();
        List<String> excludedList = messageNotificationBO.getExcludedList();
        if (CollUtil.contains(excludedList, "排除工作人员")) {
            //排除工作人员
            lqw.notExists("select 1 from biz_staff where biz_staff.user_id = biz_wechat_user.user_id");
        }
        if (CollUtil.contains(excludedList, "排除老板")) {
            //排除老板
            lqw.notExists("select 1 from biz_clubs where biz_clubs.boss_tel = biz_wechat_user.tel");
        }
        lqw.isNotNull(WechatUser::getOpenid);
        lqw.eq(WechatUser::getUtype, "1");
        List<WechatUser> users = list(lqw);
        List<String> openIds = users.parallelStream().map(WechatUser::getOpenid).toList();
        if (CollectionUtils.isEmpty(openIds)) {
            return true;
        }
        Map<String, String> notificationData = messageNotificationBO.getNotificationData();
        List<WxMaSubscribeMessage> subscribeMessageList = openIds.parallelStream().distinct()
            .map(openId -> {
                WxMaSubscribeMessage subscribeMessage = new WxMaSubscribeMessage();
                subscribeMessage.setTemplateId(messageNotificationBO.getTemplate());
                subscribeMessage.setToUser(openId);
                subscribeMessage.setPage(StrUtil.format("packageA/pages/index/index?tenantId={}", tenantId));
                List<WxMaSubscribeMessage.MsgData> msgData = new ArrayList<>();
                notificationData.forEach((k, v) -> msgData.add(new WxMaSubscribeMessage.MsgData(k, v)));
                subscribeMessage.setData(msgData);
                return subscribeMessage;
            }).toList();

        WechatNotifications wechatNotifications = new WechatNotifications();
        wechatNotifications.setContent(getNotificationContent(messageNotificationBO));
        wechatNotifications.setSendTime(DateUtil.date());
        wechatNotifications.setSendStatus("pending");
        wechatNotifications.setRecipientCount(subscribeMessageList.size());
        wechatNotifications.setSenderCount(0);
        wechatNotifications.setType("1");
        wechatNotifications.setTemplateKey(messageNotificationBO.getTemplate());
        wechatNotifications.setCreateTime(DateUtil.date());
        wechatNotificationsService.save(wechatNotifications);
        CompletableFuture<Long> future = CompletableFuture.supplyAsync(() -> {
            TenantMiniProgram tenantMiniProgram = tenantMiniProgramService.getTenantProgram(tenantId);
            if (ObjectUtil.isNull(tenantMiniProgram)) {
                throw new ServiceException("小程序商户配置不存在");
            }
            Long miniProgramId = tenantMiniProgram.getMiniProgramId();
            WechatMiniProgram miniProgram = wechatMiniProgramService.getById(miniProgramId);
            if (ObjectUtil.isNull(miniProgram)) {
                throw new ServiceException("微信小程序不存在");
            }
            if (!wxMaService.switchover(miniProgram.getAppid())) {
                throw new ServiceException(String.format("未找到对应appid=[%s]的配置，请核实！", miniProgram.getAppid()));
            }
            log.info("消息推送任务开始执行");
            for (WxMaSubscribeMessage message : subscribeMessageList) {
                log.info("消息推送任务执行中");
                try {
                    wxMaService.getMsgService().sendSubscribeMsg(message);
                    wechatNotifications.setSenderCount(wechatNotifications.getSenderCount() + 1);
                    wechatNotificationsService.updateById(wechatNotifications);
                } catch (Exception e) {
                    log.error("消息推送任务执行失败", e);
                }
            }
            log.info("消息推送任务执行完成");
            return wechatNotifications.getId();
        });
        future.thenAccept((result)->{
            LambdaUpdateWrapper<WechatNotifications> luw = Wrappers.lambdaUpdate();
            luw.eq(WechatNotifications::getId, result);
            luw.set(WechatNotifications::getSendStatus, "success");
            wechatNotificationsService.update(luw);
        });
        return true;
    }


    private String getNotificationContent(MessageNotificationBO messageNotificationBO) {
        if ("IOn9fTPeoURr_o8AfB70jHQcbWr8SAn3MI1v_thi-dY".equals(messageNotificationBO.getTemplate())) {
            Map<String, String> notificationData = messageNotificationBO.getNotificationData();
            String name = notificationData.get("thing1");
            String date = notificationData.get("time2");
            String content = notificationData.get("thing5");
            return StrUtil.format("{}--{}", name, content);
        }
        return null;
    }

    private List<String> getOpenIds(MessageNotificationBO messageNotificationBO, String utype){
        LambdaQueryWrapper<WechatUser> lqw = Wrappers.lambdaQuery();
        List<String> excludedList = messageNotificationBO.getExcludedList();
        if (CollUtil.contains(excludedList, "排除工作人员")) {
            //排除工作人员
            lqw.notExists("select 1 from biz_staff where biz_staff.user_id = biz_wechat_user.user_id");
        }
        if (CollUtil.contains(excludedList, "排除老板")) {
            //排除老板
            lqw.notExists("select 1 from biz_clubs where biz_clubs.boss_tel = biz_wechat_user.tel");
        }
        lqw.isNotNull(WechatUser::getOpenid);
        lqw.eq(WechatUser::getUtype, utype);
        List<WechatUser> users = list(lqw);
        return users.parallelStream().map(WechatUser::getOpenid).toList();
    }

    @Override
    public Boolean sendMpMessage(MessageNotificationBO messageNotificationBO){
        String tenantId = LoginHelper.getTenantId();
        TenantMiniProgram tenantMiniProgram = tenantMiniProgramService.getTenantProgram(tenantId);
        if (ObjectUtil.isNull(tenantMiniProgram)) {
            throw new ServiceException("小程序商户配置不存在");
        }
        Long miniProgramId = tenantMiniProgram.getMiniProgramId();
        WechatMiniProgram miniProgram = wechatMiniProgramService.getById(miniProgramId);
        if (ObjectUtil.isNull(miniProgram)) {
            throw new ServiceException("微信小程序不存在");
        }
        List<String> openIds = getOpenIds(messageNotificationBO, "2");
        List<WxMpSubscribeMessage> messages = openIds.parallelStream().map(openId -> {
            WxMpSubscribeMessage wxMpSubscribeMessage = new WxMpSubscribeMessage();
            wxMpSubscribeMessage.setToUser(openId);
            wxMpSubscribeMessage.setTemplateId(messageNotificationBO.getTemplate());
            WxMpSubscribeMessage.MiniProgram mp = new WxMpSubscribeMessage.MiniProgram();
            mp.setAppid(miniProgram.getAppid());
            mp.setPagePath(StrUtil.format("/packageA/pages/index/index?tenantId={}", tenantId));
            wxMpSubscribeMessage.setMiniProgram(mp);
            wxMpSubscribeMessage.setDataMap(messageNotificationBO.getNotificationData());
            String scene = StringUtils.format("tenantId-{}", tenantId);
            wxMpSubscribeMessage.setScene(scene);
            return wxMpSubscribeMessage;
        }).toList();

        wxMpService.switchover("wx46aec69901793393");

        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            log.info("公众号订阅消息推送任务开始执行");
            for (WxMpSubscribeMessage message : messages) {
                log.info("公众号订阅消息推送任务执行中");
                try {
                    wxMpService.getSubscribeMsgService().send(message);
                } catch (Exception e) {
                    log.error("公众号订阅消息推送任务执行失败", e);
                }
            }
            log.info("公众号订阅消息推送任务执行完成");
        });
        return true;
    }

    @Override
    public TableDataInfo<WechatUserVO> querySalesPage(WechatUserQuery query){
        Page<WechatUserVO> page = baseMapper.querySalesPage(query.build(), Wrappers.lambdaQuery());
        return TableDataInfo.build(page);
    }

    @Override
    public List<TenantVO> getTenantVo(){
        String tenantId = LoginHelper.getTenantId();
        Long userId = LoginHelper.getUserId();
        WechatUserVO user = getByUserId(userId);
        // 最近访问的其他商家
        List<TenantVO> tenants = queryTenantsByOpenId(user.getOpenid());
        tenants = tenants.stream()
            .filter(tenant -> !tenant.getTenantId().equals(tenantId))
            .sorted(Comparator.comparing(TenantVO::getLastLoginTime).reversed()).toList();
        return tenants;
    }

    @Override
    public List<WechatUserKefuVO> getCustomerList(String tenantId) {
        List<WechatUserVO> wechatUserVOS =
            baseMapper.selectVoList(Wrappers.<WechatUser>lambdaQuery().isNotNull(WechatUser::getOpenid));

        List<WechatUserKefuVO> voList =
            wechatUserVOS.stream().map(v-> new WechatUserKefuVO(v.getUserId(),v.getNickname(),v.getTel(),v.getTenantId(),v.getOpenid(),v.getAvatar())).toList();

        return voList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(WechatUserBO wechatUser){
        String tel = wechatUser.getTel();
        if(StrUtil.isNotBlank(tel)){
            LambdaQueryWrapper<WechatUser> userLqw = Wrappers.lambdaQuery();
            userLqw.eq(WechatUser::getTel, tel);
            long count = count(userLqw);
            if(count > 0L){
                throw new ServiceException("手机号已经存在！");
            }
        }

        WechatUser user = new WechatUser();
        user.setNickname(wechatUser.getNickname());
        user.setAvatar(wechatUser.getAvatar());
        user.setSex(wechatUser.getSex());
        user.setUtype("3");
        user.setStatus(UserStatus.OK.getCode());
        user.setTel(wechatUser.getTel());
        save(user);

        Long userId = user.getUserId();
        Long roleId = wechatUser.getRoleId();
        LambdaQueryWrapper<WechatRole> roleLqw = Wrappers.lambdaQuery();
        roleLqw.eq(WechatRole::getId, roleId);
        WechatRole role = wechatRoleService.getOne(roleLqw);
        if(ObjectUtil.isNull(role)){
            throw new ServiceException("角色不存在");
        }

        wechatUserRoleService.createRole(userId, role.getCode());

        wechatUser.setUserId(userId);
        wechatUser.setRoleCode(role.getCode());
        SpringUtils.context().publishEvent(new WechatUserEvent<>(this, wechatUser));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void remove(@NotNull Long userId){
        LambdaQueryWrapper<WechatUser> wechatUserLqw = Wrappers.lambdaQuery();
        wechatUserLqw.eq(WechatUser::getUserId, userId);

        WechatUser wechatUser = getOne(wechatUserLqw);
        if(ObjectUtil.isNull(wechatUser)){
            throw new ServiceException("用户不存在");
        }

        String utype = wechatUser.getUtype();
        if(!"3".equals(utype)){
            throw new ServiceException("只能删除后台创建的用户");
        }

        remove(wechatUserLqw);

        LambdaQueryWrapper<WechatUserRole> wechatUserRoleLqw = Wrappers.lambdaQuery();
        wechatUserRoleLqw.eq(WechatUserRole::getUserId, userId);
        wechatUserRoleService.remove(wechatUserRoleLqw);

        WechatUserRemoveEvent wechatUserRemoveEvent = new WechatUserRemoveEvent();
        wechatUserRemoveEvent.setUserId(userId);
        SpringUtils.context().publishEvent(new WechatUserEvent<>(this, wechatUserRemoveEvent));
    }

    @Override
    public Boolean updateProfile(WechatUserProfile profile){

        if (StringUtils.isNotBlank(profile.getNickname())) {
            WxMaMsgSecCheckCheckResponse.ResultBean messageCheck = wechatService
                    .messageCheck(profile.getNickname(), UgcSceneEnum.DATA.getScene());
            String suggest = messageCheck.getSuggest();
            if (!UgcSuggestEnum.PASS.getSuggest().equals(suggest)) {
                profile.setNickname("违规昵称");
            }
        }

        String oldAvatar = profile.getAvatar();
        if (StringUtils.isNotBlank(oldAvatar)) {
//            profile.setAvatar(UgcConstants.IMG_REVIEW);
            wechatService.imageCheckForData(oldAvatar, UgcBusinessType.USER_IMG.getType(), profile.getUserId());
        }

        LambdaUpdateWrapper<WechatUser> luw = Wrappers.lambdaUpdate();
        luw.eq(WechatUser::getUserId, profile.getUserId());
        luw.set(WechatUser::getNickname, profile.getNickname());
        luw.set(WechatUser::getAvatar, profile.getAvatar());
        luw.set(WechatUser::getBirthDate, profile.getBirthDate());
        luw.set(WechatUser::getSex, profile.getSex());
        luw.set(WechatUser::getAge,profile.getAge());
        return update(luw);
    }

    @Override
    public List<WechatUserVO> selectAllMom() {
        return baseMapper.selectAllMom();
    }

    @Override
    public Calendar getCalendar(String datePart, int currentYear, String timePart) throws ParseException {
        SimpleDateFormat dateFormat = new SimpleDateFormat("MM-dd");
        Date date = dateFormat.parse(datePart);
        Calendar dateCalendar = Calendar.getInstance();
        dateCalendar.setTime(date);
        dateCalendar.set(Calendar.YEAR, currentYear);
        SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm");
        Date time = timeFormat.parse(timePart);
        Calendar timeCalendar = Calendar.getInstance();
        timeCalendar.setTime(time);
        dateCalendar.set(Calendar.HOUR_OF_DAY, timeCalendar.get(Calendar.HOUR_OF_DAY));
        dateCalendar.set(Calendar.MINUTE, timeCalendar.get(Calendar.MINUTE));
//        dateCalendar.set(Calendar.SECOND, timeCalendar.get(Calendar.SECOND));
        return dateCalendar;
    }
}




