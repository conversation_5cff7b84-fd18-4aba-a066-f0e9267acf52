package org.dromara.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.CommunityBookmarks;

import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【biz_community_collections(社区动态收藏表)】的数据库操作Service
* @createDate 2024-03-25 15:43:35
*/
public interface CommunityBookmarksService extends IService<CommunityBookmarks> {

    Boolean isCollectionByUserId(Long userId, Long postId);

    Map<Long, CommunityBookmarks> mapByUserId(Long userId);
}
