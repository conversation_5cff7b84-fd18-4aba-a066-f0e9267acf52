package org.dromara.biz.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.dromara.biz.domain.Suites;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 月子套房
 */
@Getter
@Setter
@AutoMapper(target = Suites.class)
public class SuiteVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    private Long suiteId;

    /**
     * 房间名称
     */
    @NotBlank(message = "房间名称不能为空")
    private String roomName;

    /**
     * 房型
     */
    @NotBlank(message = "房型不能为空")
    private String roomType;

    /**
     * 朝向
     */
    @NotBlank(message = "房间朝向不能为空")
    private String orientation;

    /**
     * 床型
     */
    @NotBlank(message = "房间床型不能为空")
    private String bedType;

    /**
     * 最低楼层
     */
    @NotNull(message = "最低楼层不能为空")
    private Integer minFloor;

    /**
     * 最高楼层
     */
    @NotNull(message = "最高楼层不能为空")
    private Integer maxFloor;

    /**
     * 最小面积
     */
    @NotNull(message = "最小面积不能为空")
    private String minArea;

    /**
     * 最大面积
     */
    @NotNull(message = "最大面积不能为空")
    private String maxArea;

    /**
     * 打扫频次
     */
    @NotBlank(message = "打扫频次不能为空")
    private String cleaningFrequency;

    /**
     * 换床单频次
     */
    @NotBlank(message = "换床单频次不能为空")
    private String sheetChangeFrequency;

    /**
     * 消毒频次
     */
    @NotBlank(message = "消毒频次不能为空")
    private String disinfectionFrequency;

    /**
     * 室外景观
     */
    @NotEmpty(message = "室外景观不能为空")
    private List<String> outdoorFeatures = new ArrayList<>();

    /**
     * 便利设施
     */
    @NotEmpty(message = "便利设施不能为空")
    private List<String> facilityFeatures= new ArrayList<>();

    /**
     * 媒体娱乐
     */
    @NotEmpty(message = "媒体娱乐不能为空")
    private List<String> mediaFeatures= new ArrayList<>();

    /**
     * 卫浴配套
     */
    @NotEmpty(message = "卫浴配套不能为空")
    private List<String> bathroomFacilities= new ArrayList<>();

    /**
     * 套房照片列表
     */
    @NotEmpty(message = "套房照片列表不能为空")
    private List<String> suitePhotos= new ArrayList<>();

    /**
     * 套房视频列表
     */
    private List<String> suiteVideos= new ArrayList<>();

    /**
     * 在线状态
     */
    private Boolean onlineStatus;

    /**
     * 套房描述
     */
    private String description;

    /**
     * 房型标签
     */
    private List<String> tag= new ArrayList<>();

    /**
     * 展示状态
     */
    private Boolean isShow;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 签约礼品id
     */
    private Long contractGiftId;

    /**
     * 签约礼品
     */
    private ContractGiftVO contractGift;

    /**
     * 是否领取签约礼品
     */
    private Boolean isExistContractGift;

    /**
     * 图文详情
     */
    private List<String> textPhotos= new ArrayList<>();

    private Date createTime;

    private Date updateTime;

    /**
     * 是否推荐房型
     */
    private Boolean isRecommended;
}
