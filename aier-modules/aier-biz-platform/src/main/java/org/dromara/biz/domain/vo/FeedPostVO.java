package org.dromara.biz.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.biz.domain.FeedPost;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 动态vo
 */
@Data
@AutoMapper(target = FeedPost.class)
public class FeedPostVO implements Serializable {

    /**
     * 动态id
     */
    private Long postId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 入住天数
     */
    private Long liveDays = 0L;

    /**
     * 服务天数
     */
    private Long serviceDays = 0L;

    /**
     * 员工服务的客户信息
     */
    private WechatUserVO customer;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 员工职位
     */
    private String staffPost;

    /**
     * 动态内容
     */
    private String content;

    /**
     * 动态图片
     */
    private List<String> contentPhotos;

    /**
     * 视频链接
     */
    private List<String> videos;

    /**
     * 时间 xx小时前 or xx分钟前
     */
    private String hisTimeStr;

    /**
     * 会所名称
     */
    private String clubName = "";

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 评论列表
     */
    private List<FeedPostCommentVO> comments;

    /**
     * 点赞列表
     */
    private List<LikeUsers> likeUsers;

    /**
     * 当前用户是否对该动态点赞
     */
    private Boolean isLike;

    /**
     * 当前用户是否对该动态收藏
     */
    private Boolean isFavorite;

    /**
     * 点赞数
     */
    private Integer likesCount;

    /**
     * 评论数
     */
    private Integer commentsCount;

    /**
     * 分享数
     */
    private Integer shareCount;

    /**
     * 收藏数
     */
    private Integer favoriteCount;

    /**
     * 类型 区分员工、客户、会所
     */
    private String type;

    /**
     * 是否是精选动态
     */
    private Boolean isFeatured;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 动态的员工信息
     */
    private EmployeeInfo staffInfo;

    /**
     * 动态的房间信息
     */
    private RoomInfo roomInfo;

    /**
     * 动态的标签信息
     */
    private PostTagInfo postTagInfo;

    /**
     * 动态的客户信息
     */
    private CustomerInfo customerInfo;

    /**
     * 发布动态的用户信息
     */
    private UserInfo userInfo;

    /**
     * 关联宝妈笔记id
     */
    private String momId;

    /**
     * 宝宝体重
     */
    private String babyWeight;

    /**
     * 年龄
     */
    private String age;
    /**
     * 标题
     */
    private String title;

    private Long taskNodeId;
    private Long roomId;
    private Long customerId;
    private Long customerUserId;
    private Long targetUserId;
    private Long targetCustomerId;

    /**
     * 用户点赞列表
     */
    @Data
    public final static class LikeUsers {

        /**
         * 动态id
         */
        private Long postId;
        /**
         * 用户id
         */
        private Long userId;
        /**
         * 昵称
         */
        private String nickname;
        /**
         * 头像
         */
        private String avatar;

        /**
         * 点赞时间
         */
        private Date linkTime;
    }

    /**
     * 员工信息
     */
    @Data
    public final static class EmployeeInfo {

        private Long staffId;

        /**
         * 员工岗位
         */
        private String staffPost;
        /**
         * 员工标签 只有动态类型为 STAFF 时有值
         */
        private List<String> tags;

        /**
         * 从业时间
         */
        private Date practiceTime;

        /**
         * 从业年限
         */
        private String yearsEmployment = "未知";

        /**
         * 服务人员数量
         */
        private Integer serviceNum;

        /**
         * 服务开始时间
         */
        private Date serviceTime;

        /**
         * 服务天数
         */
        private Long serviceDays = 0L;
    }

    /**
     * 房间信息
     */
    @Data
    public final static class RoomInfo {

        /**
         * 房间id
         */
        private Long roomId;

        /**
         * 房间号
         */
        private String roomNumber;
    }

    /**
     * 标签信息
     */
    @Data
    public final static class PostTagInfo {

        /**
         * 标签id
         */
        private Long tagId;

        /**
         * 标签名称
         */
        private String tagName;
    }

    /**
     * 客户信息
     */
    @Data
    public final static class CustomerInfo {

        /**
         * 客户id
         */
        private Long customerId;

        /**
         * 客户的用户id
         */
        private Long userId;

        /**
         * 客户名称
         */
        private String customerName;

        /**
         * 入住天数
         */
        private Long checkInDays = 0L;

        /**
         * 入住时间
         */
        private Date checkIn;

        /**
         * 离开时间
         */
        private Date checkOut;
    }

    /**
     * 发布动态的用户信息
     */
    @Data
    public final static class UserInfo {

        /**
         * 用户id
         */
        private Long userId;

        /**
         * 昵称
         */
        private String nickname;

        /**
         * 头像
         */
        private String avatar;

        /**
         * 角色编码
         */
        private String roleCode;
    }
}
