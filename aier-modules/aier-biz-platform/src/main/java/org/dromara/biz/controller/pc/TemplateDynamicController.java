package org.dromara.biz.controller.pc;


import lombok.RequiredArgsConstructor;
import org.dromara.biz.domain.BizTemplateDynamic;
import org.dromara.biz.domain.query.TemplateQuery;
import org.dromara.biz.service.IBizTemplateDynamicService;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.stereotype.Controller;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 动态模板表 相关接口
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@Controller
@RequestMapping("/platform/templateDynamic")
public class TemplateDynamicController {
    private final IBizTemplateDynamicService bizTemplateDynamicService;

    /**
     * 分页模板节点列表
     */
    @GetMapping("/page")
    public TableDataInfo<BizTemplateDynamic> queryPage(TemplateQuery query) {
        return bizTemplateDynamicService.queryPage(query);
    }

    /**
     * 新增修改动态模板
     * @param templateDynamic 动态样式
     * @return 新增结果
     */
    @PostMapping("/save")
    @RepeatSubmit
    public R<Boolean> insert(@RequestBody @Validated BizTemplateDynamic templateDynamic) {
        return R.ok(bizTemplateDynamicService.saveB(templateDynamic));
    }

    /**
     * 删除动态模板
     * @param id id
     * @return 删除结果
     */
    @GetMapping("/remove")
    public R<Boolean> delete(@RequestParam("id") Long id){
        return R.ok(bizTemplateDynamicService.removeById(id));
    }

    /**
     * 查询详情
     * @param id id
     * @return 结果
     */
    @GetMapping("/info")
    public R<BizTemplateDynamic> infoMp(@RequestParam("id") Long id){
        return R.ok( bizTemplateDynamicService.getById(id));
    }

    /**
     * 通过标签名称查询模板
     * @param labelName
     * @return
     */
    @GetMapping("/listByLabel")
    public R<List<BizTemplateDynamic>> listByLabel(@RequestParam String labelName,
                                                   @RequestParam(name = "contentStyle", required = false) String contentStyle){
            return R.ok(bizTemplateDynamicService.listByLabel(labelName,contentStyle));
    }

    /**
     * 通过节点id查询动态模版
     */
    @GetMapping("/getTemplateByNodeId")
    public R<List<BizTemplateDynamic>> getTemplateByNodeId(@RequestParam Long nodeId){
        return R.ok(bizTemplateDynamicService.getTemplateByNodeId(nodeId));
    }


}
