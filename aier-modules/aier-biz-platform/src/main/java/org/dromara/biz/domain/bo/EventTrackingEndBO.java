package org.dromara.biz.domain.bo;

import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 小程序事件追踪结束bo
 */
@Data
public class EventTrackingEndBO {

    /**
     * 事件id
     */
    @NotNull(message = "埋点事件id不能为空")
    private Long eventId;

    /**
     * 结束时间戳
     */
    @NotNull(message = "结束时间戳不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date leaveTime;
}
