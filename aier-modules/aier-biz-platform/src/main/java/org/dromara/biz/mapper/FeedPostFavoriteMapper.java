package org.dromara.biz.mapper;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Param;
import org.dromara.biz.domain.CustomerContract;
import org.dromara.biz.domain.FeedPostFavorite;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.dromara.biz.domain.vo.AnalysisVo;
import org.dromara.biz.domain.vo.FeedPostVO;
import org.dromara.biz.domain.vo.TrajectoryVo;
import org.dromara.biz.domain.vo.customer.CustomerContractVO;
import org.springframework.data.domain.Page;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_feed_post_favorite(朋友圈动态收藏表)】的数据库操作Mapper
* @createDate 2024-04-08 14:03:24
* @Entity org.dromara.biz.domain.FeedPostFavorite
*/
public interface FeedPostFavoriteMapper extends BaseMapper<FeedPostFavorite> {
    /**
     * 查询用户收藏动态及话题
     * @param userId
     * @return
     */
    List<AnalysisVo> queryFavorite(Long userId);

    /**
     * 查询用户点赞动态及话题
     * @param userId
     * @return
     */
    List<AnalysisVo> queryLike(Long userId);

    /**
     * 查询用户评论动态及话题
     * @param userId
     * @return
     */
    List<AnalysisVo> queryComment(Long userId);

    /**
     * 查询用户转发动态及话题
     * @param userId
     * @return
     */
    List<AnalysisVo> queryForward(Long userId);

    /**
     * 客户轨迹-累计访问
     * @param userId
     * @return
     */
    List<TrajectoryVo> queryAccumulatedVisits(Long userId);

    /**
     * 客户轨迹-门店-访问最多
     * @param userId
     * @return
     */
    List<TrajectoryVo> queryVisitsNumber(Long userId);

    /**
     * 客户轨迹-门店-时间最长
     * @param userId
     * @return
     */
    List<TrajectoryVo> queryVisitsTime(Long userId);

    /**
     * 客户轨迹-门店-最后访问
     * @param userId
     * @return
     */
    List<TrajectoryVo> queryVisitsLast(Long userId);

    /**
     * 客户轨迹-访问详情
     */
    List<TrajectoryVo> queryVisitsDetail(Long userId);



    /**
     * 客户轨迹-饼图-次数
     * @param userId
     * @return
     */
    List<TrajectoryVo> queryVisitsRateNumber(Long userId);

    /**
     * 客户轨迹-饼图-时间
     * @param userId
     * @return
     */
    List<TrajectoryVo> queryVisitsRateTime(Long userId);


    /**
     * 客户轨迹-社区-访问最多
     * @param userId
     * @return
     */
    List<TrajectoryVo> queryVisitsNumberDynamics(Long userId);

    /**
     * 客户轨迹-社区-时间最长
     * @param userId
     * @return
     */
    List<TrajectoryVo> queryVisitsTimeDynamics(Long userId);

    /**
     * 客户轨迹-社区-最后访问
     * @param userId
     * @return
     */
    List<TrajectoryVo> queryVisitsLastDynamics(Long userId);

    /**
     * 最后访问动态
     * @param userId
     * @return
     */
    List<TrajectoryVo> queryVisitsDetailDynamicsLast(Long userId);
}




