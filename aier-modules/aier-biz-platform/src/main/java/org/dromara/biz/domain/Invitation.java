package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 请柬详情表
 * @TableName biz_invitation_details
 */
@TableName(value ="biz_invitation")
@Data
public class Invitation implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 请柬详情
     */
    private String detail;

    /**
     * openid
     */
    private String openid;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 请柬id
     */
    private Long templateId;

    /**
     * 地址
     */
    private Long address;

    /**
     * 是否为草稿 0:否 1：是
     */
    private Integer isDraft;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}