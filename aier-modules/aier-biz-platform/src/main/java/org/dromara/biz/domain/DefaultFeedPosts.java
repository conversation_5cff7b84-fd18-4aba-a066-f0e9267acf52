package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import org.dromara.common.mybatis.handler.type.StringListTypeHandler;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 默认动态表
 * @TableName biz_default_feed_posts
 */
@TableName(value ="biz_default_feed_posts", autoResultMap = true)
@Data
public class DefaultFeedPosts implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 动态内容
     */
    private String content;

    /**
     * 动态图片
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> contentPhotos;

    /**
     * 动态视频
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> videos;

    /**
     * 动态类型
     */
    private String type;

    /**
     * 是否绑定节点
     */
    private Boolean isNode;

    /**
     * 默认节点id
     */
    private Long defaultNodeId;

    /**
     * 默认客户id
     */
    private Long defaultCustomerId;

    /**
     * 发布者id
     */
    private Long authorId;

    /**
     * 发布者名称
     */
    @TableField(exist = false)
    private String authorName;

    /**
     * 发布者头像
     */
    @TableField(exist = false)
    private String authorAvatar;

    /**
     * 节点名称
     */
    @TableField(exist = false)
    private String nodeName;

    /**
     * 发布时间
     */
    private Date authorTime;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}