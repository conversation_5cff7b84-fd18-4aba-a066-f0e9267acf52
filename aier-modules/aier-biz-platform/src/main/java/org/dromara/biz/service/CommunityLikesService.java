package org.dromara.biz.service;

import org.dromara.biz.domain.CommunityLikes;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【biz_community_likes(社区动态点赞表)】的数据库操作Service
* @createDate 2024-03-25 15:43:35
*/
public interface CommunityLikesService extends IService<CommunityLikes> {

    Boolean isLike(Long userId, Long postId);

    Integer getLikeNum(Long postId);

    Map<Long, CommunityLikes> mapByUserId(Long userId);
}
