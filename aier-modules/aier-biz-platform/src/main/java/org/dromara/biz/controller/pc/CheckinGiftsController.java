package org.dromara.biz.controller.pc;

import lombok.RequiredArgsConstructor;
import org.dromara.biz.domain.query.CheckinGiftsQuery;
import org.dromara.biz.domain.vo.CheckinGiftVO;
import org.dromara.biz.service.CheckinGiftsService;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 签到礼品相关接口
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/checkin_gifts")
public class CheckinGiftsController {

    private final CheckinGiftsService checkinGiftsService;

    /**
     * 分页查询礼品列表
     */
    @GetMapping("/page")
    public TableDataInfo<CheckinGiftVO> list(CheckinGiftsQuery query) {
        return checkinGiftsService.queryPage(query);
    }

    /**
     * 查询礼品信息
     */
    @GetMapping("/info/{giftId}")
    public R<CheckinGiftVO> info(@PathVariable Long giftId) {
        return R.ok(checkinGiftsService.getByGiftId(giftId));
    }

    /**
     * 新增礼品
     * @param vo 礼品信息
     * @return 新增结果
     */
    @PutMapping("/save")
    @RepeatSubmit
    public R<Boolean> save(@RequestBody @Validated CheckinGiftVO vo) {
        return R.ok(checkinGiftsService.save(vo));
    }

    /**
     * 修改礼品
     * @param vo 礼品信息
     * @return 修改结果
     */
    @PostMapping("/update")
    @RepeatSubmit
    public R<Boolean> update(@RequestBody @Validated CheckinGiftVO vo) {
        return R.ok(checkinGiftsService.update(vo));
    }

    /**
     * 改变礼品上下线状态
     * @return 结果
     */
    @PostMapping("/change/{status}/{giftId}")
    public R<Boolean> changeOnlineStatus(@PathVariable Long giftId, @PathVariable Boolean status) {
        return R.ok(checkinGiftsService.changeOnlineStatus(giftId, status));
    }

    /**
     * 删除礼品
     * @param giftId 礼品id
     * @return 结果
     */
    @DeleteMapping("/{giftId}")
    public R<Boolean> remove(@PathVariable Long giftId){
        return R.ok(checkinGiftsService.removeById(giftId));
    }
}
