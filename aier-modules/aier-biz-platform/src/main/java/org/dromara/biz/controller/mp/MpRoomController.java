package org.dromara.biz.controller.mp;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.RoomLevels;
import org.dromara.biz.domain.Rooms;
import org.dromara.biz.domain.bo.RoomBO;
import org.dromara.biz.domain.bo.RoomTodoBO;
import org.dromara.biz.domain.query.RoomQuery;
import org.dromara.biz.domain.query.RoomTodoQuery;
import org.dromara.biz.domain.query.room.RoomStatusQuery;
import org.dromara.biz.domain.vo.*;
import org.dromara.biz.domain.vo.room.RoomOccupationDateRangeVO;
import org.dromara.biz.domain.vo.room.RoomStatusVO;
import org.dromara.biz.service.RoomLevelsService;
import org.dromara.biz.service.RoomsService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.enums.RoomCheckinStatus;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 小程序房间相关接口
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping("/mp/room")
@Validated
public class MpRoomController {

    private final RoomsService roomsService;
    private final RoomLevelsService roomLevelsService;

    /**
     * 查询房间列表
     */
    @GetMapping("/list")
    public R<List<RoomVO>> list(RoomQuery query) {
        List<RoomLevelVO> roomLevelList = roomsService.queryList(query);
        List<RoomVO> rooms = roomLevelList.stream()
            .filter(roomLevel-> ObjectUtil.isNotNull(roomLevel.getRooms()))
            .flatMap(roomLevel -> roomLevel.getRooms().stream()).toList();
        return R.ok(rooms);
    }

    /**
     * 通过房型id查询套餐列表
     */
    @GetMapping("/suites_list")
    public R<List<RoomVO>> getSuitesList(@RequestParam(name = "suitesId", required = false) Long suitesId) {
        return R.ok(roomsService.getSuitesList(suitesId));
    }

    /**
     * 查询房间入住状态数量
     */
    @GetMapping("/status_number")
    public R<RoomStatusStatsVO> queryRoomStatusStats() {
        return R.ok(roomsService.queryRoomStatusStats());
    }

    /**
     * 查询房间详情
     * @param roomId 房间id
     */
    @GetMapping("/info")
    public R<RoomVO> queryInfoByRoomId(@RequestParam("roomId") Long roomId) {
        return R.ok(roomsService.getRoomInfo(roomId));
    }

    /**
     * 查询房间待办
     * @param query 查询参数
     */
    @GetMapping("/todo")
    public R<List<RoomTodoVO>> queryTodoList(RoomTodoQuery query) {
        return R.ok(roomsService.queryTodoList(query));
    }

    /**
     * 查询房间待办详情
     */
    @GetMapping("/todo/info")
    public R<RoomTodoVO> queryTodoInfo(@RequestParam("feedbackId") Long feedbackId) {
        return R.ok(roomsService.queryTodoInfo(feedbackId));
    }

    /**
     * 获取房间待办状态数量统计
     */
    @GetMapping("/stats")
    public R<BossTodoStatsVO> queryTodoStats() {
        List<RoomTodoVO> roomTodoList = roomsService.queryTodoList(new RoomTodoQuery());
        Map<String, List<RoomTodoVO>> todoMap = roomTodoList.stream()
            .collect(Collectors.groupingBy(RoomTodoVO::getStatus));
        BossTodoStatsVO bossTodoStatsVO = new BossTodoStatsVO();
        //0待处理/1处理中/2已处理
        bossTodoStatsVO.setProcessed(todoMap.getOrDefault("0", List.of()).size());
        bossTodoStatsVO.setProcessedIng(todoMap.getOrDefault("1", List.of()).size());
        bossTodoStatsVO.setProcessedNot(todoMap.getOrDefault("2", List.of()).size());
        return R.ok(bossTodoStatsVO);
    }

    /**
     * 回复投诉
     * @param roomTodo 回复内容
     * @return 结果
     */
    @PostMapping("/reply")
    @RepeatSubmit
    public R<Boolean> reply (@RequestBody RoomTodoBO roomTodo){
        return R.ok(roomsService.reply(roomTodo));
    }

    /**
     * 查询房间入住状态
     */
    @GetMapping("/status")
    public R<List<RoomStatusVO>> getRoomStatus(RoomStatusQuery query) {
        return R.ok(roomsService.getRoomStatus(query));
    }

    /**
     * 查询房间排房数据
     * @param query 查询参数
     */
    @GetMapping("/occupation_range")
    public R<List<RoomOccupationDateRangeVO>> getRoomOccupationsByDateRange(RoomStatusQuery query){
        return R.ok(roomsService.getRoomOccupationsByDateRange(query));
    }

    /**
     * 新增楼层
     * @param levelList 楼层列表
     * @return 结果
     */
    @PutMapping("/add_level")
    @RepeatSubmit
    public R<Boolean> saveLevel(@RequestBody List<LevelVO> levelList){
        return R.ok(roomLevelsService.addLevel(levelList));
    }

    /**
     * 修改楼层
     * @param level 楼层信息
     * @return 结果
     */
    @PutMapping("/update_level")
    @RepeatSubmit
    public R<Boolean> updateLevel(@RequestBody LevelVO level){
        return R.ok(roomLevelsService.updateById(MapstructUtils.convert(level, RoomLevels.class)));
    }

    /**
     * 获取楼层列表
     * @return 楼层列表
     */
    @GetMapping("/list_level")
    public R<List<LevelVO>> getLevelList(){
        return R.ok(roomLevelsService.queryList());
    }

    /**
     * 删除楼层
     * @param levelId 房型id
     * @return 删除结果
     */
    @GetMapping("/remove")
    public R<Boolean> delete(@RequestParam("levelId") Long levelId){
        return R.ok(roomLevelsService.removeById(levelId));
    }


    /**
     * 新增房间
     * @param room 房间信息
     * @return 结果
     */
    @PutMapping("/add_room")
    @RepeatSubmit
    public R<Boolean> saveRoom(@RequestBody @Validated RoomBO room){
        Rooms rooms = MapstructUtils.convert(room, Rooms.class);
        if (rooms != null) {
            rooms.setOccupancyStatus(RoomCheckinStatus.NOT_CHECKED_IN.getCode());
        }
        return R.ok(roomsService.saveMp(rooms));
    }

    /**
     * 查询房间列表
     */
    @GetMapping("/mpList")
    public R<List<RoomBO>> mpList(RoomQuery query) {
        return R.ok(roomsService.queryMpList(query));
    }

    /**
     * 查询房间详情（新）
     * @param roomId 房间id
     */
    @GetMapping("/infoMp")
    public R<RoomBO> queryInfo(@RequestParam("roomId") Long roomId) {
        return R.ok( roomsService.getBoInfo(roomId));
    }

    /**
     * 根据角色获取房间
     */
    @GetMapping("/role_code")
    public R<List<RoomVO>> getByRoleCode(String roleCode) {
        return R.ok(roomsService.getByRoleCode(roleCode));
    }

    /**
     * 查询全部房间列表
     * @param notRoomIds 不查询的房间id集合 逗号分隔
     */
    @GetMapping("/options")
    public R<List<Rooms>> getRoomOptions(String notRoomIds) {
        LambdaQueryWrapper<Rooms> wrapper = new LambdaQueryWrapper<>();
        if(StrUtil.isNotBlank(notRoomIds)){
            wrapper.notIn(Rooms::getRoomId, notRoomIds);
        }
        return R.ok(roomsService.list(wrapper));
    }
}
