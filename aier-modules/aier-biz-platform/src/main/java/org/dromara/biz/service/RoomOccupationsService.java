package org.dromara.biz.service;

import org.dromara.biz.domain.RoomOccupations;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【biz_room_occupations(房间入住表)】的数据库操作Service
* @createDate 2024-09-12 20:42:19
*/
public interface RoomOccupationsService extends IService<RoomOccupations> {
    /**
     * 通过客户id获取入住记录，并且状态为0 （已入住）
     * @param customerId
     * @return
     */
    RoomOccupations getByCustomerId(Long customerId);

    /**
     * 通过房间id获取入住记录，并且状态为0 （已入住）
     * @param roomId
     * @return
     */
    RoomOccupations getByRoomId(Long roomId);
}
