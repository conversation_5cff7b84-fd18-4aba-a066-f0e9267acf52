package org.dromara.biz.domain.vo.customer;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.biz.domain.CustomerContract;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 订单录入vo
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AutoMapper(target = CustomerContract.class, reverseConvertGenerate = false)
public class OrderEntryVo extends CustomerContract {

    /**
     * 签约id biz_customer_contract
     */
     private Integer id;

     /**
     * 客户名称
     */

     @NotNull(message = "客户名称不能为空")
     private String name;

     /**
     * 客户电话
     */
     @NotBlank(message = "手机号不能为空！")
     private String tel;

    /**
     * 妈妈生日
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date momBirthday;


    /**
     * 实收类型 0=交定金;1=交合同金
     */

    @NotNull(message = "请选择实收类型")
    private Integer actualPaymentType;


    /**
     * 实收金额
     */
    @NotNull(message = "实收金额不能为空！")
    private BigDecimal paymentAmount;

    /**
     * 服务开始时间
     */

    @NotNull(message = "服务开始时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date serviceStartDate;

    /**
     * 服务结束时间
     */

    @NotNull(message = "服务结束时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date serviceEndDate;

    /**
     * 签约时间
     */

    @NotNull(message = "签约时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date contractSignDate;

    /**
     * 房间id
     */
    @NotNull(message = "请选择房间！")
    private Long roomId;

    /**
     *跟进人id
     */

//    @NotNull(message = "跟进人不能为空")
    private Long followerId;

    /**
     * 其他费用
     */
    private BigDecimal  otherAmount;

    /**
     * 线索来源
     */
//    @NotNull(message = "请选择线索来源！")
    private String source;

    /**
     * 是否入住
     */

//    @NotNull(message = "是否到店不能为空")
    private Boolean isArrivedAtStore;

    /**
     * 是否到店
     */
    private Boolean isToStore;


    /**
     * 到店次数
     */
    private String arrivedAtStoreNumber;


    /**
     * 到店次数
     */
    private String wechat;

    /**
     * 预产期
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date dueDate;

    /**
     * 套房id
     */
    private Long suiteId;

    /**
     * 套餐名称
     */
    private String mealName;

    /**
     * 套餐金额
     */
    private BigDecimal mealAmount;

    /**
     * 房间号
     */
    private String roomNumber;

    /**
     * 房型名称
     */
    private String suiteName;

    /**
     * 身份证
     */
    private String idCard;



}
