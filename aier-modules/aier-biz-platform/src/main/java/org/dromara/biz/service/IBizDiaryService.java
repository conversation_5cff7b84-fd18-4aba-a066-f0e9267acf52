package org.dromara.biz.service;

import org.dromara.biz.domain.BizDiary;
import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.RemindersDiaryParams;
import org.dromara.biz.domain.bo.diary.DiaryBO;
import org.dromara.biz.domain.query.diary.DiaryQuery;
import org.dromara.biz.domain.vo.DiaryVO;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
 * <p>
 * 宝妈笔记表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-31
 */
public interface IBizDiaryService extends IService<BizDiary> {

    /**
     * 发布笔记
     * @param diaryBO
     * @return
     */
    Boolean publish(DiaryBO diaryBO);

    /**
     * 笔记id获取笔记vo
     * @param diaryId
     * @return
     */
    DiaryVO getDetail(String diaryId);

    /**
     * 分页查询笔记
     * @param query
     * @return
     */
    TableDataInfo<DiaryVO> queryPage(DiaryQuery query);

    /**
     * 修改笔记
     * @param diaryBO
     * @return
     */
    Boolean updateDiary(DiaryBO diaryBO);

    /**
     * 创建笔记推送提醒
     * @param remindersDiaryParams
     * @return
     */
    Boolean createReminder(RemindersDiaryParams remindersDiaryParams);
}
