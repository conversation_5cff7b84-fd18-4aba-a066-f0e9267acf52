package org.dromara.biz.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.dromara.biz.domain.InvitationTemplate;
import org.dromara.biz.domain.query.invitation.InvitationQuery;
import org.dromara.biz.domain.vo.invitation.InvitationTemplateVO;
import org.dromara.biz.mapper.InvitationTemplateMapper;
import org.dromara.biz.service.InvitationCollectService;
import org.dromara.biz.service.InvitationTemplateService;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
* <AUTHOR>
* @description 针对表【biz_invitation_template(请柬模版表)】的数据库操作Service实现
* @createDate 2024-07-30 11:25:40
*/
@Service
@RequiredArgsConstructor
public class InvitationTemplateServiceImpl extends ServiceImpl<InvitationTemplateMapper, InvitationTemplate>
    implements InvitationTemplateService{

    private final InvitationCollectService invitationCollectService;

    @Override
    public TableDataInfo<InvitationTemplateVO> queryPage(InvitationQuery query){
        IPage<InvitationTemplateVO> page = baseMapper.selectVoPage(query.build(), buildQueryWrapper(query));
        return TableDataInfo.build(page);
    }

    @Override
    public InvitationTemplateVO getTemplateDetail(Long id){
        return baseMapper.selectVoById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(InvitationTemplateVO templateVO){
        InvitationTemplate template = MapstructUtils.convert(templateVO, InvitationTemplate.class);
        save(template);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(InvitationTemplateVO templateVO){
        InvitationTemplate template = MapstructUtils.convert(templateVO, InvitationTemplate.class);
        updateById(template);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTemplateStatus(Long id, Boolean status){
        LambdaUpdateWrapper<InvitationTemplate> luw = Wrappers.lambdaUpdate();
        luw.eq(InvitationTemplate::getId, id);
        luw.set(InvitationTemplate::getEnable, status);
        update(luw);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void templateCollect(Long id){
        Long userId = LoginHelper.getUserId();
        if(invitationCollectService.isCollect(userId, id)) return;
        LambdaQueryWrapper<InvitationTemplate> lqw = Wrappers.lambdaQuery();
        lqw.eq(InvitationTemplate::getId, id);
        if(count(lqw) <= 0L) {
            throw new ServiceException("请帖不存在");
        }
        addCollectNum(id);
        invitationCollectService.collect(userId, id);
    }

    @Override
    public TableDataInfo<InvitationTemplateVO> getTemplateCollectPage(PageQuery pageQuery, Long userId){
        Page<InvitationTemplateVO> page = baseMapper.getTemplateCollectPage(pageQuery.build(), userId);
        return TableDataInfo.build(page);
    }

    /**
     * 构造请帖模版查询条件
     */
    private LambdaQueryWrapper<InvitationTemplate> buildQueryWrapper(InvitationQuery query) {
        LambdaQueryWrapper<InvitationTemplate> lqw = Wrappers.lambdaQuery();
        if(ObjectUtil.isNotNull(query.getEnable())){
            lqw.eq(InvitationTemplate::getEnable, query.getEnable());
        }
        if(ObjectUtil.isNotNull(query.getCategory())){
            lqw.eq(InvitationTemplate::getCategory, query.getCategory());
        }
        if(ObjectUtil.isNotNull(query.getInvitationType())){
            lqw.eq(InvitationTemplate::getInvitationType, query.getInvitationType());
        }
        if(ObjectUtil.isNotNull(query.getInvitationName())){
            lqw.like(InvitationTemplate::getInvitationName, query.getInvitationName());
        }
        lqw.orderByDesc(InvitationTemplate::getCreateTime);
        return lqw;
    }

    public void addCollectNum(Long id, Integer num){
        if (id == null || num == null) {
            throw new IllegalArgumentException("ID和数量不能为空");
        }
        LambdaUpdateWrapper<InvitationTemplate> lw = Wrappers.lambdaUpdate();
        lw.eq(InvitationTemplate::getId, id);
        lw.setSql("collect_num = collect_num + {0}", num);
        update(lw);
    }

    public void addCollectNum(Long id){
        addCollectNum(id, 1);
    }

    public void removeCollectNum(Long id, Integer num){
        if (id == null || num == null) {
            throw new IllegalArgumentException("ID和数量不能为空");
        }
        LambdaUpdateWrapper<InvitationTemplate> lw = Wrappers.lambdaUpdate();
        lw.eq(InvitationTemplate::getId, id);
        lw.setSql("collect_num = collect_num - {0}", num);
        update(lw);
    }

    public void removeCollectNum(Long id){
        removeCollectNum(id, 1);
    }
}




