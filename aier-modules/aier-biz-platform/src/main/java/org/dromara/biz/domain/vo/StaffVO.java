package org.dromara.biz.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.dromara.biz.domain.Staff;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 护理人员&员工信息vo
 */
@Data
@AutoMapper(target = Staff.class)
public class StaffVO implements Serializable {

    /**
     * id
     */
    private Long staffId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 员工姓名
     */
    @NotBlank(message = "员工姓名不能为空")
    private String staffName;

    /**
     * 员工手机号
     */
    @NotBlank(message = "员工手机号不能为空")
    private String staffTel;

    /**
     * 员工照片
     */
    @NotEmpty(message = "员工照片不能为空")
    private List<String> staffPhotos = new ArrayList<>();

    /**
     * 员工职位
     */
    @NotEmpty(message = "员工职位不能为空")
    private String staffPost;

    /**
     * 个人简介
     */
    private String staffDesc;

    /**
     * 从业时间
     */
    @NotNull(message = "员工从业时间不能为空")
    private Date practiceTime;

    /**
     * 是否展示
     */
    private Boolean isShow;

    /**
     * 证件类型 0=身份证
     */
//    @NotNull(message = "员工证件类型不能为空")
    private String documentType;

    /**
     * 证件号码
     */
//    @NotNull(message = "员工证件号码不能为空")
    private String documentNumber;

    /**
     * 正面照片
     */
//    @NotEmpty(message = "员工证件正面照片不能为空")
    private List<String> photoFront;

    /**
     * 反面照片
     */
//    @NotEmpty(message = "员工证件反面照片不能为空")
    private List<String> photoBack;

    /**
     * 资质信息
     */
    private Map<String, Object> qualificationObject;

    /**
     * 服务人数
     */
    private Integer serviceNum = 0;

    /**
     * 从业年限
     */
    private String yearsEmployment;

    /**
     * 人员标签
     */
    private List<String> tag;

    /**
     * 会所名称
     */
    @Translation(type = TransConstant.CLUB_NAME)
    private String clubName = "";

    private String tenantId;

    @Serial
    private static final long serialVersionUID = 1L;
}
