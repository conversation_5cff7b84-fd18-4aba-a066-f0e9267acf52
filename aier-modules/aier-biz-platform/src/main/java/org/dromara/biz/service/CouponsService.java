package org.dromara.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.CheckinGifts;
import org.dromara.biz.domain.ContractGifts;
import org.dromara.biz.domain.Coupons;
import org.dromara.biz.domain.vo.CouponVO;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

/**
* <AUTHOR>
* @description 针对表【biz_coupons(优惠券)】的数据库操作Service
* @createDate 2024-05-21 15:47:31
*/
public interface CouponsService extends IService<Coupons> {

    Boolean createCouponsByCheckGifts(CheckinGifts checkinGifts);

    Boolean createCouponsByContractGifts(ContractGifts contractGifts);

    TableDataInfo<CouponVO> queryPage(PageQuery query);
}
