package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.dromara.common.mybatis.handler.type.StringListTypeHandler;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 宝宝洗澡记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("biz_baby_bath")
public class BizBabyBath extends TenantEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 宝宝ID
     */
    private Long babyId;

    /**
     * 记录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date recordTime;

    /**
     * 房间温度（单位：摄氏度）
     */
    private String roomTemperature;

    /**
     * 房间湿度（单位：百分比）
     */
    private String roomHumidity;

    /**
     * 洗澡时长（单位：分钟）
     */
    private String bathDuration;

    /**
     * 体温（单位：摄氏度）
     */
    private String bodyTemperature;

    /**
     * 备注信息
     */
    private String notes;

    /**
     * 照片列表（存储图片路径或URL，JSON格式）
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> photoList;
}
