//package org.dromara.biz.controller.mp;
//
//
//import cn.dev33.satoken.annotation.SaIgnore;
//import cn.hutool.json.JSONUtil;
//import lombok.AllArgsConstructor;
//import org.dromara.biz.domain.BizWechatInviation;
//import org.dromara.biz.domain.vo.WechatInviationVo;
//import org.dromara.biz.service.IBizWechatInviationService;
//import org.dromara.common.core.domain.R;
//import org.dromara.common.core.exception.ServiceException;
//import org.dromara.common.core.utils.MapstructUtils;
//import org.dromara.common.core.utils.StringUtils;
//import org.springframework.web.bind.annotation.*;
//
///**
// * 微信电子请柬表
// * <AUTHOR>
// * @since 2024-09-03
// */
//@AllArgsConstructor
//@RestController
//@RequestMapping("/mp/wechat_inviation")
//@SaIgnore
//public class BizWechatInviationController {
//    private final IBizWechatInviationService wechatInviationService;
//
//    /**
//     * 新增修改请柬
//     * @param wechatInviationVo
//     * @return
//     */
//    @PostMapping("/save")
//    @ResponseBody
//    public R<WechatInviationVo> editInvitationDetails(@RequestBody WechatInviationVo wechatInviationVo){
//        return  R.ok(wechatInviationService.save(wechatInviationVo));
//    }
//
//
//    /**
//     * 获取请柬信息
//     * @param id
//     * @return
//     */
//    @GetMapping("/getInfo")
//    public R<WechatInviationVo> getInfo(@RequestParam("id")String id){
//        BizWechatInviation wechatInviation = wechatInviationService.getById(id);
//        if (wechatInviation!=null){
//            WechatInviationVo wechatInviationVo = new WechatInviationVo();
//            wechatInviationVo.setId(wechatInviation.getId());
//            wechatInviationVo.setStandby1((cn.hutool.json.JSONObject) JSONUtil.parse(wechatInviation.getStandby1()));
//            wechatInviationVo.setStandby2((cn.hutool.json.JSONObject) JSONUtil.parse(wechatInviation.getStandby2()));
//            wechatInviationVo.setStandby3((cn.hutool.json.JSONObject) JSONUtil.parse(wechatInviation.getStandby3()));
//            wechatInviationVo.setTemple(wechatInviation.getTemple());
//            wechatInviationVo.setTitle(wechatInviation.getTitle());
//            wechatInviationVo.setDescription(wechatInviation.getDescription());
//            wechatInviationVo.setThumbUrl(wechatInviation.getThumbUrl());
//            wechatInviationVo.setTempleUrl(wechatInviation.getTempleUrl());
//            return R.ok(wechatInviationVo);
//        }
//        throw new ServiceException("找不到该请柬！");
//    }
//
//    /**
//     * 删除请柬
//     * @param id
//     * @return
//     */
//    @DeleteMapping("/remove")
//    public R<Boolean> commentRemove(@RequestParam("id")String id){
//        if (StringUtils.isEmpty(id)){
//            throw new ServiceException("找不到该请柬！");
//        }
//        return R.ok(wechatInviationService.removeById(id));
//    }
//
//}
