package org.dromara.biz.controller.mp;


import cn.dev33.satoken.annotation.SaIgnore;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.BizBabyMedication;
import org.dromara.biz.domain.BizBabyRoutine;
import org.dromara.biz.domain.vo.nurse.BabyMedicationVO;
import org.dromara.biz.domain.vo.nurse.BabyRoutineVO;
import org.dromara.biz.service.IBizBabyInfoService;
import org.dromara.biz.service.IBizBabyMedicationService;
import org.dromara.biz.service.IBizBabyRoutineService;
import org.dromara.common.core.domain.R;
import org.springframework.web.bind.annotation.*;

import org.springframework.stereotype.Controller;

import java.util.List;


/**
 * 宝宝常规记录表 前端控制器
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping("/mp/baby/routine")
public class BizBabyRoutineController {
    private final IBizBabyRoutineService babyRoutineService;
    private final IBizBabyInfoService babyInfoService;
    /**
     * 新增宝宝常规记录
     * @param babyRoutine
     * @return
     */
    @PostMapping("/insert")
    public R<Boolean> insert(@RequestBody BizBabyRoutine babyRoutine) {

        return R.ok(babyRoutineService.save(babyRoutine));
    }

    /**
     * 修改宝宝常规记录
     * @param babyRoutine
     * @return
     */
    @PostMapping("/update")
    public R<Boolean> update(@RequestBody BizBabyRoutine babyRoutine) {
        return R.ok(babyRoutineService.updateById(babyRoutine));
    }

    /**
     * 规记录列表
     * @param customerId
     * @return
     */
    @GetMapping("/list")
    @SaIgnore
    public R<List<BabyRoutineVO>> list(@RequestParam("customerId") Long customerId){
        return R.ok(babyRoutineService.listVo(customerId));
    }
    /**
     * 规记录列表
     * @return
     */
    @GetMapping("/myList")
    public R<List<BabyRoutineVO>> myList(){
        Long  babyId = babyInfoService.getByLoginUser();
        if (babyId != null) {
            return R.ok(babyRoutineService.listVo(babyId));
        }
        return R.ok(null);
    }

    /**
     * 获取记录详情 （宝宝规）
     * @param id
     * @return
     */
    @GetMapping("/getDetail")
    @SaIgnore
    public R<BabyRoutineVO> getDetail(@RequestParam("id") Long id){
        return R.ok(babyRoutineService.getDetail(id));
    }

    /**
     * 删除记录
     * @param id
     * @return
     */
    @GetMapping("/delete")
    public R<Boolean> delete(@RequestParam("id") Long id){
        return R.ok(babyRoutineService.removeById(id));
    }

}
