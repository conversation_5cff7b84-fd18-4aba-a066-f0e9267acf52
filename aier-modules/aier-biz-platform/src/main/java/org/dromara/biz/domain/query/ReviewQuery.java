package org.dromara.biz.domain.query;


import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class ReviewQuery extends PageQuery {

    /**
     * 标签
     */
    private String tag;

    /**
     * 总体评价星级
     */
    private Integer overallRating;

    /**
     * 环境评价星级
     */
    private Integer environmentRating;

    /**
     * 服务评价星级
     */
    private Integer serviceRating;

    /**
     * 评论开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 评论结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 排序方式 1-综合 2-最热
     */
    private String orderType;

    /**
     * 查询方式 room=房间; meal_package=膳食套餐; recovery=产康; nanny=移动月嫂; package=优惠套餐;review=总体评价;all=全部;
     * pictures=图片视频;
     */
    private String reviewType = "review";

    /**
     * 查询方式：全部
     */
    @Getter
    public enum ReviewTypeEnum {
        ROOM("room"),
        MEAL_PACKAGE("meal_package"),
        RECOVERY("recovery"),
        NANNY("nanny"),
        PACKAGE("package"),
        REVIEW("review"),
        ALL("all"),
        PICTURES("pictures");

        private final String value;

        ReviewTypeEnum(String value) {
            this.value = value;
        }

    }
}
