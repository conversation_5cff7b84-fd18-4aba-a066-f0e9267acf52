package org.dromara.biz.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.dromara.biz.domain.EventTracking;
import org.dromara.biz.domain.query.EventTrackingBaseQuery;
import org.dromara.biz.domain.query.ModuleStatsQuery;
import org.dromara.biz.domain.query.WechatUserQuery;
import org.dromara.biz.domain.vo.*;
import org.dromara.biz.domain.vo.UserTotalStatsVO.PageStats;
import org.dromara.biz.domain.vo.UserTotalStatsVO.ModuleStats;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_event_tracking(小程序事件追踪表)】的数据库操作Mapper
* @createDate 2024-04-12 10:07:51
* @Entity org.dromara.biz.domain.EventTracking
*/
public interface EventTrackingMapper extends BaseMapper<EventTracking> {

    /**
     * 查询 总访问用户量、浏览总时间、总访问次数
     * @return 统计结果
     */
    TotalStatsVO selectTotalStats();

    /**
     * 查询 用户总浏览总时间、总访问次数
     * @param userId 用户id
     * @return 统计结果
     */
    TotalStatsVO selectUserTotalStats(Long userId);

    /**
     * 查询时间段内每日访问用户数量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 每日访问用户数量
     */
    List<UserDayViewStatsVO> selectVisitStatsGroupDay(@Param("startDate") Date startTime,
                                                          @Param("endDate") Date endTime);

    /**
     * 查询指定时间内的访客数量
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    UserVisitTotalStatsVO selectVisitTotal(@Param("startDate") Date startTime,
                                          @Param("endDate") Date endTime);

    /**
     * 根据用户id查询用户每个模块的访问时间
     * @param userId 用户id
     * @return 用户每个模块的访问时间
     */
    UserVisitDetailStatsVO selectViewsTimeByEventType(@Param("userId") Long userId);

    /**
     * 查询用户近7天浏览次数
     * @param userId 用户id
     * @return 7天浏览次数
     */
    List<UserDayStatsVO> selectViewsCountByUser(@Param("userId") Long userId);

    /**
     * 查询模块统计 访问时长、访问次数、访问人数、分享次数、分享人数
     * @param params 查询参数
     * @return 模块访问统计
     */
    List<EventTrackingModuleStatsVO> selectModuleStats(EventTrackingBaseQuery params);

    /**
     * 查询单个模块统计 访问时长、访问次数、访问人数、分享次数、分享人数
     * @param params 查询参数
     * @return 模块统计结果
     */
    EventTrackingModuleStatsVO selectOneModuleStats(ModuleStatsQuery params);

    /**
     * 查询单个模块每天访问次数、访问时长
     * @param params 查询参数
     * @return 模块统计结果
     */
    List<ModuleDayViewStatsVO> selectModuleStatsByDate(ModuleStatsQuery params);

    /**
     * 查询用户每个模块访问总次数和总时间
     * @param userId 用户id
     * @return 模块统计结果
     */
    List<ModuleStats> selectUserModuleStats(@Param("userId") Long userId);

    /**
     * 查询用户每个页面访问总次数和总时间
     * @param userId 用户id
     * @param module 模块
     * @return 页面统计结果
     */
    List<PageStats> selectUserModulePageStats(@Param("userId") Long userId, @Param("module") String module);

    /**
     * 按模块查询访问最长时间和访问最多次用户列表
     * @param page 分页参数
     * @param params 查询参数
     * @return 结果
     */
    Page<WechatUserVO> selectModuleUserList(Page<WechatUserVO> page, @Param("params") WechatUserQuery params);
}




