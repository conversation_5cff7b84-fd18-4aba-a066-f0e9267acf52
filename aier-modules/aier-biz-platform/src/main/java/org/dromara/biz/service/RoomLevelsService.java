package org.dromara.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.RoomLevels;
import org.dromara.biz.domain.vo.LevelVO;
import org.dromara.biz.domain.vo.SelectOptionsStringVO;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_room_levels(房间楼层表)】的数据库操作Service
* @createDate 2024-03-20 19:03:26
*/
public interface RoomLevelsService extends IService<RoomLevels> {

    Boolean addLevel(List<LevelVO> levelList);

    List<LevelVO> queryList();

    List<SelectOptionsStringVO> getSelectOptions();

    Boolean save(LevelVO roomLevel);
}
