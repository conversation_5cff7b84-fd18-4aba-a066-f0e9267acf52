package org.dromara.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.EmployeePost;
import org.dromara.biz.domain.query.employee.EmployeePostQuery;
import org.dromara.biz.domain.vo.EmployeePostVO;
import org.dromara.biz.domain.vo.SelectOptionsVO;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;

/**
 * <p>
 * 护理人员类型表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
public interface EmployeePostService extends IService<EmployeePost> {

    EmployeePostVO save(EmployeePostVO employeeNurseType);

    /**
     * 查询员工岗位列表
     */
    TableDataInfo<EmployeePostVO> queryPage(EmployeePostQuery query);

    /**
     * 查询员工职位下拉列表选项数据
     */
    List<SelectOptionsVO> getOptions();

    /**
     * 通过tenantId获取商户岗位列表
     */
    List<SelectOptionsVO> getOptionsByTenantId(String tenantId);
}
