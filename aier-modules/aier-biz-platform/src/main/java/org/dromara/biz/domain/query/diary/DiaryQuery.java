package org.dromara.biz.domain.query.diary;


import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.page.PageQuery;

@Data
@EqualsAndHashCode(callSuper = true)
public class DiaryQuery extends PageQuery {
    /**
     * 宝妈id
     */
    private Long momId;
    /**
     * 宝妈uuid(与传宝妈id一致)
     */
    private String uuid;
    /**
     * 服务天数
     */
    private Integer timeNumber;

    /**
     * 标签id
     */
    private Long taskNodeId;

    /**
     * 宝妈手机号
     */
    private String tel;

    /**
     * 发送人
     */
    private Long userId;

}
