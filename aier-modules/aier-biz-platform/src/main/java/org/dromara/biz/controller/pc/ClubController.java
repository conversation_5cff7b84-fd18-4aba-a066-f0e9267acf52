package org.dromara.biz.controller.pc;

import lombok.RequiredArgsConstructor;
import org.dromara.biz.domain.query.ClubQuery;
import org.dromara.biz.domain.vo.ClubVO;
import org.dromara.biz.service.ClubsService;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 会所信息相关接口
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/club")
public class ClubController {

    private final ClubsService clubsService;

    /**
     * 分页查询会所列表
     */
    @GetMapping("/page")
    public TableDataInfo<ClubVO> list(ClubQuery query) {
        return clubsService.queryPage(query);
    }

    /**
     * 查询会所信息
     */
    @GetMapping("/info/{clubId}")
    @Deprecated
    public R<ClubVO> info(@PathVariable Long clubId) {
        return R.ok(clubsService.getByClubId(clubId));
    }

    /**
     * 获取当前会所基本信息
     * @return 结果
     */
    @GetMapping("/info")
    public R<ClubVO> getClubInfo() {
        return R.ok(clubsService.getInfo());
    }

    /**
     * 查询当前租户会所配置信息
     */
    @GetMapping("/info/merchants")
    public R<ClubVO> infoByMerchants() {
        return R.ok(clubsService.getInfo());
    }

    /**
     * 新增会所基本信息
     * @param clubs 会所基本信息
     * @return 新增结果
     */
    @PutMapping("/save")
    @RepeatSubmit
    public R<Boolean> save(@RequestBody @Validated ClubVO clubs) {
        return R.ok(clubsService.save(clubs));
    }

    /**
     * 修改会所基本信息
     * @param clubs 会所基本信息
     * @return 修改结果
     */
    @PostMapping("/update")
    @RepeatSubmit
    public R<Boolean> update(@RequestBody @Validated ClubVO clubs) {
        return R.ok(clubsService.update(clubs));
    }
}
