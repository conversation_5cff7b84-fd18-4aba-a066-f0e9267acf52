package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 会所朋友圈动态评论表
 * @TableName biz_feed_post_comment
 */
@TableName(value ="biz_feed_post_comment")
@Data
public class FeedPostComment implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long commentId;

    /**
     * 评论
     */
    private String comment;

    /**
     * 评论人id
     */
    private Long userId;

    /**
     * 动态id
     */
    private Long postId;

    /**
     * 评论时间
     */
    private Date createTime;

    /**
     * 审查状态 有risky、pass、review三种值
     */
    private String suggest;

    /**
     * 审查命中标签枚举值，100 正常；10001 广告；20001 时政；20002 色情；20003 辱骂；20006 违法犯罪；20008 欺诈；20012 低俗；20013 版权；21000 其他
     */
    private Integer label;

    /**
     * 父级评论，如果是一级评论则为空
     */
    private Long parentCommentId;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
