package org.dromara.biz.service;

import org.dromara.biz.domain.Card;
import org.dromara.biz.domain.query.card.CardQuery;
import org.dromara.biz.domain.vo.card.CardEditStatusVO;
import org.dromara.biz.domain.vo.card.CardShareParams;
import org.dromara.biz.domain.vo.card.CardTagVO;
import org.dromara.biz.domain.vo.card.CardTemplateVO;
import org.dromara.biz.domain.vo.card.UserCardVO;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;

public interface CardTemplateService {

    TableDataInfo<CardTemplateVO> queryPage(CardQuery query);

    /**
     * 查询h5模版详情
     * @param templateId 模版id
     */
    CardTemplateVO getTemplateDetail(Long templateId);

    /**
     * 通过模版id创建用户自己的请帖
     * @param templateId h5模版id
     * @return 用户请帖id
     */
    Long createCard(Long templateId);

    /**
     * 获取用户创建的请帖详情
     * @param cardId 请帖id
     */
    UserCardVO getUserCardDetail(Long cardId);

    Long saveUserCard(Card card);

    /**
     * 分享用户请帖id
     * @param cardShareParams 分享参数
     */
    Boolean cardShare(CardShareParams cardShareParams);

    /**
     * 获取某个模版的用户编辑状态 true=之前编辑过该模版并且没有分享；false=没有编辑
     * @param templateId h5模版id
     */
    CardEditStatusVO getEditStatus(Long templateId);

    /**
     * 分页查询用户请帖列表
     * @param query 查询参数
     */
    TableDataInfo<Card> getUserCardList(CardQuery query);

    /**
     * 获取所有标签列表
     * @return 标签列表
     */
    List<CardTagVO> getAllTags();
}
