package org.dromara.biz.controller.mp;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.vo.CheckInVO;
import org.dromara.biz.domain.vo.CheckinGiftVO;
import org.dromara.biz.service.CheckInsService;
import org.dromara.biz.service.CheckinGiftsService;
import org.dromara.common.core.domain.R;
import org.springframework.web.bind.annotation.*;

/**
 * 小程序签到相关接口
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping("/mp/check_in")
public class MpCheckInController {

    private final CheckInsService checkInsService;
    private final CheckinGiftsService checkinGiftsService;

    /**
     * 签到
     * @return 签到结果
     */
    @PostMapping("/signIn")
    public R<Boolean> signIn(){
        return R.ok(checkInsService.signIn());
    }

    /**
     * 获取当前登录用户签到信息
     */
    @GetMapping("/records_info")
    public R<CheckInVO> queryCheckInInfo(){
        return R.ok("查询成功",checkInsService.queryCheckInInfo());
    }

    /**
     * 查询礼品信息
     */
    @GetMapping("/info")
    public R<CheckinGiftVO> info(@RequestParam("giftId") Long giftId) {
        return R.ok(checkinGiftsService.getByGiftId(giftId));
    }
}
