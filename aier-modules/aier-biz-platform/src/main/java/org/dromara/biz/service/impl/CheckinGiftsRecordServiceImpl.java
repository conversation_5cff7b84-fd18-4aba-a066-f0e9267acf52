package org.dromara.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.biz.domain.CheckinGiftsRecord;
import org.dromara.biz.service.CheckinGiftsRecordService;
import org.dromara.biz.mapper.CheckinGiftsRecordMapper;
import org.springframework.stereotype.Service;

/**
* <AUTHOR>
* @description 针对表【biz_checkin_gifts_record(签到礼物领取记录表)】的数据库操作Service实现
* @createDate 2024-04-09 10:45:52
*/
@Service
public class CheckinGiftsRecordServiceImpl extends ServiceImpl<CheckinGiftsRecordMapper, CheckinGiftsRecord>
    implements CheckinGiftsRecordService{

    @Override
    public Boolean whetherReceive(Long userId, Long checkInCount) {
        LambdaQueryWrapper<CheckinGiftsRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(CheckinGiftsRecord::getUserId, userId);
        lqw.eq(CheckinGiftsRecord::getCheckInDays, checkInCount);
        return baseMapper.selectCount(lqw) > 0;
    }
}




