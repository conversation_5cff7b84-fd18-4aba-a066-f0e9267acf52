package org.dromara.biz.controller.pc;

import lombok.RequiredArgsConstructor;
import org.dromara.biz.domain.DefaultCustomersStaff;
import org.dromara.biz.domain.DefaultStaffs;
import org.dromara.biz.domain.bo.DefaultCustomerStaffBO;
import org.dromara.biz.service.DefaultStaffsService;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 默认客户controller
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/default_staff")
public class DefaultStaffController {

    private final DefaultStaffsService defaultStaffsService;

    /**
     * 分页查询
     */
    @GetMapping("/page")
    public TableDataInfo<DefaultStaffs> page(PageQuery query) {
        return TableDataInfo.build(defaultStaffsService.page(query.build()));
    }

    /**
     * 查询详情
     */
    @GetMapping("/detail/{id}")
    public R<DefaultStaffs> detail(@PathVariable Long id) {
        return R.ok(defaultStaffsService.getById(id));
    }

    /**
     * 新增默认员工
     * @param defaultStaff 员工信息
     * @return 新增结果
     */
    @PostMapping("/add")
    @RepeatSubmit
    public R<Boolean> add(@RequestBody @Validated DefaultStaffs defaultStaff) {
        return R.ok(defaultStaffsService.save(defaultStaff));
    }

    /**
     * 绑定客户
     * @param defaultCustomerStaffBO 绑定信息
     * @return 绑定结果
     */
    @PostMapping("/bind_customer")
    @RepeatSubmit
    public R<Boolean> bindCustomer(@RequestBody @Validated DefaultCustomerStaffBO defaultCustomerStaffBO) {
        return R.ok(defaultStaffsService.bindCustomer(defaultCustomerStaffBO));
    }

    /**
     * 查询员工绑定客户列表
     */
    @GetMapping("/customers_staff_list/{id}")
    public R<List<DefaultCustomersStaff>> getCustomersStaffList(@PathVariable Long id) {
        return R.ok(defaultStaffsService.getCustomersStaffList(id));
    }

    /**
     * 修改默认员工
     * @param defaultStaff 员工信息
     * @return 修改结果
     */
    @PutMapping("/update")
    @RepeatSubmit
    public R<Boolean> update(@RequestBody @Validated DefaultStaffs defaultStaff) {
        return R.ok(defaultStaffsService.updateById(defaultStaff));
    }

    /**
     * 删除默认员工
     * @param id 员工id
     * @return 结果
     */
    @DeleteMapping("/{id}")
    public R<Boolean> remove(@PathVariable Long id){
        return R.ok(defaultStaffsService.removeById(id));
    }
}
