package org.dromara.biz.controller.mp;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.service.ContractGiftsService;
import org.dromara.common.core.domain.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 小程序签约礼品相关接口
 */
@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping("/mp/contract_gifts")
public class MpContractGiftsController {

    private final ContractGiftsService contractGiftsService;


    /**
     * 签约礼品领取
     * @param contractGiftId 签约礼品id
     * @return 结果
     */
    @GetMapping("/signing")
    public R<Boolean> signingContractGift(@RequestParam("contractGiftId") Long contractGiftId){
        return R.ok(contractGiftsService.signingContractGift(contractGiftId));
    }
}
