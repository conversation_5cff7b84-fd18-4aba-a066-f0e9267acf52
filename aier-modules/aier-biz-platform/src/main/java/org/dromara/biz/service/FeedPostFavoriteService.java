package org.dromara.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.FeedPostFavorite;
import org.dromara.biz.domain.query.customer.AnalysisQuery;
import org.dromara.biz.domain.vo.AnalysisVo;
import org.dromara.biz.domain.vo.TrajectoryVo;
import org.dromara.biz.domain.vo.customer.CustomerContractVO;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【biz_feed_post_favorite(朋友圈动态收藏表)】的数据库操作Service
* @createDate 2024-04-08 14:03:24
*/
public interface FeedPostFavoriteService extends IService<FeedPostFavorite> {

    /**
     * 获取指定动态的收藏信息
     * @param postIds 动态id集合
     * @return 结果
     */
    Map<Long, FeedPostFavorite> getFavoriteMap(Set<Long> postIds);

    /**
     * 获取当前用户对指定动态的收藏信息
     * @param postIds 动态id集合
     * @return 结果
     */
    Map<Long, FeedPostFavorite> getCurrentUserFavoriteMap(Set<Long> postIds);

    /**
     * 获取用户收藏的朋友圈/话题 集合
     * @param userId
     * @return
     */
    List<AnalysisVo> getAnalysisByUserId(Long userId);

    /**
     * 获取用户点赞的朋友圈/话题 集合
     * @param userId
     * @return
     */
    List<AnalysisVo> getLikeByUserId(Long userId);

    /**
     * 获取用户评论的朋友圈/话题 集合
     * @param userId
     * @return
     */
    List<AnalysisVo> getCommentByUserId(Long userId);

    /**
     * 获取用户转发的朋友圈/话题 集合
     * @param userId
     * @return
     */
    List<AnalysisVo> getForwardByUserId(Long userId);

    /**
     * 获取客户分析头部信息
     * @param userId
     * @return
     */
    Map<String,List<TrajectoryVo>> getTrajectoryTop(Long userId);

    /**
     * 获取客户分析头部信息
     */
    TableDataInfo<TrajectoryVo> trajectoryPage(AnalysisQuery query);
}
