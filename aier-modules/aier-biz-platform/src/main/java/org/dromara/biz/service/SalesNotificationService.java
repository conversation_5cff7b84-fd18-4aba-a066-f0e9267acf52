package org.dromara.biz.service;

import org.dromara.biz.domain.SalesNotification;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_sales_notification(销售员工系统通知表)】的数据库操作Service
* @createDate 2024-08-22 16:57:45
*/
public interface SalesNotificationService extends IService<SalesNotification> {

    List<SalesNotification> getNotification(Long userId);

    SalesNotification getNotificationDetail(Long id);
}
