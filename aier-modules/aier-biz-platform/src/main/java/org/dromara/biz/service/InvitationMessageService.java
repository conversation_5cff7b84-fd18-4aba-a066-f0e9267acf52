package org.dromara.biz.service;

import org.dromara.biz.domain.CardMessage;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_invitation_message(请柬留言表)】的数据库操作Service
* @createDate 2024-07-30 10:50:39
*/
public interface InvitationMessageService extends IService<CardMessage> {

    /**
     * 获取回复列表集合
     * @param: []
     * @return: java.util.List<org.dromara.biz.domain.InvitationMessage>
     * @author: CYP
     * @date: 2024/7/30
     **/
    List<CardMessage> getMessageList(Long id);

    Boolean addMessage(CardMessage cardMessage);

}
