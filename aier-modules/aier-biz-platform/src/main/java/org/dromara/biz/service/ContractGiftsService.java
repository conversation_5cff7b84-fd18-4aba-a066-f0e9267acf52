package org.dromara.biz.service;

import org.dromara.biz.domain.ContractGifts;
import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.vo.ContractGiftVO;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;
import java.util.Map;

/**
* <AUTHOR>
* @description 针对表【biz_contract_gifts(签约礼品表)】的数据库操作Service
* @createDate 2024-05-15 21:14:03
*/
public interface ContractGiftsService extends IService<ContractGifts> {

    Boolean save(ContractGiftVO contractGiftVO);

    Boolean update(ContractGiftVO contractGiftVO);

    TableDataInfo<ContractGiftVO> queryPage(PageQuery query);

    ContractGiftVO getByContractGiftId(Long contractGiftId);

    List<ContractGiftVO> queryList(PageQuery query);

    Map<Long, ContractGiftVO> queryMap();

    ContractGiftVO queryInfo(Long contractGiftId);

    Boolean signingContractGift(Long contractGiftId);

    Boolean isExistContractGift(Long contractGiftId);
}
