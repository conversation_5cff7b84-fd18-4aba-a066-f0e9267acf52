package org.dromara.biz.domain.query;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = true)
public class CommunityPostQuery extends PageQuery {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 话题id
     */
    private Long topicId;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date startDate;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endDate;

    /**
     * 排序方式 1-最新 2-最热
     */
    private String orderType;

    public interface CommunityPostQueryOrderType{
        String newest = "1";// 最新
        String hottest = "2"; // 最热
    }
}
