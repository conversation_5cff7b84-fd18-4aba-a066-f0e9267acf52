package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.dromara.common.mybatis.handler.type.StringListTypeHandler;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 宝宝护理信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-18
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("biz_baby_care")
public class BizBabyCare extends TenantEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 护理ID
     */
    @TableId
    private Long id ;

    /**
     * 宝宝ID
     */
    private Long babyId;

    /**
     * 体温
     */
    private String temperature;

    /**
     * 黄疸值
     */
    private String jaundiceValue;

    /**
     * 身高
     */
    private String height;

    /**
     * 体重
     */
    private String weight;

    /**
     * 头部情况
     */
    private String head;

    /**
     * 囟门情况
     */
    private String fontanelle;

    /**
     * 眼部情况
     */
    private String eyes;

    /**
     * 鼻部情况
     */
    private String nose;

    /**
     * 口腔情况
     */
    private String mouth;

    /**
     * 面部情况
     */
    private String face;

    /**
     * 颈部情况
     */
    private String neck;

    /**
     * 腹部情况
     */
    private String abdomen;

    /**
     * 脐部情况
     */
    private String umbilicus;

    /**
     * 生殖器情况
     */
    private String genitals;

    /**
     * 四肢情况
     */
    private String limbs;

    /**
     * 肌张力
     */
    private String muscleTone;

    /**
     * 红疹部位
     */
    private String rashLocation;

    /**
     * 血管瘤部位
     */
    private String hemangiomaLocation;

    /**
     * 蒙古斑部位
     */
    private String mongolianSpotLocation;

    /**
     * 备注
     */
    private String notes;

    /**
     * 图片信息
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> imageInfo;
}
