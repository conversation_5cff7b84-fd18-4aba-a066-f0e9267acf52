package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.handler.type.StringListTypeHandler;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 页面收藏表
 * @TableName biz_page_bookmarks
 */
@TableName(value ="biz_page_bookmarks", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class PageBookmarks extends TenantEntity implements Serializable {
    /**
     *
     */
    @TableId
    private Long bookmarkId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 页面路径
     */
    private String pagePath;

    /**
     * 页面标题
     */
    private String pageTitle;

    /**
     * 页面图片
     */
    private String imageUrl;

    /**
     * 页面标签
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> tag;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
