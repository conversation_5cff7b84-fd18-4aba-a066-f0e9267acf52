package org.dromara.biz.controller.pc;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.query.ReviewQuery;
import org.dromara.biz.domain.vo.ReviewRepliesVO;
import org.dromara.biz.domain.vo.ReviewVO;
import org.dromara.biz.service.ReviewsService;
import org.dromara.common.core.domain.R;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 商家总体评价相关接口
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping("/platform/review")
@Validated
public class ReviewController {

    private final ReviewsService reviewsService;


    /**
     * 查询商家总体评价列表
     */
    @GetMapping("/list")
    public R<List<ReviewVO>> list(ReviewQuery query) {
        return R.ok(reviewsService.queryList(query));
    }

    /**
     * 回复评价
     * @param reviewRepliesvo 回复信息
     */
    @PostMapping("/replies")
    public R<Boolean> replies(@RequestBody ReviewRepliesVO reviewRepliesvo) {
        return R.ok(reviewsService.replies(reviewRepliesvo));
    }

    /**
     * 删除评价
     * @param reviewId 总体评价id
     */
    @DeleteMapping("/remove/{reviewId}")
    public R<Boolean> remove(@PathVariable Long reviewId) {
        return R.ok(reviewsService.removeByReviewId(reviewId));
    }

    /**
     * 删除商家回复
     * @param replyId 回复id
     */
    @DeleteMapping("/remove_replies/{replyId}")
    public R<Boolean> removeReplies(@PathVariable Long replyId) {
        return R.ok(reviewsService.removeReplies(replyId));
    }

    /**
     * 查询最近7天新增的评价数量
     */
    @GetMapping("/new_stats")
    public R<Long> newStats() {
        return R.ok(reviewsService.newStats());
    }
}
