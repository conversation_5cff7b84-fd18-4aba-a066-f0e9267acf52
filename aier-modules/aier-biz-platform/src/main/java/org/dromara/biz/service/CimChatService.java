package org.dromara.biz.service;


import org.dromara.biz.dto.customerservice.WebSocketMessage;

/**
 * 聊天接口
 */
public interface CimChatService {

    /**
     * 处理聊天消息
     */
    void handleChatMessage(WebSocketMessage wsMessage, String userType);

    /**
     * 处理服务请求
     */
    void handleServiceRequest(WebSocketMessage wsMessage, Long customerId, String tenantId);

    /**
     * 处理客服接受客户请求
     */
    void handleAcceptCustomer(WebSocketMessage wsMessage, Long serviceId, String tenantId);

    /**
     * 处理转接请求
     */
    void handleTransferRequest(WebSocketMessage wsMessage, Long serviceId, String tenantId);

    /**
     * 处理结束会话
     */
    void handleEndSession(WebSocketMessage wsMessage, Long userId, String userType, String tenantId);

    /**
     * 保存离线消息
     */
    void saveOfflineMessage(WebSocketMessage message);

    /**
     * 发送正在服务的客户列表
     */
    void sendServingCustomers(String tenantId, Long serviceId);

    /**
     * 更新客服状态
     */
    void updateServiceStatus(String tenantId, Long userId, Integer status);
}
