package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.handler.type.StringListTypeHandler;
import org.dromara.common.tenant.core.TenantEntity;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * 会所总体评价表
 * @TableName biz_reviews
 */
@TableName(value ="biz_reviews", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
public class Reviews extends TenantEntity implements Serializable {
    /**
     *
     */
    @TableId
    private Long reviewId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 总体评价星级
     */
    private Integer overallRating;

    /**
     * 环境评价星级
     */
    private Integer environmentRating;

    /**
     * 服务评价星级
     */
    private Integer serviceRating;

    /**
     * 评价内容
     */
    private String content;

    /**
     * 评价标签
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> tag;

    /**
     * 评价图片
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> photos;

    /**
     * 视频链接
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> videos;

    /**
     * 评价类型 room=房间; meal_package=膳食套餐; recovery=产康; nanny=移动月嫂; package=优惠套餐;review=总体评价;
     */
    private String type;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
