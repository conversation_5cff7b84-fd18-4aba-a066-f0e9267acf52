package org.dromara.biz.service;

import org.dromara.biz.domain.InvitationCollect;
import com.baomidou.mybatisplus.extension.service.IService;

/**
* <AUTHOR>
* @description 针对表【biz_invitation_collect(请柬收藏表)】的数据库操作Service
* @createDate 2024-07-30 10:50:39
*/
public interface InvitationCollectService extends IService<InvitationCollect> {

    /**
     * 指定用户是否收藏请帖
     */
    Boolean isCollect(Long userId, Long invitationTemplateId);

    /**
     * 收藏请帖
     */
    void collect(Long userId, Long id);
}
