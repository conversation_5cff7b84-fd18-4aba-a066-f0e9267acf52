package org.dromara.biz.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.RequiredArgsConstructor;
import org.dromara.biz.domain.CustomerReminders;
import org.dromara.biz.mapper.CustomerRemindersMapper;
import org.dromara.biz.service.CustomerRemindersService;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
* <AUTHOR>
* @description 针对表【biz_customer_reminders(客户跟进事项提醒)】的数据库操作Service实现
* @createDate 2024-07-22 15:48:55
*/
@Service
@RequiredArgsConstructor
public class CustomerRemindersServiceImpl extends ServiceImpl<CustomerRemindersMapper, CustomerReminders>
    implements CustomerRemindersService{

    @Override
    public CustomerReminders getDetail(Long reminderId) {
        CustomerReminders reminders = baseMapper.getDetail(reminderId);
        if(ObjectUtil.isNotNull(reminders)){
            Date reminderDate = reminders.getReminderDate();
            reminders.setDate(DateUtil.format(reminderDate, "MM-dd"));
            reminders.setTime(DateUtil.format(reminderDate, "HH:mm:ss"));
            return reminders;
        }
        return null;
    }
}




