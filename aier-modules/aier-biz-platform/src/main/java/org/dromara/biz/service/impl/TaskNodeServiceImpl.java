package org.dromara.biz.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.biz.domain.ServiceProcedureSteps;
import org.dromara.biz.domain.TaskNode;
import org.dromara.biz.domain.query.node.TaskNodeQuery;
import org.dromara.biz.domain.vo.CustomerServiceStepVO;
import org.dromara.biz.domain.vo.TaskNodeVO;
import org.dromara.biz.domain.vo.customer.MamaInfoVO;
import org.dromara.biz.mapper.TaskNodeMapper;
import org.dromara.biz.service.*;
import org.dromara.common.core.enums.AppUserRoleEnum;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【biz_task_node(服务节点表)】的数据库操作Service实现
* @createDate 2024-05-18 16:30:24
*/
@Service
@AllArgsConstructor
public class TaskNodeServiceImpl extends ServiceImpl<TaskNodeMapper, TaskNode>
    implements TaskNodeService{

    private final ServiceProcedureStepsService serviceProcedureStepsService;
    private final MamaService mamaService;

    @Override
    public TableDataInfo<TaskNodeVO> queryPage(PageQuery query) {
        IPage<TaskNodeVO> page = baseMapper.selectVoPage(query.build(), buildQueryWrapper());
        return TableDataInfo.build(page);
    }

    @Override
    public List<TaskNodeVO> queryList(){
        List<TaskNode> nodeList = list();
        return MapstructUtils.convert(nodeList, TaskNodeVO.class);
    }

    @Override
    public List<TaskNodeVO> getListByUserId(Long userId){
        TaskNodeQuery query = new TaskNodeQuery();
        query.setUserId(userId);
        return baseMapper.queryList(query);
    }

    private LambdaQueryWrapper<TaskNode> buildQueryWrapper() {
        LambdaQueryWrapper<TaskNode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(TaskNode::getCreateTime);
        return queryWrapper;
    }

    @Override
    public TaskNodeVO queryInfo(Long taskNodeId) {
        return baseMapper.selectVoById(taskNodeId);
    }

    @Override
    public Boolean save(TaskNodeVO taskNodeVO) {
        TaskNode taskNode = MapstructUtils.convert(taskNodeVO, TaskNode.class);
        taskNode.setEnable(true);
        return baseMapper.insert(taskNode) > 0;
    }

    @Override
    public Boolean update(TaskNodeVO taskNodeVO) {
        TaskNode taskNode = MapstructUtils.convert(taskNodeVO, TaskNode.class);
        return baseMapper.updateById(taskNode) > 0;
    }

    @Override
    public Map<Long, TaskNodeVO> getMapByIds(Set<Long> taskNodeIds) {
        if(CollectionUtils.isEmpty(taskNodeIds)){
            return Map.of();
        }
        LambdaQueryWrapper<TaskNode> lqw = Wrappers.lambdaQuery();
        lqw.in(TaskNode::getTaskNodeId, taskNodeIds);
        List<TaskNodeVO> taskNodeList = baseMapper.selectVoList(lqw);
        return taskNodeList.stream().collect(Collectors.toMap(TaskNodeVO::getTaskNodeId, Function.identity()));
    }

    @Override
    public Boolean changeFeatured(TaskNodeVO taskNodeVO){
        Long taskNodeId = taskNodeVO.getTaskNodeId();
        Boolean isFeatured = taskNodeVO.getIsFeatured();
        if(ObjectUtil.isNull(taskNodeId) || ObjectUtil.isNull(isFeatured)){
            throw new ServiceException("参数错误");
        }
        LambdaUpdateWrapper<TaskNode> luw = Wrappers.lambdaUpdate();
        luw.eq(TaskNode::getTaskNodeId, taskNodeId);
        luw.set(TaskNode::getIsFeatured, isFeatured);
        return update(luw);
    }

    @Override
    public List<TaskNodeVO> listByType(String type, String role) {
        LambdaUpdateWrapper<TaskNode> luw = Wrappers.lambdaUpdate();
        luw.orderByAsc(TaskNode::getSortOrder);
        luw.orderByDesc(TaskNode::getCreateTime);
        if (!StrUtil.isBlank(type)) {
            luw.eq(TaskNode::getNurseType, type);
        }
        if (!StrUtil.isBlank(role) && !role.equals(AppUserRoleEnum.CUSTOMER.name())) {
            luw.eq(TaskNode::getNurseRole, role);
        }
        if(LoginHelper.isLogin() && StpUtil.hasRole(AppUserRoleEnum.CUSTOMER.name())){
            return  baseMapper.selectVoList(luw);
        }
        return  baseMapper.selectVoList(luw);
    }

    @Override
    public List<TaskNodeVO> getOptions(){
        if(StpUtil.hasRoleOr(AppUserRoleEnum.CUSTOMER.name(), AppUserRoleEnum.SALES.name())){
            return baseMapper.selectVoList();
        }
        List<String> roleList = StpUtil.getRoleList();
        if(CollUtil.isEmpty(roleList)){
            return baseMapper.selectVoList();
        }
        String roleCode = roleList.get(0);
        LambdaQueryWrapper<TaskNode> lqw = Wrappers.lambdaQuery();
        lqw.eq(TaskNode::getNurseRole, roleCode);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(Long id){
        Boolean hasDynamicLink = serviceProcedureStepsService.hasDynamicLink(id);
        if(hasDynamicLink){
            throw new ServiceException("标签已经使用, 无法删除！");
        }
        return removeById(id);
    }

    @Override
    @Deprecated
    public List<CustomerServiceStepVO> getCustomerServiceStepList(Long userId, String group) {
        MamaInfoVO mamaInfo = mamaService.getMamaInfo(userId);
        if(ObjectUtil.isNull(mamaInfo)){
            return Collections.emptyList();
        }
        Date checkInTime = mamaInfo.getCheckInTime();
        if(ObjectUtil.isNull(checkInTime)){
            return Collections.emptyList();
        }

        LambdaQueryWrapper<ServiceProcedureSteps> lqw = Wrappers.lambdaQuery();
        lqw.eq(ServiceProcedureSteps::getTargetUserId, userId);
        List<ServiceProcedureSteps> stepList = serviceProcedureStepsService.list(lqw);

        List<CustomerServiceStepVO> result = new ArrayList<>();
        if("day".equals(group)){
            result = getCustomerServiceStepListByDay(stepList, checkInTime);
        }else if("week".equals(group)){
            result = getCustomerServiceStepListByWeek(stepList, checkInTime);
        }else if("all".equals(group)){
            result = getCustomerServiceStepListAll(stepList);
            return result.stream().toList();
        }
        return result.stream().sorted(Comparator.comparing(CustomerServiceStepVO::getStartDate)).toList();
    }

    private List<CustomerServiceStepVO> getCustomerServiceStepListAll(List<ServiceProcedureSteps> stepList){

        List<CustomerServiceStepVO> result = new ArrayList<>();
        CustomerServiceStepVO customerServiceStepVO = new CustomerServiceStepVO();
        customerServiceStepVO.setGroup("all");
        List<CustomerServiceStepVO.NodeStep> nodeSteps = getStepList(stepList);
        customerServiceStepVO.setStepList(nodeSteps);
        result.add(customerServiceStepVO);
        return result;
    }

    private List<CustomerServiceStepVO> getCustomerServiceStepListByDay(List<ServiceProcedureSteps> stepList,
                                                                        Date checkinDate){

        Map<String, List<ServiceProcedureSteps>> stepMap = stepList.stream()
                .collect(Collectors.groupingBy((step) -> {
                    Date publishedAt = step.getPublishedAt();
                    return DateUtil.format(publishedAt, "yyyy-MM-dd");
                }));

        List<CustomerServiceStepVO> result = new ArrayList<>();
        stepMap.forEach((date, steps) -> {
            long dayNum = DateUtil.betweenDay(checkinDate, DateUtil.parseDate(date), true);
            dayNum = dayNum + 1;
            CustomerServiceStepVO customerServiceStepVO = new CustomerServiceStepVO();
            customerServiceStepVO.setDayName(StrUtil.format("第{}天", dayNum));
            customerServiceStepVO.setStartDate(date);
            customerServiceStepVO.setEndDate(date);
            customerServiceStepVO.setGroup("day");
            List<CustomerServiceStepVO.NodeStep> nodeSteps = getStepList(steps);
            customerServiceStepVO.setStepList(nodeSteps);
            result.add(customerServiceStepVO);
        });
        return result;
    }

    private List<CustomerServiceStepVO> getCustomerServiceStepListByWeek(List<ServiceProcedureSteps> stepList,
                                                                         Date checkinDate){
        Map<String, List<ServiceProcedureSteps>> stepMap = stepList.stream()
                .collect(Collectors.groupingBy((step) -> {
                    Date publishedAt = step.getPublishedAt();
                    long week = DateUtil.betweenWeek(checkinDate, publishedAt, true);
                    week = week + 1;
                    return StrUtil.format("第{}周", week);
                }));

        List<CustomerServiceStepVO> result = new ArrayList<>();
        stepMap.forEach((week, steps) -> {
            steps = steps.stream().sorted(Comparator.comparing(ServiceProcedureSteps::getPublishedAt)).toList();
            Date start = steps.get(0).getPublishedAt();
            Date end = steps.get(steps.size() - 1).getPublishedAt();
            CustomerServiceStepVO customerServiceStepVO = new CustomerServiceStepVO();
            customerServiceStepVO.setDayName(week);
            customerServiceStepVO.setGroup("week");
            customerServiceStepVO.setStartDate(DateUtil.formatDate(start));
            customerServiceStepVO.setEndDate(DateUtil.formatDate(end));

            List<CustomerServiceStepVO.NodeStep> nodeSteps = getStepList(steps);
            customerServiceStepVO.setStepList(nodeSteps);
            result.add(customerServiceStepVO);
        });

        return result;
    }

    /**
     * 获取节点列表
     * @param steps 服务过程步骤列表
     * @return 节点列表
     */
    private List<CustomerServiceStepVO.NodeStep> getStepList(List<ServiceProcedureSteps> steps){

        //节点去重
        Set<Long> nodeIds = steps.stream().map(ServiceProcedureSteps::getTaskNodeId)
                .collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(nodeIds)){
            return List.of();
        }
        List<TaskNode> nodeList = lambdaQuery()
                .in(TaskNode::getTaskNodeId, nodeIds).list();

        return nodeList.stream().map(node -> {
            CustomerServiceStepVO.NodeStep nodeStep = new CustomerServiceStepVO.NodeStep();
            nodeStep.setTaskNodeId(node.getTaskNodeId());
            nodeStep.setNodeName(node.getNodeName());
            nodeStep.setIsFeatured(node.getIsFeatured());
            return nodeStep;
        }).sorted(Comparator.comparing(CustomerServiceStepVO.NodeStep::getIsFeatured).reversed()).toList();
    }
}




