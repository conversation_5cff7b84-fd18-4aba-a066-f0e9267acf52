package org.dromara.biz.domain.vo;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.biz.domain.WechatRole;
import org.dromara.biz.domain.WechatUser;
import org.dromara.common.translation.annotation.Translation;
import org.dromara.common.translation.constant.TransConstant;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 微信用户vo
 * <AUTHOR>
 */
@Data
@AutoMapper(target = WechatUser.class)
public class WechatUserVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * id
     */
    private Long userId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 年龄
     */
    private String age;

    /**
     * unionid
     */
    private String unionid;

    /**
     * openid
     */
    private String openid;

    /**
     * 用户类型 1小程序 2公众号 等等
     */
    private String utype;

    /**
     * 手机
     */
    private String tel;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 性别 0=男；1=女
     */
    private String sex;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 城市
     */
    private String city;

    /**
     * 地址
     */
    private String address;

    /**
     * 最近登录时间
     */
    private Date lastLoginTime;

    /**
     * 最近登录ip
     */
    private String lastLoginIp;

    /**
     * ip属地
     */
    @Translation(type = TransConstant.IP_TO_LOCATION, mapper = "lastLoginIp")
    private String ipLocation;

    /**
     * 角色 USER=用户 CUSTOMER=客户 STAFF=员工 BOSS=老板; SALES=销售员工；SALES_MANAGER=销售主管
     */
    private Set<String> rolePermission;

    /**
     * 预产期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date dueDate;

    /**
     * 出生日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthDate;

    /**
     * 套房名称
     */
    private String suiteName = "";

    /**
     * 房间号
     */
    private String roomNumber = "";

    /**
     * 员工职位
     */
    private String staffPost = "";

    /**
     * 会所名称
     */
    @Translation(type = TransConstant.CLUB_NAME)
    private String clubName = "";

    /**
     * 是否绑定手机号码
     */
    private Boolean hasBindPhone = false;

    /**
     * 账号状态 0正常；1禁用
     */
    private String status;

    /**
     * appid
     */
    private String appid;

    /**
     * 已分配人数
     */
    private Long followUpNum;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 客户真实姓名
     */
    private String customerName;
    /**
     * 客户id
     */
    private Long customerId;
    /**
     * 客户来源
     */
    private String source;

    /**
     * 角色
     */
    private List<WechatRole> roles = CollUtil.newArrayList();

    /**
     * 用户的房间id
     */
    private List<Long> roomIds;

    /**
     * 用户角色
     */
    private Long roleId;

    /**
     * 访问时长
     */
    private Long viewTime;
    /**
     * 访问时长
     */
    private String viewTimeStr;

    /**
     * 访问数量
     */
    private Long viewNum;

    /**
     * 入住时间
     */
    private Date checkInTime;
}
