package org.dromara.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.dromara.biz.domain.InvitationLikes;
import org.dromara.biz.service.InvitationLikesService;
import org.dromara.biz.mapper.InvitationLikesMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_invitation_likes(点赞表)】的数据库操作Service实现
* @createDate 2024-07-30 10:50:39
*/
@Service
public class InvitationLikesServiceImpl extends ServiceImpl<InvitationLikesMapper, InvitationLikes>
    implements InvitationLikesService{

    /**
     * 点赞操作
     * @param: [masterId]
     * @return: java.lang.Boolean
     * @author: CYP
     * @date: 2024/8/1
     **/
    @Override
    @Transactional
    public Boolean addLike(Long masterId) {
        QueryWrapper<InvitationLikes> wrapper = new QueryWrapper<>();
        wrapper.eq("master_id",masterId);
        List<InvitationLikes> likes = this.baseMapper.selectList(wrapper);
        //如果没有创建过 那么创建一个 然后起始的count为1
        if (likes.isEmpty()){
            InvitationLikes invitationLikes = new InvitationLikes();
            invitationLikes.setLikesCount(1);
            invitationLikes.setUpdateTime(new Date());
            this.baseMapper.insert(invitationLikes);
            return true;
        }else {
            InvitationLikes invitationLikes = likes.get(0);
            invitationLikes.setLikesCount(invitationLikes.getLikesCount()+1);
            this.baseMapper.updateById(invitationLikes);
            return true;
        }

    }

    /**
     * 查询点赞数量
     * @param: [masterId]
     * @return: org.dromara.biz.domain.InvitationLikes
     * @author: CYP
     * @date: 2024/8/1
     **/
    @Override
    public InvitationLikes getLikes(Long masterId) {
        QueryWrapper<InvitationLikes> wrapper = new QueryWrapper<>();
        wrapper.eq("master_id",masterId);
        List<InvitationLikes> likes = this.baseMapper.selectList(wrapper);
        if (likes.isEmpty()){
            return new InvitationLikes();
        }else {
            return likes.get(0);
        }

    }
}




