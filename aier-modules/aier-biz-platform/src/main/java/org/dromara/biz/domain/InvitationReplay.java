package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 请柬回复人表
 * @TableName biz_invitation_replay
 */
@TableName(value ="biz_invitation_replay")
@Data
public class InvitationReplay implements Serializable {
    /**
     * 主键id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 主表id
     */
    private Long masterId;

    /**
     * 回复人姓名
     */
    private String replayName;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 回复人人数
     */
    private String replayNum;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}