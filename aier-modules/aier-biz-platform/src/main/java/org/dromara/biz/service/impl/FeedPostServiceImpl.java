package org.dromara.biz.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaSubscribeMessage;
import cn.binarywang.wx.miniapp.bean.security.WxMaMsgSecCheckCheckResponse;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.biz.domain.*;
import org.dromara.biz.domain.bo.CreateFeedPostBO;
import org.dromara.biz.domain.bo.FeedPostBO;
import org.dromara.biz.domain.bo.FeedPostCommentBO;
import org.dromara.biz.domain.bo.feedPost.SalesFeedPostBO;
import org.dromara.biz.domain.query.CustomerServiceStepQuery;
import org.dromara.biz.domain.query.FeedPostNotificationQuery;
import org.dromara.biz.domain.query.FeedPostPageQuery;
import org.dromara.biz.domain.query.FeedPostQuery;
import org.dromara.biz.domain.vo.*;
import org.dromara.biz.domain.vo.employee.EmployeeVO;
import org.dromara.biz.domain.vo.room.RoomDetailVO;
import org.dromara.biz.mapper.FeedPostMapper;
import org.dromara.biz.service.*;
import org.dromara.common.core.enums.*;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
* <AUTHOR>
* @description 针对表【biz_feed_post(会所朋友圈动态表)】的数据库操作Service实现
* @createDate 2024-03-15 10:57:12
*/
@Service
@AllArgsConstructor
@Slf4j
public class FeedPostServiceImpl extends ServiceImpl<FeedPostMapper, FeedPost>
    implements FeedPostService{

    private final StaffService staffService;
    private final WechatUserService wechatUserService;
    private final FeedPostCommentService feedPostCommentService;
    private final FeedPostLinksService feedPostLinksService;
    private final FeedPostNotificationService feedPostNotificationService;
    private final RoomsService roomsService;
    private final CustomersService customersService;
    private final FeedPostShareService feedPostShareService;
    private final FeedPostFavoriteService feedPostFavoriteService;
    private final ServiceProcedureStepsService serviceProcedureStepsService;
    private final FeedPostFeaturedService feedPostFeaturedService;
    private final WechatService wechatService;
    private final FeedPostPhotosService feedPostPhotosService;
    private final FeedPostVideosService feedPostVideosService;
    private final UgcTraceService ugcTraceService;
    private final IBizEmployeeService employeeService;
    private final RoomOccupationsService occupationsService;
    private final WechatUserRoleService wechatUserRoleService;
    private final WechatRoleService roleService;
    private final IBizEmployeeRoomService employeeRoomService;
    private final IBizFeedPostRemindersService remindersService;
    private final WxMaService wxMaService;
    private final IBizDiaryMomService diaryMomService;

    @Override
    public TableDataInfo<FeedPostVO> queryPage(FeedPostPageQuery query) {
        LambdaQueryWrapper<FeedPost> lqw = this.buildQueryWrapper(query);
        Page<FeedPost> feedPostPage = baseMapper.selectPage(query.build(), lqw);
        return this.convertHandler(feedPostPage);
    }

    @Override
    public List<FeedPostVO> listByIds(List<Long> ids) {
        Long userId = LoginHelper.getUserId();
        QueryWrapper<FeedPostVO> wrapper =   Wrappers.query();
        wrapper.in("fp.post_id", ids);
        return baseMapper.getFeedPostList(wrapper, userId);
    }

    private LambdaQueryWrapper<FeedPost> buildQueryWrapper(FeedPostPageQuery query){
        long uid = LoginHelper.getUserId();
        LambdaQueryWrapper<FeedPost> lqw = Wrappers.lambdaQuery();
        if(ObjectUtil.isNotNull(query.getExcludeType())){
            lqw.ne(FeedPost::getType, query.getExcludeType());
        }
        lqw.eq(FeedPost::getSuggest, UgcSuggestEnum.PASS.getSuggest());

        if(StpUtil.hasRole("CUSTOMER")){
            //客户可查看为自己的服务人员动态
            List<Long> staffIds = this.getStaffIds(uid);
            staffIds.add(uid);
            lqw.and(wrapper-> wrapper.in(FeedPost::getAuthorId, staffIds)
                .or().eq(FeedPost::getType, FeedPost.TYPE_CLUB));
        }else if(StpUtil.hasRole("STAFF")){
            //员工可查看自己服务的客户动态
            Staff staff = staffService.lambdaQuery()
                .eq(Staff::getUserId, uid)
                .one();
            if(ObjectUtil.isNull(staff)){
                throw new ServiceException("您的账号还未绑定员工");
            }
            Long staffId = staff.getStaffId();
            List<Rooms> roomList = roomsService.lambdaQuery()
                .apply("FIND_IN_SET({0}, service_staff_ids)", staffId)
                .list();
            Set<Long> customerIds = roomList.stream().map(Rooms::getCustomerId).collect(Collectors.toSet());
            if(CollectionUtils.isEmpty(customerIds)){
                customerIds.add(-1L);
            }
            List<Customers> customerList = customersService.lambdaQuery()
                .in(Customers::getCustomerId, customerIds).list();
            Set<Long> userIds = customerList.stream().map(Customers::getUserId).collect(Collectors.toSet());

            if(CollectionUtils.isEmpty(userIds)){
                userIds.add(-1L);
            }

            userIds.add(uid);
            lqw.and(wrapper-> wrapper.in(FeedPost::getAuthorId, userIds)
                .or().eq(FeedPost::getType, FeedPost.TYPE_CLUB));
        } else if(StpUtil.hasRole("BOSS")){
            //会所条件先预留
        } else {
            lqw.eq(FeedPost::getPostId, -1);
        }
        lqw.orderByDesc(FeedPost::getCreateTime);
        return lqw;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean publishPost(FeedPostBO post) {

        String type = FeedPost.TYPE_OTHER;
        long userId = LoginHelper.getUserId();
        Long customerId = null;
        Long targetUserId = null;
        Long roomId = null;

        if(StpUtil.hasRole(AppUserRoleEnum.CUSTOMER.name())){
            // 宝妈动态
            type = FeedPost.TYPE_USER;
            CustomerVO customer = customersService.getByUserId(userId);
            if(ObjectUtil.isNotNull(customer)){
                roomId = customer.getRoomId();
            }
        }else if(StpUtil.hasRoleOr(AppUserRoleEnum.BOSS.name(),AppUserRoleEnum.APP_SUPER_ADMIN.name(),AppUserRoleEnum.SALES.name())){
            // 会所动态  (老板，管理员，销售)
            type = FeedPost.TYPE_CLUB;
        }else if(StpUtil.hasRoleOr(AppUserRoleEnum.STAFF.name(), AppUserRoleEnum.NURSE.name(),
                AppUserRoleEnum.POSTPARTUM.name(), AppUserRoleEnum.CHEF.name(), AppUserRoleEnum.MATERNITY_NANNY.name())){
            // 员工动态
            type = FeedPost.TYPE_STAFF;
//            List<Rooms> rooms = employeeService.getEmployeeRoomsByUserId(userId);

            // 这里判断长度等于1 是护理角色只会绑定一间房间
//            if(CollUtil.size(rooms) == 1 && ObjectUtil.isNull(post.getRoomId())){
//                Rooms room = CollUtil.get(rooms, 0);
//                if(ObjectUtil.isNotNull(room)){
//                    RoomDetailVO roomDetail = roomsService.getDetail(room.getRoomId());
//                    // roomDetail 为空是房间没有客户入住
//                    if(ObjectUtil.isNotNull(roomDetail)){
//                        customerId = roomDetail.getCustomerId();
//                        targetUserId = roomDetail.getCustomerUserId();
//                        roomId = roomDetail.getRoomId();
//                    }
//                }
//            }else {
//                RoomDetailVO roomDetail = roomsService.getDetail(post.getRoomId());
//                if(ObjectUtil.isNotNull(roomDetail)){
//                    customerId = roomDetail.getCustomerId();
//                    targetUserId = roomDetail.getCustomerUserId();
//                    roomId = roomDetail.getRoomId();
//                }
//            }
            RoomDetailVO roomDetail = roomsService.getDetail(post.getRoomId());
            if(ObjectUtil.isNotNull(roomDetail)){
                customerId = roomDetail.getCustomerId();
                targetUserId = roomDetail.getCustomerUserId();
                roomId = roomDetail.getRoomId();
            }
        }

        FeedPost feedPost = new FeedPost();
        feedPost.setContent(post.getContent());
        feedPost.setAuthorId(userId);
        feedPost.setType(type);
        WxMaMsgSecCheckCheckResponse.ResultBean messageCheck = wechatService
            .messageCheck(post.getContent(), UgcSceneEnum.FORUM.getScene());
        feedPost.setLikesCount(0);
        feedPost.setCommentsCount(0);
        feedPost.setContentPhotos(post.getContentPhotos());
        feedPost.setVideos(post.getVideos());
        feedPost.setFavoriteCount(0);
        feedPost.setShareCount(0);
        feedPost.setSuggest(messageCheck.getSuggest());
        feedPost.setLabel(Integer.valueOf(messageCheck.getLabel()));
        feedPost.setTitle(post.getTitle());
        boolean flag = save(feedPost);

        // 关联服务过程
        ServiceProcedureSteps serviceProcedureSteps = new ServiceProcedureSteps();
        serviceProcedureSteps.setCustomerId(customerId);
        serviceProcedureSteps.setRoomId(roomId);
        serviceProcedureSteps.setTargetUserId(targetUserId);
        serviceProcedureSteps.setTaskNodeId(post.getTaskNodeId());
        serviceProcedureSteps.setPostId(feedPost.getPostId());
        serviceProcedureSteps.setPublishedAt(DateUtil.date());
        serviceProcedureStepsService.save(serviceProcedureSteps);
        // 附件内容审查
        feedPostAttachmentUgcHandler(post.getContentPhotos(), post.getVideos(), feedPost.getPostId());
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean salesPublish(SalesFeedPostBO post){
        String roleCode = post.getRoleCode();
        Long roomId = post.getRoomId();
        Long employeeId = null;
        Long userId = LoginHelper.getUserId();
        Long customerId = null;
        Long targetUserId = null;
        if(StrUtil.isNotEmpty(roleCode)){
            LambdaQueryWrapper<BizEmployeeRoom> lqw = Wrappers.lambdaQuery();
            lqw.eq(BizEmployeeRoom::getRoleCode, roleCode);
            lqw.eq(BizEmployeeRoom::getRoomId, roomId);
            long count = employeeRoomService.count(lqw);
            if(count > 1L){
                List<BizEmployeeRoom> employeeRoomList = employeeRoomService.list();
                BizEmployeeRoom employeeRoom = employeeRoomList.get(0);
                employeeId = employeeRoom.getEmployeeId();
            }else {
                BizEmployeeRoom employeeRoom = employeeRoomService.getOne(lqw);
                if(ObjectUtil.isNotNull(employeeRoom)){
                    employeeId = employeeRoom.getEmployeeId();
                }
            }

            if(ObjectUtil.isNotNull(employeeId)){
                EmployeeVO employee = employeeService.detail(employeeId);
                if(ObjectUtil.isNotNull(employee)){
                    userId = employee.getUserId();
                }
            }
        }

        RoomDetailVO roomDetail = roomsService.getDetail(roomId);
        if(ObjectUtil.isNotNull(roomDetail)){
            customerId = roomDetail.getCustomerId();
            targetUserId = roomDetail.getCustomerUserId();
        }

        FeedPost feedPost = new FeedPost();
        feedPost.setContent(post.getContent());
        feedPost.setAuthorId(userId);
        feedPost.setType("STAFF");
        WxMaMsgSecCheckCheckResponse.ResultBean messageCheck = wechatService
                .messageCheck(post.getContent(), UgcSceneEnum.FORUM.getScene());
        feedPost.setLikesCount(0);
        feedPost.setCommentsCount(0);
        feedPost.setContentPhotos(post.getContentPhotos());
        feedPost.setVideos(post.getVideos());
        feedPost.setFavoriteCount(0);
        feedPost.setShareCount(0);
        feedPost.setSuggest(messageCheck.getSuggest());
        feedPost.setLabel(Integer.valueOf(messageCheck.getLabel()));
        feedPost.setTitle(post.getTitle());
        boolean flag = save(feedPost);

        // 关联服务过程
        ServiceProcedureSteps serviceProcedureSteps = new ServiceProcedureSteps();
        serviceProcedureSteps.setCustomerId(customerId);
        serviceProcedureSteps.setRoomId(roomId);
        serviceProcedureSteps.setTargetUserId(targetUserId);
        serviceProcedureSteps.setTaskNodeId(post.getTaskNodeId());
        serviceProcedureSteps.setPostId(feedPost.getPostId());
        serviceProcedureSteps.setPublishedAt(DateUtil.date());
        serviceProcedureStepsService.save(serviceProcedureSteps);
        // 附件内容审查
        feedPostAttachmentUgcHandler(post.getContentPhotos(), post.getVideos(), feedPost.getPostId());
        return flag;
    }

    @Override
    public Map<Long, FeedPost> queryMapByIds(List<Long> postIds2) {
        if(CollectionUtils.isEmpty(postIds2)){
            return MapUtil.empty();
        }
        LambdaQueryWrapper<FeedPost> lqw = Wrappers.lambdaQuery();
        lqw.in(FeedPost::getPostId, postIds2);
        List<FeedPost> communityPosts = baseMapper.selectList(lqw);
        return communityPosts.stream()
            .collect(Collectors.toMap(FeedPost::getPostId, Function.identity(), (o1, o2) -> o1));
    }

    /**
     * 内容审查处理
     * @param contentPhotos 图片
     * @param videos 视频
     * @param feedPostId 动态id
     */
    private void feedPostAttachmentUgcHandler(List<String> contentPhotos, List<String> videos, Long feedPostId){

        if(CollUtil.isNotEmpty(contentPhotos)){
            for (String contentPhoto : contentPhotos) {
                String traceId = wechatService
                        .mediaCheck(contentPhoto, UgcSceneEnum.FORUM.getScene(), UgcMediaType.IMAGE.getCode());

                FeedPostPhotos feedPostPhotos = new FeedPostPhotos();
                feedPostPhotos.setPostId(feedPostId);
                feedPostPhotos.setUrl(contentPhoto);
                feedPostPhotos.setTraceId(traceId);
                feedPostPhotos.setLabel(null);
                feedPostPhotos.setSuggest(UgcSuggestEnum.PENDING.getSuggest());
                feedPostPhotosService.save(feedPostPhotos);

                UgcTrace ugcTrace = new UgcTrace();
                ugcTrace.setScene(UgcSceneEnum.FORUM.getScene());
                ugcTrace.setMediaType(UgcMediaType.IMAGE.getCode());
                ugcTrace.setMediaUrl(contentPhoto);
                ugcTrace.setTraceId(traceId);
                ugcTrace.setBusinessType(UgcBusinessType.FEED_POST_IMG.getType());
                ugcTrace.setEntityId(feedPostPhotos.getId());
                ugcTraceService.save(ugcTrace);
            }
        }

        if(CollUtil.isNotEmpty(videos)){
            for (String video : videos) {
                String traceId = wechatService
                        .mediaCheck(video, UgcSceneEnum.FORUM.getScene(), UgcMediaType.AUDIO.getCode());
                FeedPostVideos feedPostVideos = new FeedPostVideos();
                feedPostVideos.setPostId(feedPostId);
                feedPostVideos.setVideoUrl(video);
                feedPostVideos.setTraceId(traceId);
                feedPostVideos.setLabel(null);
                feedPostVideos.setSuggest(UgcSuggestEnum.PENDING.getSuggest());
                feedPostVideosService.save(feedPostVideos);

                UgcTrace ugcTrace = new UgcTrace();
                ugcTrace.setScene(UgcSceneEnum.FORUM.getScene());
                ugcTrace.setMediaType(UgcMediaType.AUDIO.getCode());
                ugcTrace.setMediaUrl(video);
                ugcTrace.setTraceId(traceId);
                ugcTrace.setBusinessType(UgcBusinessType.FEED_POST_VIDEO.getType());
                ugcTrace.setEntityId(feedPostVideos.getId());
                ugcTraceService.save(ugcTrace);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean likes(Long postId) {
        long uid = LoginHelper.getUserId();
        LambdaQueryWrapper<FeedPostLinks> lqw = Wrappers.lambdaQuery();
        lqw.eq(FeedPostLinks::getUserId, uid);
        lqw.eq(FeedPostLinks::getPostId, postId);
        long linkCount = feedPostLinksService.count(lqw);
        if(linkCount > 0L){
            this.lambdaUpdate().eq(FeedPost::getPostId, postId)
                .setSql("likes_count = likes_count - 1")
                .update();
            return feedPostLinksService.remove(lqw);
        }
        FeedPostLinks links = new FeedPostLinks();
        links.setPostId(postId);
        links.setLinkTime(new Date());
        links.setUserId(uid);
        lambdaUpdate().eq(FeedPost::getPostId, postId)
            .setSql("likes_count = likes_count + 1")
            .update();
        //通知
        this.notificationHandler(FeedPostNotification.TYPE_LIKES, postId, links.getLinkId(),"赞了你的动态");
        return feedPostLinksService.save(links);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean comment(FeedPostCommentBO commentBo) {
        long uid = LoginHelper.getUserId();
        FeedPostComment feedPostComment = new FeedPostComment();
        feedPostComment.setPostId(commentBo.getPostId());
        feedPostComment.setComment(commentBo.getComment());
        feedPostComment.setCreateTime(new Date());
        feedPostComment.setUserId(uid);
        feedPostComment.setParentCommentId(commentBo.getParentCommentId());

        WxMaMsgSecCheckCheckResponse.ResultBean messageCheck = wechatService
            .messageCheck(commentBo.getComment(), UgcSceneEnum.COMMENT.getScene());
        feedPostComment.setSuggest(messageCheck.getSuggest());
        feedPostComment.setLabel(Integer.valueOf(messageCheck.getLabel()));

        this.lambdaUpdate().eq(FeedPost::getPostId, commentBo.getPostId())
            .setSql("comments_count = comments_count + 1")
            .update();
        boolean save = feedPostCommentService.save(feedPostComment);
        //通知
        this.notificationHandler(FeedPostNotification.TYPE_COMMENT, commentBo.getPostId(), feedPostComment.getCommentId(),commentBo.getComment());
        return save;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean commentRemove(Long commentId) {
        FeedPostComment comment = feedPostCommentService.getById(commentId);
        if(ObjectUtil.isNull(comment)){
            return true;
        }
        this.lambdaUpdate().eq(FeedPost::getPostId, comment.getPostId())
            .setSql("comments_count = comments_count - 1")
            .update();
        return feedPostCommentService.removeById(comment);
    }

    @Override
    public TableDataInfo<FeedPostVO> queryClubPage(PageQuery query) {
        Page<FeedPostVO> queryPage = query.build();
        QueryWrapper<FeedPostVO> wrapper = Wrappers.query();
        wrapper.eq("fp.type", FeedPost.TYPE_CLUB);
        wrapper.orderByDesc("fp.create_time");
        Long userId = null;
        if(LoginHelper.isLogin()){
            userId = LoginHelper.getUserId();
        }
        Page<FeedPostVO> page = baseMapper.queryClubPost(queryPage, wrapper, userId);
        return TableDataInfo.build(page);
    }

    @Override
    public List<FeedPostNotificationVO> queryNotificationList(FeedPostNotificationQuery query) {
        long userId = LoginHelper.getUserId();
        LambdaQueryWrapper<FeedPostNotification> lqw = Wrappers.lambdaQuery();
        lqw.eq(ObjectUtil.isNotNull(query.getType()),FeedPostNotification::getType, query.getType());
        lqw.eq(FeedPostNotification::getReceiverId, userId);
        lqw.eq(ObjectUtil.isNotNull(query.getStatus()), FeedPostNotification::getStatus, query.getStatus());
        lqw.orderByDesc(FeedPostNotification::getCreateTime);
        List<FeedPostNotification> notifications = feedPostNotificationService.list(lqw);
        //设置为已读
        notifications.stream().map(FeedPostNotification::getNotificationId).forEach(this::notificationRead);
        return this.convertNotificationHandler(notifications);
    }

    @Override
    public Long queryNotificationNum(){
        long userId = LoginHelper.getUserId();
        LambdaQueryWrapper<FeedPostNotification> lqw = Wrappers.lambdaQuery();
        lqw.eq(FeedPostNotification::getStatus, false);
        lqw.eq(FeedPostNotification::getReceiverId, userId);
        return feedPostNotificationService.count(lqw);
    }

    private List<FeedPostNotificationVO> convertNotificationHandler(List<FeedPostNotification> notificationList){
        if(CollectionUtils.isEmpty(notificationList)){
            return List.of();
        }
        Set<Long> postIds = notificationList.stream()
            .map(FeedPostNotification::getPostId).collect(Collectors.toSet());
        List<Long> userIds = notificationList.stream()
            .flatMap(notification -> Stream.of(notification.getSenderId(), notification.getReceiverId()))
            .distinct()
            .toList();
        Map<Long, FeedPostVO> postMap = this.getMapByPostIds(postIds);
        Map<Long, WechatUserVO> userMap = wechatUserService.queryMapByIds(userIds);
        return notificationList.stream().map(notification -> {
            FeedPostNotificationVO vo = MapstructUtils.convert(notification, FeedPostNotificationVO.class);
            FeedPostVO post = postMap.getOrDefault(notification.getPostId(), new FeedPostVO());
            WechatUserVO sender = userMap.getOrDefault(notification.getSenderId(), new WechatUserVO());
            WechatUserVO receiver = userMap.getOrDefault(notification.getReceiverId(), new WechatUserVO());
            vo.setNotificationId(notification.getNotificationId());
            vo.setPostContent(post.getContent());
            vo.setPostPhotos(post.getContentPhotos());
            vo.setMessage(notification.getMessage());
            vo.setAuthor(receiver.getNickname());
            vo.setAuthorAvatar(receiver.getAvatar());
            vo.setNickname(sender.getNickname());
            vo.setAvatar(sender.getAvatar());
            vo.setHisTimeStr(DateUtils.formatTime(notification.getCreateTime()));
            return vo;
        }).toList();
    }

    public Map<Long, FeedPostVO> getMapByPostIds(Set<Long> postIds){
        if(CollectionUtils.isEmpty(postIds)){
            return Map.of();
        }
        LambdaQueryWrapper<FeedPost> lqw = Wrappers.lambdaQuery();
        lqw.in(FeedPost::getPostId, postIds);
        List<FeedPostVO> feedPostList = baseMapper.selectVoList(lqw);
        return feedPostList
            .stream()
            .collect(Collectors.toMap(FeedPostVO::getPostId, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean notificationRead(Long notificationId) {
        return feedPostNotificationService.lambdaUpdate()
            .eq(FeedPostNotification::getNotificationId, notificationId)
            .set(FeedPostNotification::getStatus, true)
            .update();
    }

    @Override
    public FeedPostVO getInfo(Long postId) {
//        Long userId = LoginHelper.getUserId();
        return getDetail(postId);
    }

    @Override
    public TableDataInfo<FeedPostVO> queryUserPage(FeedPostQuery query) {
        Long currentUserId = LoginHelper.getUserId();
        QueryWrapper<FeedPostVO> qw = Wrappers.query();
        qw.eq("fp.author_id", query.getUserId());
        qw.eq("fp.suggest", UgcSuggestEnum.PASS.getSuggest());
        qw.orderByDesc("fp.create_time");
        Page<FeedPostVO> page = baseMapper.getFeedPostPage(query.build(), qw, currentUserId);
        return TableDataInfo.build(page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean share(Long postId) {
        Long userId = LoginHelper.getUserId();
        LambdaQueryWrapper<FeedPostShare> lqw = Wrappers.lambdaQuery();
        lqw.eq(FeedPostShare::getPostId, postId);
        lqw.eq(FeedPostShare::getUserId, userId);
        long count = feedPostShareService.count(lqw);
        if(count > 0){
            return true;
        }
        FeedPostShare feedPostShare = new FeedPostShare();
        feedPostShare.setPostId(postId);
        feedPostShare.setUserId(userId);
        feedPostShareService.save(feedPostShare);
        return this.lambdaUpdate().eq(FeedPost::getPostId, postId)
            .setSql("share_count = share_count + 1")
            .update();
    }

    @Override
    public Boolean favorite(Long postId) {
        Long userId = LoginHelper.getUserId();
        LambdaQueryWrapper<FeedPostFavorite> lqw = Wrappers.lambdaQuery();
        lqw.eq(FeedPostFavorite::getPostId, postId);
        lqw.eq(FeedPostFavorite::getUserId, userId);
        long count = feedPostFavoriteService.count(lqw);
        if(count > 0L){
            this.lambdaUpdate().eq(FeedPost::getPostId, postId)
                .setSql("favorite_count = favorite_count - 1")
                .update();
            return feedPostFavoriteService.remove(lqw);
        }
        FeedPostFavorite feedPostFavorite = new FeedPostFavorite();
        feedPostFavorite.setPostId(postId);
        feedPostFavorite.setUserId(userId);
        feedPostFavoriteService.save(feedPostFavorite);
        return this.lambdaUpdate().eq(FeedPost::getPostId, postId)
            .setSql("favorite_count = favorite_count + 1")
            .update();
    }

    @Override
    public TableDataInfo<FeedPostVO> queryFavoriteFeedPostPage(PageQuery query) {
        LambdaQueryWrapper<FeedPostFavorite> lqw = Wrappers.lambdaQuery();
        lqw.eq(FeedPostFavorite::getUserId, LoginHelper.getUserId());
        Page<FeedPostFavorite> page = feedPostFavoriteService.page(query.build(),lqw);
        List<FeedPostFavorite> records = page.getRecords();
        if(CollectionUtils.isEmpty(records)){
            return TableDataInfo.build();
        }
        Set<Long> postIds = records.stream().map(FeedPostFavorite::getPostId).collect(Collectors.toSet());
        LambdaQueryWrapper<FeedPost> feedPostLqw = Wrappers.lambdaQuery();
        feedPostLqw.in(FeedPost::getPostId, postIds);
        feedPostLqw.eq(FeedPost::getSuggest, UgcSuggestEnum.PASS.getSuggest());
        List<FeedPost> feedPostList = baseMapper.selectList(feedPostLqw);
        return TableDataInfo.build(this.convertHandler(feedPostList), page.getTotal());
    }

    @Override
    public FeedPostHistoryPageVO queryHistoryPage(PageQuery query) {
        Page<FeedPost> page = baseMapper
            .selectPage(query.build(), Wrappers.<FeedPost>lambdaQuery()
                .eq(FeedPost::getAuthorId, LoginHelper.getUserId())
                .orderByDesc(FeedPost::getCreateTime));
        TableDataInfo<FeedPostVO> dataInfo = this.convertHandler(page);
        List<FeedPostVO> rows = dataInfo.getRows();
        Map<String, List<FeedPostVO>> map = rows.stream().collect(Collectors.groupingBy(this::groupingByYear));
        List<FeedPostHistoryVO> result = new ArrayList<>();
        map.forEach((k,v)->{
            FeedPostHistoryVO feedPostHistoryVO = new FeedPostHistoryVO();
            feedPostHistoryVO.setYear(k);
            feedPostHistoryVO.setFeedPosts(v);
            result.add(feedPostHistoryVO);
        });
        FeedPostHistoryPageVO feedPostHistoryPageVO = new FeedPostHistoryPageVO();
        feedPostHistoryPageVO.setCurrentPage(page.getCurrent());
        feedPostHistoryPageVO.setTotal(page.getTotal());
        feedPostHistoryPageVO.setData(result);
        return feedPostHistoryPageVO;
    }

    private String groupingByYear(FeedPostVO feedPost) {
        return DateUtil.format(feedPost.getCreateTime(), "yyyy年");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(Long postId) {
        FeedPost feedPost = baseMapper.selectById(postId);
        if (feedPost == null) {
            throw new ServiceException("未找到该动态，请检查是否已删除！");
        }
//        if (!feedPost.getAuthorId().equals(LoginHelper.getUserId())&& !StpUtil.hasRoleOr("SALES_MANAGER", "BOSS","ADMIN")) {
//            throw new ServiceException("sorry，只允许删除自己发布的动态！");
//        }
        //删除动态
        LambdaQueryWrapper<FeedPost> lqw = Wrappers.lambdaQuery();
        lqw.eq(FeedPost::getPostId, postId);
        baseMapper.delete(lqw);
        //删除动态图片
        LambdaQueryWrapper<FeedPostPhotos> photosLqw = Wrappers.lambdaQuery();
        photosLqw.eq(FeedPostPhotos::getPostId, postId);
        feedPostPhotosService.remove(photosLqw);
        //删除动态视频
        LambdaQueryWrapper<FeedPostVideos> videosLqw = Wrappers.lambdaQuery();
        videosLqw.eq(FeedPostVideos::getPostId, postId);
        feedPostVideosService.remove(videosLqw);
        //删除动态评论
        LambdaQueryWrapper<FeedPostComment> commentLqw = Wrappers.lambdaQuery();
        commentLqw.eq(FeedPostComment::getPostId, postId);
        feedPostCommentService.remove(commentLqw);
        //删除用户已经收藏的动态
        LambdaQueryWrapper<FeedPostFavorite> favoriteLqw = Wrappers.lambdaQuery();
        favoriteLqw.eq(FeedPostFavorite::getPostId, postId);
        feedPostFavoriteService.remove(favoriteLqw);
        //删除点赞
        LambdaQueryWrapper<FeedPostLinks> linksLqw = Wrappers.lambdaQuery();
        linksLqw.eq(FeedPostLinks::getPostId, postId);
        feedPostLinksService.remove(linksLqw);
        //删除通知
        LambdaQueryWrapper<FeedPostNotification> notificationLqw = Wrappers.lambdaQuery();
        notificationLqw.eq(FeedPostNotification::getPostId, postId);
        feedPostNotificationService.remove(notificationLqw);
        //删除分享
        LambdaQueryWrapper<FeedPostShare> shareLqw = Wrappers.lambdaQuery();
        shareLqw.eq(FeedPostShare::getPostId, postId);
        feedPostShareService.remove(shareLqw);
        //删除服务过程
        LambdaQueryWrapper<ServiceProcedureSteps> stepLqw = Wrappers.lambdaQuery();
        stepLqw.eq(ServiceProcedureSteps::getPostId, postId);
        serviceProcedureStepsService.remove(stepLqw);
        //删除精选动态
        LambdaQueryWrapper<FeedPostFeatured> featuredLqw = Wrappers.lambdaQuery();
        featuredLqw.eq(FeedPostFeatured::getFeedPostId, postId);
        feedPostFeaturedService.remove(featuredLqw);
        return true;
    }

    @Override
    public TableDataInfo<FeedPostVO> queryRoomPage(FeedPostQuery query) {
        RoomVO room = roomsService.getRoomInfo(query.getRoomId());
        if(ObjectUtil.isNull(room)){
            return TableDataInfo.build();
        }
        Long customerId = room.getCustomerId();
        List<String> staffIds = room.getServiceStaffIds();
        Set<Long> authorIds = new HashSet<>();
        if(CollectionUtils.isNotEmpty(staffIds)){
            LambdaQueryWrapper<Staff> staffLqw = Wrappers.lambdaQuery();
            staffLqw.in(Staff::getStaffId, staffIds);
            List<Staff> staffList = staffService.list(staffLqw);
            Set<Long> staffUserIds = staffList.stream().map(Staff::getUserId).collect(Collectors.toSet());
            authorIds.addAll(staffUserIds);
        }
        CustomerVO customerVO = customersService.getByCustomerId(customerId);
        if(ObjectUtil.isNotNull(customerVO)){
            authorIds.add(customerVO.getUserId());
        }
        LambdaQueryWrapper<FeedPost> lqw = Wrappers.lambdaQuery();
        lqw.in(FeedPost::getAuthorId, authorIds);
        lqw.orderByDesc(FeedPost::getCreateTime);
        lqw.eq(FeedPost::getSuggest, UgcSuggestEnum.PASS.getSuggest());
        Page<FeedPost> page = baseMapper.selectPage(query.build(), lqw);
        return this.convertHandler(page);
    }

    @Override
    public TableDataInfo<FeedPostVO> queryUserPost(){
        LambdaQueryWrapper<FeedPost> lqw = Wrappers.lambdaQuery();
        lqw.eq(FeedPost::getSuggest, UgcSuggestEnum.PASS.getSuggest());
        lqw.orderByDesc(FeedPost::getLikesCount,FeedPost::getCommentsCount,
            FeedPost::getFavoriteCount, FeedPost::getShareCount);
        Page<FeedPost> page = baseMapper.selectPage(new Page<>(1, 1),lqw);
        return this.convertHandler(page);
    }

    private TableDataInfo<FeedPostVO> convertHandler(Page<FeedPost> page){
        List<FeedPost> records = page.getRecords();
        List<FeedPostVO> result = this.convertHandler(records);
        return TableDataInfo.build(result, page.getTotal());
    }

    private List<FeedPostVO> convertHandler(List<FeedPost> feedPosts){
        if(CollectionUtils.isEmpty(feedPosts)){
            return List.of();
        }

//        Long loginUserId = LoginHelper.getUserId();

        List<Long> authorIds = feedPosts.stream().map(FeedPost::getAuthorId).toList();
        Set<Long> postSet = feedPosts.stream().map(FeedPost::getPostId).collect(Collectors.toSet());

        Set<Long> customerUserIds = feedPosts.stream().map(FeedPost::getAuthorId).collect(Collectors.toSet());

        Map<Long, WechatUserVO> authorMap = wechatUserService.queryMapByIds(authorIds);
        Map<Long, StaffVO> staffMap = staffService.getMapByUserIds(authorIds);
        Map<Long, List<FeedPostCommentVO>> commentGroupMap = feedPostCommentService.getGroupMap(postSet);
        Map<Long, FeedPostLinks> likeMap = feedPostLinksService.getCurrentUserMapByPostIds(postSet);
        Map<Long, FeedPostFavorite> favoriteMap = feedPostFavoriteService.getCurrentUserFavoriteMap(postSet);
        Map<Long, FeedPostFeatured> dynamicSelectedMap = feedPostFeaturedService.isDynamicSelectedList(postSet);
        Map<Long, List<FeedPostVO.LikeUsers>> likeUserMap = feedPostLinksService.getLikeUsers(postSet);
        Map<Long, List<String>> photosMap = feedPostPhotosService.getPhotosByPostIds(postSet);
        Map<Long, List<String>> videosMap = feedPostVideosService.getVideosByPostIds(postSet);
        Map<Long, CustomerVO> customerMap = customersService.getMapByUserIds(customerUserIds);
        Map<Long, ServiceProcedureSteps> procedureStepsMap = serviceProcedureStepsService.getByPostIds(postSet);
        List<Long> customerStepsList = serviceProcedureStepsService.getCustomerIdsByPostIds(postSet);
        Map<Long, Rooms> roomsMap = roomsService.getByCustomerIds(customerStepsList);

        return feedPosts.stream().map(feedPost->{
            FeedPostVO vo = new FeedPostVO();
            Date createTime = feedPost.getCreateTime();
            vo.setPostId(feedPost.getPostId());
            //用户
            WechatUserVO author = authorMap.getOrDefault(feedPost.getAuthorId(), new WechatUserVO());
            vo.setNickname(author.getNickname());
            vo.setAvatar(author.getAvatar());
            vo.setContent(feedPost.getContent());
            vo.setContentPhotos(feedPost.getContentPhotos());
            vo.setCreateTime(createTime);
            vo.setHisTimeStr(DateUtils.formatTime(createTime));
            List<FeedPostCommentVO> comments = commentGroupMap.getOrDefault(feedPost.getPostId(), List.of());
            //评论列表
            if("CLUB".equals(feedPost.getType())){
                //会所动态只能自己查看自己评论
//                comments = comments.parallelStream()
//                    .filter(comment -> comment.getUserId().equals(loginUserId)).toList();
            }
            if("USER".equals(feedPost.getType())){
                // 客户入住天数
                CustomerVO customer = customerMap.getOrDefault(feedPost.getAuthorId(), null);
                if(ObjectUtil.isNotNull(customer)){
                    RoomVO room = roomsService.getByCustomerId(customer.getCustomerId());
                    if(ObjectUtil.isNotNull(room)){
                        Date checkinDate = room.getCheckinDate();
                        if(ObjectUtil.isNotNull(checkinDate) && ObjectUtil.isNotNull(createTime)){
                            long liveDays = DateUtil.betweenDay(checkinDate, createTime, true);
                            vo.setLiveDays(liveDays);
                        }
                    }
                }
            }
            if("STAFF".equals(feedPost.getType())){
                //员工职位
                StaffVO staff = staffMap.getOrDefault(feedPost.getAuthorId(), new StaffVO());
                vo.setStaffPost(staff.getStaffPost());
                FeedPostVO.EmployeeInfo staffInfo = new FeedPostVO.EmployeeInfo();
                staffInfo.setTags(staff.getTag());
                staffInfo.setServiceNum(staff.getServiceNum());
                staffInfo.setPracticeTime(staff.getPracticeTime());
                staffInfo.setStaffPost(staff.getStaffPost());
                Date practiceTime = staff.getPracticeTime();
                if(ObjectUtil.isNotNull(practiceTime)){
                    Long betweenYear = DateUtil.betweenYear(practiceTime, DateUtil.date(), true);
                    staffInfo.setYearsEmployment(String.format("%s年", betweenYear));
                }
                vo.setStaffInfo(staffInfo);

                // 服务天数
                ServiceProcedureSteps serviceProcedureSteps = procedureStepsMap
                        .getOrDefault(feedPost.getPostId(), new ServiceProcedureSteps());
                Long customerId = serviceProcedureSteps.getCustomerId();
                Rooms room = roomsMap.getOrDefault(customerId, new Rooms());
                if(ObjectUtil.isNotNull(room)){
                    Date checkinDate = room.getCheckinDate();
                    if(ObjectUtil.isNotNull(checkinDate) && ObjectUtil.isNotNull(createTime)){
                        long serviceDays = DateUtil.betweenDay(checkinDate, createTime, true);
                        vo.setServiceDays(serviceDays);
                    }
                }
            }
            vo.setComments(comments);

            //点赞列表
            List<FeedPostVO.LikeUsers> likeUsers = likeUserMap.getOrDefault(feedPost.getPostId(), List.of());
            vo.setLikeUsers(likeUsers);
            //点赞
            FeedPostLinks isLike = likeMap.get(feedPost.getPostId());
            vo.setIsLike(ObjectUtil.isNotNull(isLike));
            //收藏
            FeedPostFavorite isFavorite = favoriteMap.get(feedPost.getPostId());
            vo.setIsFavorite(ObjectUtil.isNotNull(isFavorite));
            vo.setVideos(feedPost.getVideos());
            vo.setType(feedPost.getType());
            vo.setLikesCount(feedPost.getLikesCount());
            vo.setFavoriteCount(feedPost.getFavoriteCount());
            vo.setShareCount(feedPost.getShareCount());
            //是否精选
            FeedPostFeatured isFeatured = dynamicSelectedMap.get(feedPost.getPostId());
            vo.setIsFeatured(ObjectUtil.isNotNull(isFeatured));
            vo.setUserId(feedPost.getAuthorId());

            //图片
            List<String> photos = photosMap.get(feedPost.getPostId());
            vo.setContentPhotos(photos);
            //视频
            List<String> videos = videosMap.get(feedPost.getPostId());
            vo.setVideos(videos);

            return vo;
        }).toList();
    }

    /**
     * 发送消息通知
     * @param type 通知类型
     * @param postId 动态id
     * @param message 消息
     */
    private void notificationHandler(String type, Long postId,Long commentId, String message){
        FeedPost feedPost = this.getById(postId);
        if(ObjectUtil.isNull(feedPost)){
            return;
        }
        long senderId = LoginHelper.getUserId();
        long receiverId = feedPost.getAuthorId();

        FeedPostNotification notification = new FeedPostNotification();
        notification.setPostId(postId);
        notification.setMessage(message);
        notification.setStatus(false);
        notification.setCreateTime(new Date());
        notification.setType(type);
        notification.setSenderId(senderId);
        notification.setReceiverId(receiverId);
        notification.setCommentId(commentId);
        feedPostNotificationService.save(notification);
    }

    /**
     * 根据用户id获取当前为自己服务的工作人员id
     * @param  uid 用户id
     * @return
     */
    private List<Long> getStaffIds(Long uid){
        //客户可查看为自己的服务人员动态
        CustomerVO customer = customersService.getByUserId(uid);
        if(ObjectUtil.isNull(customer)){
            throw new ServiceException("用户未绑定客户信息, 请绑定客户！");
        }
        RoomVO room = roomsService.getByCustomerId(customer.getCustomerId());
        if(ObjectUtil.isNull(room)){
            throw new ServiceException("用户未绑定房间信息, 请绑定房间！");
        }
        List<String> serviceStaffIds = room.getServiceStaffIds();
        if(CollectionUtils.isEmpty(serviceStaffIds)){
            throw new ServiceException("用户入住的房间暂未绑定服务人员, 请绑定服务人员！");
        }
        List<Long> result = staffService.lambdaQuery()
            .in(Staff::getStaffId, room.getServiceStaffIds())
            .list()
            .stream()
            .map(Staff::getUserId)
            .distinct()
            .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(result)){
            result.add(-1L);
        }
        return result;
    }

    @Override
    public TableDataInfo<FeedPostVO> queryCustomerServiceStep(CustomerServiceStepQuery query){

        LambdaQueryWrapper<FeedPost> lqw = Wrappers.lambdaQuery();

        if(ObjectUtil.isNotNull(query.getStartDate()) && ObjectUtil.isNotNull(query.getEndDate())){
            lqw.between(FeedPost::getCreateTime, DateUtil.beginOfDay(query.getStartDate()),
                    DateUtil.endOfDay(query.getEndDate()));
        }
        lqw.and(wrapper-> {
            wrapper.eq(FeedPost::getAuthorId, query.getUserId());
            List<Long> postIds = serviceProcedureStepsService.queryCustomerServiceStepPostIds(query);
            if(CollUtil.isNotEmpty(postIds)){
                wrapper.or().in(FeedPost::getPostId, postIds);
            }
        });
        lqw.eq(FeedPost::getSuggest, UgcSuggestEnum.PASS.getSuggest());
        lqw.orderByDesc(FeedPost::getCreateTime);
        Page<FeedPost> page = baseMapper.selectPage(query.build(), lqw);
        List<FeedPost> records = page.getRecords();
        return TableDataInfo.build(convertHandler(records), page.getTotal());
    }

    @Override
    public Boolean importFeedPost(List<FeedPostExportVO> feedPostExportList) {
        if(CollectionUtils.isEmpty(feedPostExportList)){
            return false;
        }
        for (FeedPostExportVO export : feedPostExportList) {
            Long staffUserId = export.getStaffUserId();
            StaffVO staff = staffService.getByUserId(staffUserId);
            if(ObjectUtil.isNull(staff)){
                continue;
            }
            Customers customer = customersService.getById(export.getCustomerId());
            if(ObjectUtil.isNull(customer)){
                continue;
            }

            DateTime parse = DateUtil.parse(export.getDate(), "yyyy-MM-dd HH:mm");
            FeedPost feedPost = new FeedPost();
            feedPost.setContent(export.getContent());
            feedPost.setAuthorId(staff.getUserId());
            feedPost.setType(FeedPost.TYPE_STAFF);
            feedPost.setLikesCount(0);
            feedPost.setCommentsCount(0);
            String images = export.getImages();
            List<String> imageList = StrUtil.split(images, ",");
            feedPost.setContentPhotos(imageList);
            String videos = export.getVideos();
            List<String> videoList = StrUtil.split(videos, ",");
            feedPost.setVideos(videoList);
            feedPost.setFavoriteCount(0);
            feedPost.setShareCount(0);
            feedPost.setCreateTime(parse);
            save(feedPost);

            ServiceProcedureSteps serviceProcedureSteps = new ServiceProcedureSteps();
            serviceProcedureSteps.setCustomerId(customer.getCustomerId());
            serviceProcedureSteps.setTaskNodeId(export.getTakNodeId());
            serviceProcedureSteps.setPostId(feedPost.getPostId());
            serviceProcedureSteps.setStaffId(staff.getStaffId());
            serviceProcedureSteps.setPublishedAt(parse);
            serviceProcedureStepsService.save(serviceProcedureSteps);
        }
        return true;
    }

    @Override
    public TableDataInfo<FeedPostVO> queryPcPage(FeedPostPageQuery query) {
        LambdaQueryWrapper<FeedPost> lqw = Wrappers.lambdaQuery();
        lqw.eq(ObjectUtil.isNotNull(query.getAuthorId()), FeedPost::getAuthorId, query.getAuthorId());
        lqw.eq(ObjectUtil.isNotNull(query.getPostType()), FeedPost::getType, query.getPostType());

        if(ObjectUtil.isNotNull(query.getIsAttachments())){
            Boolean isAttachments = query.getIsAttachments();
            if(isAttachments){
                lqw.isNotNull(FeedPost::getContentPhotos);
            }else {
                lqw.isNull(FeedPost::getContentPhotos);
            }
        }
        if(ObjectUtil.isNotNull(query.getIsFeatured())){
            String sql = "select 1 from biz_feed_post_featured where feed_post_id = biz_feed_post.post_id";
            if(query.getIsFeatured()){
                lqw.exists(sql);
            }else {
                lqw.notExists(sql);
            }
        }
        if(ObjectUtil.isNotNull(query.getTaskNodeId())){
            String sql = "select 1 from biz_service_procedure_steps where post_id = biz_feed_post.post_id and task_node_id = " + query.getTaskNodeId();
            lqw.exists(sql);
        }
        lqw.orderByDesc(FeedPost::getCreateTime);
        Page<FeedPost> feedPostPage = baseMapper.selectPage(query.build(), lqw);
        return this.convertHandler(feedPostPage);
    }

    @Override
    public TableDataInfo<FeedPostVO> queryCustomerPage(PageQuery query,String type,Long taskNodeId) {
        LambdaQueryWrapper<FeedPost> lqwPost = Wrappers.lambdaQuery();
        List<FeedPostVO> feedPostVOS = baseMapper.selectVoList(lqwPost);
        List<Long> postIds = feedPostVOS.stream().map(FeedPostVO::getPostId).toList();
        LambdaQueryWrapper<FeedPost> lqw = Wrappers.lambdaQuery();
        if(!"ALL".equals(type)){
            lqw.eq(FeedPost::getType, type);
        }
        if (!postIds.isEmpty() && postIds.size()>0) {
            lqw.in(FeedPost::getPostId, postIds);
        }
        lqw.orderByDesc(FeedPost::getCreateTime);
        lqw.eq(FeedPost::getSuggest, UgcSuggestEnum.PASS.getSuggest());
        Page<FeedPost> page = page(query.build(), lqw);
        return this.convertHandler(page);
    }

    @Override
    public Boolean featuredFeedPost(Long postId, Boolean featuredStatus) {
        if(featuredStatus){
            Boolean dynamicSelected = feedPostFeaturedService.isDynamicSelected(postId);
            if(dynamicSelected){
                return true;
            }
            return feedPostFeaturedService.save(postId);
        }else{
            return feedPostFeaturedService.cancel(postId);
        }
    }

    @Override
    public List<FeedPostVO> queryFeaturedList(Integer limit) {
        LambdaQueryWrapper<FeedPost> lqw = Wrappers.lambdaQuery();
        lqw.exists("select 1 from biz_feed_post_featured where feed_post_id = biz_feed_post.post_id");
        lqw.last("limit " + limit);
        lqw.eq(FeedPost::getSuggest, UgcSuggestEnum.PASS.getSuggest());
        List<FeedPost> feedPosts = list(lqw);
        return convertHandler(feedPosts);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean remove(Long[] postIds) {
        if(ArrayUtil.isEmpty(postIds)){
            return false;
        }
        for (Long postId : postIds) {
            remove(postId);
        }
        return true;
    }

    @Override
    public List<FeedPostVO> queryFeaturedNodeList(Integer limit){
        return baseMapper.queryFeaturedNodeList(limit);
    }

    @Override
    public TableDataInfo<FeedPostVO> queryGrowGrassPage(FeedPostQuery query){
        Long userId = LoginHelper.getUserId();
        //我的页面入参值
        if (ObjectUtil.isNotNull(query.getCustomerId())){
            query.setUserId(userId);
            query.setCustomerId(null);
        }
        QueryWrapper<FeedPostVO> wrapper = buildQueryWrapper(query);
        wrapper.eq("fp.type", "USER").or().eq("fp.type", "CUSTOMER");
        Page<FeedPostVO> page = baseMapper.getFeedPostPage(query.build(), wrapper, userId);
        List<FeedPostVO> records = page.getRecords();
        records.forEach(this::handlerOtherConvert);
        return TableDataInfo.build(page);
    }
    @Override
    public TableDataInfo<FeedPostVO> queryMyGrowGrassPage(FeedPostQuery query){
        Long userId = LoginHelper.getUserId();
        //我的页面入参值
        if (ObjectUtil.isNotNull(query.getCustomerId())){
            query.setUserId(userId);
            query.setCustomerId(null);
        }
        QueryWrapper<FeedPostVO> wrapper = buildQueryWrapper(query);
        Page<FeedPostVO> page = baseMapper.getFeedPostPage(query.build(), wrapper, userId);
        List<FeedPostVO> records = page.getRecords();
        records.forEach(this::handlerOtherConvert);
        return TableDataInfo.build(page);
    }

    @Override
    public Boolean createReminder(RemindersFeedPostParams feedPostParams) {
        List<WechatUserVO>  users =  wechatUserService.selectAllMom();
        if (users == null || users.isEmpty()) {
            return false;
        }
        String datePart = feedPostParams.getDate();
        String timePart = feedPostParams.getTime();
        Date parse;
        try{
            // 获取当前年份
            int currentYear = Calendar.getInstance().get(Calendar.YEAR);
            Calendar dateCalendar = wechatUserService.getCalendar(datePart, currentYear, timePart);
            parse = dateCalendar.getTime();
        }catch (Exception e){
            e.printStackTrace();
            throw new ServiceException("日期时间格式不正确");
        }
        FeedPostVO feedPost = getDetail(feedPostParams.getPostId());
        if(ObjectUtil.isNull(feedPost)){
            throw new ServiceException("动态不存在,无法创建提醒事项。");
        }
        List<BizFeedPostReminders>  remindersList = new ArrayList<>();
        for (WechatUserVO user : users) {
            LambdaQueryWrapper<BizFeedPostReminders> lqw = Wrappers.lambdaQuery();
            lqw.eq(BizFeedPostReminders::getPostId, feedPostParams.getPostId());
            lqw.eq(BizFeedPostReminders::getStatus, false);
            lqw.eq(BizFeedPostReminders::getUserId, user.getUserId());
            long count = remindersService.count(lqw);
            if(count > 0L){
                continue;
            }
            BizFeedPostReminders reminders = new BizFeedPostReminders();
            reminders.setPostId(feedPost.getPostId());
            reminders.setReminderDate(parse);
            reminders.setUserId(user.getUserId());
            reminders.setOpenid(user.getOpenid());
            reminders.setAppid(user.getAppid());
            reminders.setDescription(feedPost.getContent());
            reminders.setTitle(StrUtil.format("{}，发布新动态啦！", feedPost.getNickname()));
            reminders.setStatus(false);
            remindersList.add(reminders);
        }

        remindersService.saveBatch(remindersList);
        LambdaQueryWrapper<BizFeedPostReminders> lqw = Wrappers.lambdaQuery();
        lqw.eq(BizFeedPostReminders::getPostId, feedPostParams.getPostId());
        remindersList =  remindersService.list(lqw);
        String pageUrl = switch (feedPostParams.getReceivingType()) {
            case "0" -> "pageA/dynamics?postId="+feedPostParams.getPostId();
            case "1" -> "pageA/pageB/community/sending?postId="+feedPostParams.getPostId();
            case "2" -> "pageA/pageB/community/staffdetail?postId="+feedPostParams.getPostId();
            default -> null;
        };
        handlerSetAlarm(parse,remindersList,pageUrl);
        return true;
    }

    private void handlerSetAlarm(Date time, List<BizFeedPostReminders> remindersList, String pageUrl) {
        LocalDateTime targetTime = DateUtil.toLocalDateTime(time);
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 计算当前时间和目标时间之间的差值（以秒为单位）
        long delay = Duration.between(now, targetTime).getSeconds();
        if (delay <= 0) {
            throw new ServiceException("目标时间已过，无法设置提醒!", 100012);
        }

        for (BizFeedPostReminders reminders : remindersList) {
            ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
            scheduler.schedule(() -> {
                if(ObjectUtil.isNotNull(reminders)){

                    // 微信通知
                    if (!wxMaService.switchover(reminders.getAppid())) {
                        throw new ServiceException(String.format("未找到对应appid=[%s]的配置，请核实！", reminders.getAppid()));
                    }
                    String time1 = DateUtil.format(reminders.getReminderDate(), "yyyy-MM-dd HH:mm");
                    String thing2 = null;
                    if (StringUtils.isNotEmpty(reminders.getDescription())){
                        thing2 = StringUtils.substring(reminders.getDescription(),0,10);
                    }else {
                        thing2 = "我们发布一条消息，请查收";
                    }
                    WxMaSubscribeMessage subscribeMessage = new WxMaSubscribeMessage();
                    subscribeMessage.setTemplateId("4ozrWQOn4Z0lxEfPNXyVzNaKCWaanMNYypzOu8PH9I0");
                    subscribeMessage.setToUser(reminders.getOpenid());
                    subscribeMessage.setPage(pageUrl);
                    List<WxMaSubscribeMessage.MsgData> msgData = new ArrayList<>();
                    msgData.add(new WxMaSubscribeMessage.MsgData("time3", time1));
                    msgData.add(new WxMaSubscribeMessage.MsgData("thing4", reminders.getTitle()));
                    msgData.add(new WxMaSubscribeMessage.MsgData("thing5", thing2));
                    subscribeMessage.setData(msgData);
                    try {
                        wxMaService.getMsgService().sendSubscribeMsg(subscribeMessage);
                    } catch (Exception e) {
                        log.error("微信模版订阅消息推送失败", e);
                    }
                    // 更新提醒状态
                    LambdaUpdateWrapper<BizFeedPostReminders> luw = Wrappers.lambdaUpdate();
                    luw.eq(BizFeedPostReminders::getId, reminders.getId());
                    luw.set(BizFeedPostReminders::getStatus, true);
                    remindersService.update(luw);
                }
            }, delay, TimeUnit.SECONDS);
            scheduler.shutdown();
        }
    }

    @Override
    public List<CustomerVO> queryGoodMomList() {
        LambdaQueryWrapper<FeedPost> lqw = Wrappers.lambdaQuery();
        lqw.exists("select 1 from biz_feed_post_featured where feed_post_id = biz_feed_post.post_id");
        lqw.eq(FeedPost::getSuggest, UgcSuggestEnum.PASS.getSuggest());
        lqw.eq(FeedPost::getType,"USER");
        List<FeedPost> feedPosts = list(lqw);
//        Set<Long> postList = new HashSet<>(feedPosts.stream().map(FeedPost::getPostId).toList());
        List<Long> userList = new ArrayList<>(feedPosts.stream().map(FeedPost::getAuthorId).toList());
        List<CustomerVO> customerList = null;
        if (!userList.isEmpty()){

            customerList = customersService.getCustomerListByUserIds(userList);
            List<Long> customerIds =  new ArrayList<>(customerList.stream().map(CustomerVO::getCustomerId).toList());
            customerList = customersService.getCustomerList(customerIds);
        }

        return customerList;
    }

    @Override
    public List<Rooms> queryUserByRooms() {
        Long userId = LoginHelper.getUserId();
        if(StpUtil.hasRole(AppUserRoleEnum.CUSTOMER.name())){
            // 客户
            CustomerVO customer = customersService.getByUserId(userId);
            Rooms rooms = new Rooms();
            rooms.setRoomId(customer.getRoomId());
            rooms.setRoomNumber(customer.getRoomNumber());
            return CollUtil.newArrayList(rooms);
        }
        if(StpUtil.hasRoleOr(AppUserRoleEnum.NURSE.name(), AppUserRoleEnum.POSTPARTUM.name())){
            return employeeService.getEmployeeRoomsByUserId(userId);
        }
        // 返回所有房间
        return roomsService.list();
    }

    @Override
    public TableDataInfo<FeedPostVO> getFeedPostPage(FeedPostQuery query){
        Long userId = LoginHelper.getUserId();
        QueryWrapper<FeedPostVO> wrapper = buildQueryWrapper(query);
        if(StrUtil.isBlank(query.getPostType())){
            wrapper.ne("fp.type", "CLUB");
        }
        //后台不过滤
        if (StrUtil.isNotBlank(query.getIsAsc()) && query.getIsAsc().equals("platform")){
            query.setIsAsc(null);
        }else{
            wrapper.notIn("fp.type", "USER");
            wrapper.notIn("fp.type", "CUSTOMER");
        }
        Page<FeedPostVO> page = baseMapper.getFeedPostPage(query.build(), wrapper, userId);
        List<FeedPostVO> records = page.getRecords();
        records.forEach(this::handlerOtherConvert);
        return TableDataInfo.build(page);
    }


    @Override
    public List<WechatUserVO> getByRoleName(String roleName) {

        List<WechatUserVO> userlist = new ArrayList<>();
        //客户
        if (roleName.equals(AppUserRoleEnum.CUSTOMER.name())){
            LambdaQueryWrapper<RoomOccupations> qwOcc = new LambdaQueryWrapper<>();
            qwOcc.eq(RoomOccupations::getStatus,0);
            List<RoomOccupations> occList = occupationsService.list(qwOcc);
            List<Long> customerIds = occList.stream().map(RoomOccupations::getCustomerId).toList();
            LambdaQueryWrapper<Customers> qw = new LambdaQueryWrapper<>();
            qw.eq(Customers::getIsContract,true);
            qw.in(Customers::getCustomerId,customerIds);
            List<Customers> list = customersService.list(qw);
            WechatUserVO userVO = null;
            for (Customers customers : list) {
                userVO = new WechatUserVO();
                userVO.setUserId(customers.getUserId());
                userVO.setNickname(customers.getName());
                userlist.add(userVO);
            }
        }else {
            //查询角色权限
            WechatRole byCode = roleService.getByCode(roleName);
            //员工
            LambdaQueryWrapper<WechatUserRole> qwRole = new LambdaQueryWrapper<>();
            qwRole.eq(WechatUserRole::getRoleId,byCode.getId());
            List<WechatUserRole> list = wechatUserRoleService.list();
            List<Long>  userIdList = list.stream().map(WechatUserRole::getUserId).toList();
            userlist = wechatUserService.queryList(userIdList);

        }
        return userlist;
    }

    @Override
    public  List<RoomVO> getRoomsByUserId(Long userId) {
        List<RoomVO> roomVOList = new ArrayList<>();
        LambdaQueryWrapper<WechatUserRole> qwRole = new LambdaQueryWrapper<>();
        qwRole.eq(WechatUserRole::getUserId,userId);
        List<WechatUserRole> list = wechatUserRoleService.list(qwRole);
        if (null != list && !list.isEmpty()) {
            //客户
            if (list.get(0).getRoleId() == 5) {
                RoomVO byUserId = roomsService.getRoomByUserId(userId);
                roomVOList.add(byUserId);
            }else {
                //员工
                List<Rooms> employeeRoomsByUserId = employeeService.getEmployeeRoomsByUserId(userId);
                roomVOList = MapstructUtils.convert(employeeRoomsByUserId, RoomVO.class);
            }
        }
        return roomVOList;
    }

    private QueryWrapper<FeedPostVO> buildQueryWrapper(FeedPostQuery query){
        QueryWrapper<FeedPostVO> qw = Wrappers.query();
        if(CollUtil.isNotEmpty(query.getNodeTypeList())){
            qw.in("tn.nurse_type", query.getNodeTypeList());
        }
        if(CollUtil.isNotEmpty(query.getTaskNodeIdList())){
            qw.in("tn.task_node_id", query.getTaskNodeIdList());
        }
        if(ObjectUtil.isNotNull(query.getUserId())){
            qw.eq("u.user_id", query.getUserId());
        }
        if(ObjectUtil.isNotNull(query.getNodeId())){
            qw.eq("tn.task_node_id", query.getNodeId());
        }
        if(ObjectUtil.isNotNull(query.getNodeType())){
            qw.eq("tn.nurse_type", query.getNodeType());
        }

        if(StrUtil.isNotBlank(query.getPostType())){
            qw.eq("fp.type", query.getPostType());
        }

        if(ObjectUtil.isNotNull(query.getCustomerId())){
            qw.eq("ps.customer_id", query.getCustomerId());
        }

        if(ObjectUtil.isNotNull(query.getAuthorId())){
            qw.eq("fp.author_id", query.getAuthorId());
        }

        if(ObjectUtil.isNotNull(query.getIsAttachments())){
            Boolean isAttachments = query.getIsAttachments();
            if(isAttachments){
                qw.isNotNull("fp.content_photos");
            }else {
                qw.isNull("fp.content_photos");
            }
        }
        if(ObjectUtil.isNotNull(query.getWeekNum())){
            Date checkInTime = query.getCheckInTime();
            if(ObjectUtil.isNull(checkInTime)) throw new ServiceException("checkInTime is null");
            Date startTime;
            Date endTime;
            if(query.getWeekNum() == 1){
                startTime = DateUtil.beginOfDay(checkInTime);
                endTime = DateUtil.offsetDay(checkInTime, 7);
            }else {
                startTime = DateUtil.offsetWeek(checkInTime, query.getWeekNum());
                endTime = DateUtil.offsetDay(startTime, 7);
            }
            qw.between("fp.create_time", startTime, endTime);
        }
        if(ObjectUtil.isNotNull(query.getUserAndEmployee())){
            qw.and(w ->
                    w.eq("ps.target_user_id", query.getUserAndEmployee())
                            .or().eq("fp.author_id", query.getUserAndEmployee()));
        }

        if(ObjectUtil.isNotNull(query.getIsFeatured())){
            String sql = "select 1 from biz_feed_post_featured where feed_post_id = fp.post_id";
            if(query.getIsFeatured()){
                qw.exists(sql);
            }else {
                qw.notExists(sql);
            }
        }
        if(CollUtil.isNotEmpty(query.getNotPostIds())){
            qw.notIn("fp.post_id", query.getNotPostIds());
        }
        qw.eq("fp.suggest", UgcSuggestEnum.PASS.getSuggest());
        qw.groupBy("fp.post_id");
        qw.orderByDesc("is_featured", "fp.create_time");
        return qw;
    }

    @Override
    @Transactional(rollbackFor= Exception.class)
    public Boolean createFeedPost(CreateFeedPostBO createFeedPostBO){
        List<Long> config = createFeedPostBO.getConfig();
//        if(config.size() <= 1){
//            throw new ServiceException("动态发布人不能为空");
//        }
//        Long roomId = config.get(0);
//        Long userId = config.get(1);
//        Long nodeId = null;
//
//        if(config.size() == 3){
//            nodeId = config.get(2);
//        }

        Date parse = createFeedPostBO.getDate();
        FeedPost feedPost = new FeedPost();
        feedPost.setContent(createFeedPostBO.getContent());
        feedPost.setAuthorId(createFeedPostBO.getAuthorId());
        feedPost.setType(getFeedPostType(createFeedPostBO.getAuthorId()));
        feedPost.setLikesCount(0);
        feedPost.setCommentsCount(0);
        feedPost.setContentPhotos(createFeedPostBO.getContentPhotos());
        feedPost.setVideos(createFeedPostBO.getVideos());
        feedPost.setFavoriteCount(0);
        feedPost.setShareCount(0);
        feedPost.setCreateTime(parse);
        feedPost.setSuggest(UgcSuggestEnum.PASS.getSuggest());
        feedPost.setLabel(100);
        feedPost.setTitle(createFeedPostBO.getTitle());
        save(feedPost);

//        RoomDetailVO roomDetail = roomsService.getDetail(roomId);
        ServiceProcedureSteps serviceProcedureSteps = new ServiceProcedureSteps();
//        serviceProcedureSteps.setTargetUserId(roomDetail.getCustomerUserId());
//        serviceProcedureSteps.setCustomerId(roomDetail.getCustomerId());
//        serviceProcedureSteps.setRoomId(roomId);
        serviceProcedureSteps.setTaskNodeId(createFeedPostBO.getTaskNodeId());
        serviceProcedureSteps.setPostId(feedPost.getPostId());
        serviceProcedureSteps.setPublishedAt(DateUtil.date());
        serviceProcedureStepsService.save(serviceProcedureSteps);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateFeedPost(CreateFeedPostBO feedPostBO){
        Date parse = feedPostBO.getDate();
        FeedPost feedPost = new FeedPost();
        feedPost.setPostId(feedPostBO.getPostId());
        feedPost.setContent(feedPostBO.getContent());
        feedPost.setContentPhotos(feedPostBO.getContentPhotos());
        feedPost.setVideos(feedPostBO.getVideos());
        feedPost.setTitle(feedPostBO.getTitle());
        feedPost.setCreateTime(parse);
        feedPost.setAuthorId(feedPostBO.getAuthorId());
        updateById(feedPost);

        ServiceProcedureSteps serviceProcedureSteps = serviceProcedureStepsService.getByPostId(feedPost.getPostId());
        if (serviceProcedureSteps != null){
            serviceProcedureSteps.setTaskNodeId(feedPostBO.getTaskNodeId());
            serviceProcedureStepsService.updateById(serviceProcedureSteps);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createClubPost(CreateFeedPostBO createFeedPostBO) {
        Date parse = createFeedPostBO.getDate();
        FeedPost feedPost = new FeedPost();
        feedPost.setContent(createFeedPostBO.getContent());
        feedPost.setAuthorId(createFeedPostBO.getAuthorId());
        feedPost.setType("CLUB");
        feedPost.setLikesCount(0);
        feedPost.setCommentsCount(0);
        feedPost.setContentPhotos(createFeedPostBO.getContentPhotos());
        feedPost.setVideos(createFeedPostBO.getVideos());
        feedPost.setFavoriteCount(0);
        feedPost.setShareCount(0);
        feedPost.setCreateTime(parse);
        feedPost.setSuggest(UgcSuggestEnum.PASS.getSuggest());
        feedPost.setLabel(100);
        return save(feedPost);
    }

    @Override
    public FeedPostVO getDetail (Long postId){
        Long userId = LoginHelper.getUserId();
        FeedPostVO post =  baseMapper.getDetail(postId, userId);
        handlerOtherConvert(post);
        return post;
    }

    private void handlerOtherConvert(FeedPostVO post){
        Date createTime = post.getCreateTime();
        post.setHisTimeStr(DateUtils.formatTime(createTime));
        String type = post.getType();
        Date serviceTime = null;
            if(("USER".equals(type) || "CUSTOMER".equals(type)) && ObjectUtil.isNotNull(post.getCustomerInfo())){
                FeedPostVO.CustomerInfo customerInfo = post.getCustomerInfo();
                serviceTime = customerInfo.getCheckIn();
                //员工职位
                if(ObjectUtil.isNotNull(serviceTime)){
                    long serviceDays = DateUtil.betweenDay(serviceTime, post.getCreateTime(), true);
                    if (ObjectUtil.isNotNull(serviceDays) && serviceDays < 27) {
                        customerInfo.setCheckInDays(serviceDays + 1);
                    }
                    else {
                        customerInfo.setCheckInDays(28L);
                    }
                }
            }
            if("STAFF".equals(type) && ObjectUtil.isNotNull(post.getStaffInfo())) {
                FeedPostVO.EmployeeInfo staffInfo = post.getStaffInfo();
                if (staffInfo != null) {
                    serviceTime = staffInfo.getServiceTime();
                    //员工职位
                    if (ObjectUtil.isNotNull(serviceTime)) {
                        long serviceDays = DateUtil.betweenDay(serviceTime, post.getCreateTime(), true);
                        if (ObjectUtil.isNotNull(serviceDays) && serviceDays < 27) {
                            staffInfo.setServiceDays(serviceDays + 1);
                        } else {
                            staffInfo.setServiceDays(28L);
                        }
                    }
                    Date practiceTime = staffInfo.getPracticeTime();
                    if (ObjectUtil.isNotNull(practiceTime)) {
                        Long betweenYear = DateUtil.betweenYear(practiceTime, DateUtil.date(), true);
                        staffInfo.setYearsEmployment(String.format("%s年", betweenYear));
                    }
                }
            }

    }

    @Override
    public FeedPostVO getClubInfo(Long postId){
        Long userId = null;
        if(LoginHelper.isLogin()){
            userId = LoginHelper.getUserId();
        }
        return baseMapper.getClubDetail(postId, userId);
    }

    private String getFeedPostType(Long userId) {
        WechatUserVO user = wechatUserService.queryByUserId(userId);
        List<WechatRole> roles = user.getRoles();
        boolean isEmployee = roles.stream().anyMatch(role -> {
            String code = role.getCode();
            return AppUserRoleEnum.NURSE.name().equals(code)
                    || AppUserRoleEnum.POSTPARTUM.name().equals(code)
                    || AppUserRoleEnum.CHEF.name().equals(code);
        });
        if(isEmployee){
            return "STAFF";
        }
        boolean isCustomer = roles.stream().anyMatch(role -> {
            String code = role.getCode();
            return AppUserRoleEnum.CUSTOMER.name().equals(code);
        });
        if(isCustomer){
            return "CUSTOMER";
        }
        return null;
    }
}




