package org.dromara.biz.controller.pc;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import org.dromara.biz.domain.TaskNode;
import org.dromara.biz.domain.vo.SelectOptionsVO;
import org.dromara.biz.domain.vo.TaskNodeVO;
import org.dromara.biz.service.TaskNodeService;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 服务过程节点相关接口
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/task_node")
public class TaskNodeController {

    private final TaskNodeService taskNodeService;

    /**
     * 分页查询节点列表
     */
    @GetMapping("/page")
    public TableDataInfo<TaskNodeVO> queryPage(PageQuery query) {
        return taskNodeService.queryPage(query);
    }

    /**
     * 查询节点详情
     */
    @GetMapping("/info/{taskNodeId}")
    public R<TaskNodeVO> queryInfo(@PathVariable Long taskNodeId) {
        return R.ok(taskNodeService.queryInfo(taskNodeId));
    }

    /**
     * 新增节点
     * @param taskNodeVO 节点信息
     * @return 新增结果
     */
    @PostMapping("/save")
    @RepeatSubmit
    public R<Boolean> save(@RequestBody @Validated TaskNodeVO taskNodeVO) {
        return R.ok(taskNodeService.save(taskNodeVO));
    }

    /**
     * 更改节点精选状态
     * @param taskNodeVO 节点信息
     */
    @PostMapping("/changeFeatured")
    @RepeatSubmit
    public R<Boolean> changeFeatured(@RequestBody TaskNodeVO taskNodeVO) {
        return R.ok(taskNodeService.changeFeatured(taskNodeVO));
    }

    /**
     * 修改节点
     * @param taskNodeVO 节点信息
     * @return 修改结果
     */
    @PutMapping("/update")
    @RepeatSubmit
    public R<Boolean> update(@RequestBody @Validated TaskNodeVO taskNodeVO) {
        return R.ok(taskNodeService.update(taskNodeVO));
    }

    /**
     * 查询节点下拉选项数据
     * @return 结果
     */
    @GetMapping("/options")
    public R<List<SelectOptionsVO>> getSelectOptions(String roleCode){
        LambdaQueryWrapper<TaskNode> lqw = new LambdaQueryWrapper<>();
        if(StrUtil.isNotBlank(roleCode) && !StrUtil.equals(roleCode, "CUSTOMER")){
            lqw.eq(TaskNode::getNurseRole, roleCode);
        }
        List<TaskNode> nodeList = taskNodeService.list(lqw);
        List<SelectOptionsVO> result = nodeList.stream().map(node -> {
            SelectOptionsVO options = new SelectOptionsVO();
            options.setLabel(node.getNodeName());
            options.setValue(node.getTaskNodeId());
            return options;
        }).toList();
        return R.ok(result);
    }

    /**
     * 删除节点
     * @param taskNodeId 节点id
     * @return 结果
     */
    @DeleteMapping("/{taskNodeId}")
    public R<Boolean> remove(@PathVariable Long taskNodeId){
        return R.ok(taskNodeService.remove(taskNodeId));
    }

    /**
     * 通过类型与角色查询标签列表
     */
    @PostMapping("/listByType")
    @SaIgnore
    public R<List<TaskNodeVO>> listByType(@RequestBody TaskNodeVO taskNodeVO){
       return R.ok(taskNodeService.listByType(taskNodeVO.getNurseType(),taskNodeVO.getNurseRole()));
    }
}
