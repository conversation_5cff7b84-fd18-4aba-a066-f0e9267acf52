package org.dromara.biz.domain.query;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.page.PageQuery;

import java.io.Serial;
import java.io.Serializable;

/**
 * 小程序商户查询对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class TenantMiniProgramQuery extends PageQuery implements Serializable {

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 小程序id
     */
    private Long miniProgramId;

    /**
     * 租户小程序配置id
     */
    private Long tenantConfigId;

    /**
     * 是否默认
     */
    private Boolean isDefault;

    @Serial
    private static final long serialVersionUID = 1L;
}
