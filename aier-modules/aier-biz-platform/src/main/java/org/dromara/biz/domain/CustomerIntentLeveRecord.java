package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 客户意向度变更记录
 * @TableName biz_customer_intent_leve_record
 */
@TableName(value ="biz_customer_intent_leve_record")
@Data
public class CustomerIntentLeveRecord implements Serializable {
    /**
     * 
     */
    @TableId
    private Long id;

    /**
     * 意向度
     */
    private String intentLeve;

    /**
     * 变更时间
     */
    private Date createTime;

    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 操作人id
     */
    private Long operId;

    /**
     * 备注
     */
    private String note;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}