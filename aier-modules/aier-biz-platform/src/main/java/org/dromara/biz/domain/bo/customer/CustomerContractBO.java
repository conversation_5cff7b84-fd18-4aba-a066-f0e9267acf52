package org.dromara.biz.domain.bo.customer;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.dromara.biz.domain.CustomerContract;
import org.dromara.common.web.annotation.MobilePhone;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 客户签约对象
 */
@Data
@AutoMapper(target = CustomerContract.class, reverseConvertGenerate = false)
public class CustomerContractBO {

    /**
     * 套餐id
     */
    @NotNull(message = "套餐id不能为空")
    private Long mealId;

    /**
     * 实收类型 0=交定金;1=交合同金
     */
    @NotNull(message = "实收类型不能为空")
    private Integer actualPaymentType;

    /**
     * 实收金额
     */
    private BigDecimal paymentAmount = BigDecimal.ZERO;

    /**
     * 服务开始时间
     */
    @NotNull(message = "服务开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date serviceStartDate;

    /**
     * 服务结束时间
     */
    @NotNull(message = "服务结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date serviceEndDate;

    /**
     * 签约天数
     */
    @NotNull(message = "签约天数不能为空")
    private Integer contractDays;

    /**
     * 签约备注
     */
    private String contractNotes;

    /**
     * 签约时间
     */
    @NotNull(message = "签约时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date contractSignDate;

    /**
     * 客户id
     */
    @NotNull(message = "客户id不能为空")
    private Long customerId;

    /**
     * 房间id
     */
//    @NotNull(message = "房间id不能为空")
    private Long roomId;

    /**
     * 套餐价格
     */
    private BigDecimal mealPrice;

    /**
     * 其他费用
     */
    private BigDecimal  otherAmount;

    /**
     * 手机号
     */
    @NotBlank(message = "联系方式不能为空")
    @MobilePhone(message = "联系方式格式错误")
    private String  tel;
}
