package org.dromara.biz.domain.vo.room;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.dromara.biz.domain.Suites;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 小程序使用房型Bo
 */
@Getter
@Setter
@AutoMapper(target = Suites.class)
public class SuiteBo implements Serializable {
    /**
     * 主键id
     */
    private Long suiteId;

    /**
     * 房间名称
     */
    @NotBlank(message = "房型名称不能为空")
    private String roomName;

    /**
     * 朝向
     */
//    @NotBlank(message = "房间朝向不能为空")
    private String orientation;

    /**
     * 床型
     */
//    @NotBlank(message = "房间床型不能为空")
    private String bedType;


    /**
     * 最小面积
     */
//    @NotNull(message = "最小面积不能为空")
    private String minArea;

    /**
     * 最大面积
     */
//    @NotNull(message = "最大面积不能为空")
    private String maxArea;



    /**
     * 套房描述
     */
    private String description;


    /**
     * 套房照片列表
     */
//    @NotEmpty(message = "套房照片列表不能为空")
    private List<String> suitePhotos;
}
