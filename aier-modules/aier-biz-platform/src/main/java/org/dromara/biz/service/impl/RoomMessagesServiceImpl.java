package org.dromara.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.biz.domain.RoomMessages;
import org.dromara.biz.domain.vo.CustomerMessageVO;
import org.dromara.biz.mapper.RoomMessagesMapper;
import org.dromara.biz.service.RoomMessagesService;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【biz_room_messages(房间评价消息表)】的数据库操作Service实现
* @createDate 2024-03-21 11:24:03
*/
@Service
@Slf4j
@AllArgsConstructor
public class RoomMessagesServiceImpl extends ServiceImpl<RoomMessagesMapper, RoomMessages>
    implements RoomMessagesService{

    @Override
    public List<CustomerMessageVO> queryMessageList() {
        LoginUser loginUser = LoginHelper.getLoginUser();
        Long userId = loginUser.getUserId();
        LambdaQueryWrapper<RoomMessages> roomMessageLqw = Wrappers.lambdaQuery();
        roomMessageLqw.eq(RoomMessages::getStatus, false);
        roomMessageLqw.eq(RoomMessages::getUserId, userId);
        roomMessageLqw.orderByDesc(RoomMessages::getCreateTime);
        List<RoomMessages> messagesList = baseMapper.selectList(roomMessageLqw);
        if(CollectionUtils.isEmpty(messagesList)){
            return Collections.emptyList();
        }
        RoomMessages roomMessages = messagesList.stream().findAny().get();
        CustomerMessageVO customerMessagevo = new CustomerMessageVO();
        customerMessagevo.setContent(roomMessages.getContent());
        List<CustomerMessageVO> result = new ArrayList<>();
        result.add(customerMessagevo);
        return result;
    }

    @Override
    public List<RoomMessages> queryMessageDetailList() {
        Long userId = LoginHelper.getUserId();
        LambdaQueryWrapper<RoomMessages> roomMessageLqw = Wrappers.lambdaQuery();
        roomMessageLqw.eq(RoomMessages::getUserId, userId);
        roomMessageLqw.orderByDesc(RoomMessages::getCreateTime);
        return baseMapper.selectList(roomMessageLqw);
    }

    @Override
    public Boolean read(){
        Long userId = LoginHelper.getUserId();
        return this.lambdaUpdate().eq(RoomMessages::getUserId, userId).set(RoomMessages::getStatus, true).update();
    }
}




