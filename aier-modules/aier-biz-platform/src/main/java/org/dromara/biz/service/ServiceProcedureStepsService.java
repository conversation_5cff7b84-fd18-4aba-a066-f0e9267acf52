package org.dromara.biz.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.biz.domain.ServiceProcedureSteps;
import org.dromara.biz.domain.query.CustomerServiceStepQuery;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【biz_service_procedure_steps(客户服务过程表)】的数据库操作Service
* @createDate 2024-05-23 19:45:32
*/
public interface ServiceProcedureStepsService extends IService<ServiceProcedureSteps> {

    /**
     * 查询客户服务节点对应的动态id
     * @param query 查询参数
     * @return 动态id列表
     */
    List<Long> queryCustomerServiceStepPostIds(CustomerServiceStepQuery query);

    /**
     * 通过动态id获取服务过程
     */
    ServiceProcedureSteps getByPostId(Long postId);

    /**
     * 通过动态ids获取服务过程map
     */
    Map<Long, ServiceProcedureSteps> getByPostIds(Set<Long> postIds);

    /**
     * 查询服务过程客户ids
     */
    List<Long> getCustomerIdsByPostIds(Set<Long> postIds);

    /**
     * 是否关联动态
     */
    Boolean hasDynamicLink(Long taskNodeId);
}
