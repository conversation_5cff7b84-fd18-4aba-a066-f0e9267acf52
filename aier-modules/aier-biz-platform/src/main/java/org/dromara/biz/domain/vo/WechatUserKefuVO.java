package org.dromara.biz.domain.vo;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.dromara.biz.domain.WechatUser;

@Data
@AutoMapper(target = WechatUser.class)
public class WechatUserKefuVO {

    private Long  userId;

    private String kfNick;

    /**
     * 手机
     */
    private String kfWx;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * openid
     */
    private String kfOpenid;

    /**
     * 头像
     */
    private String avatar;

    public WechatUserKefuVO(Long userId, String kfNick, String kfWx, String tenantId, String kfOpenid,String avatar) {
        this.userId = userId;
        this.kfNick = kfNick;
        this.kfWx = kfWx;
        this.tenantId = tenantId;
        this.kfOpenid = kfOpenid;
        this.avatar = avatar;
    }

    public WechatUserKefuVO() {
    }
}
