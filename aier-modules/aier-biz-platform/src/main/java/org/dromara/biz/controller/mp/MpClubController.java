package org.dromara.biz.controller.mp;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.dromara.biz.domain.BizClubActivityLogs;
import org.dromara.biz.domain.Clubs;
import org.dromara.biz.domain.vo.*;
import org.dromara.biz.domain.vo.club.ClubActivityVO;
import org.dromara.biz.service.*;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.domain.model.LoginUser;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 小程序会所相关接口
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping("/mp/club")
public class MpClubController {

    private static final String KEY = "e6c773d382b97def531ec3edbf65371e";
    private final FeedPostService feedPostService;
    private final ClubsService clubsService;
    private final ClubFacilitiesService clubFacilitiesService;
    private static final OkHttpClient httpClient;
    private final IBizClubActivityLogsService clubActivityLogsService;
    static {
        httpClient = new OkHttpClient.Builder()
            .connectTimeout(2000, TimeUnit.SECONDS)
            .readTimeout(40000, TimeUnit.SECONDS)
            .build();
    }
    private final ClubActivityService clubActivityService;

    /**
     * 分页查询会所动态列表
     * @param query 查询参数
     * @return 结果
     */
    @GetMapping("/post_page")
    @SaIgnore
    public TableDataInfo<FeedPostVO> queryPage(PageQuery query) {
        return feedPostService.queryClubPage(query);
    }

    /**
     * 查询会所客服电话
     * @return 结果
     */
    @GetMapping("/service_phone")
    public R<String> getServicePhone(){
        ClubVO club = clubsService.getInfo();
        if(ObjectUtil.isNotNull(club)){
            return R.ok(club.getServicePhone());
        }
        return R.fail();
    }

    /**
     * 获取地理/逆地理编码
     * @param address 查询参数 规则遵循：国家、省份、城市、区县、城镇、乡村、街道、门牌号码、屋邨、大厦，如：北京市朝阳区阜通东大街6号。
     * @return 结果
     */
    @GetMapping("/geocodes")
    @SaIgnore
    public R<Geocodes> geocodes(@RequestParam("address") String address) {
        Response response = null;
        Geocodes geocodes;
        try{
            Request request = new Request.Builder()
                .addHeader("content-type", "application/json")
                //参数放到链接后面
                .url(StrUtil
                    .format("https://restapi.amap.com/v3/geocode/geo?key={}&address={}&output=json", KEY, address))
                .build();
            response = httpClient.newCall(request).execute();
            String respStr = response.body().string();
            JSONObject parse = JSONUtil.parseObj(respStr);
            List<Geocodes> geocodesList = parse.getBeanList("geocodes", Geocodes.class);
            geocodes = geocodesList.get(0);
        }catch (Exception e){
            e.printStackTrace();
            throw new ServiceException("不支持的地址");
        }finally {
            if(response != null){
                response.close();
            }
        }
        return R.ok(geocodes);
    }

    /**
     * 获取当前会所名称
     * @return 会所名称
     */
    @GetMapping("/name")
    public R<String> getClubName() {
        return R.ok("操作成功", clubsService.getClubName());
    }

    /**
     * 获取当前会所基本信息
     * @return 结果
     */
    @GetMapping("/info")
    @SaIgnore
    public R<ClubVO> getClubInfo() {
        return R.ok(clubsService.getInfo());
    }

    /**
     * 查询会所主页信息
     * @return 结果
     */
    @GetMapping("/home")
    @SaIgnore
    public R<ClubHomeVO> queryClubHome(){
        Clubs club = clubsService.lambdaQuery()
            .last("limit 1").one();
        if(ObjectUtil.isNull(club)){
            return R.ok(new ClubHomeVO());
        }
        ClubHomeVO clubHome = new ClubHomeVO();
        clubHome.setClubName(club.getClubName())
            .setClubTagNames(club.getClubTagNames())
            .setClubBackgroundPhotos(club.getClubBackgroundPhotos())
            .setClubActivityAreaPhotos(club.getClubActivityAreaPhotos())
            .setLocationCity(club.getLocationCity())
            .setLocationAddress(club.getLocationAddress());
        return R.ok(clubHome);
    }

    /**
     * 查询会所品牌介绍
     * @return 结果
     */
    @GetMapping("/home_brand")
    @SaIgnore
    public R<ClubBrandVO> queryClubHomeBrand(){
        Clubs club = clubsService.lambdaQuery()
            .last("limit 1").one();
        if(ObjectUtil.isNull(club)){
            return R.ok(new ClubBrandVO());
        }
        ClubBrandVO clubBrand = new ClubBrandVO();
        clubBrand.setClubName(club.getClubName());
        clubBrand.setClubFacilityPhotos(club.getClubFacilityPhotos());
        clubBrand.setClubDescription(club.getClubDescription());
        clubBrand.setClubFacilitiesPhotos(club.getClubFacilitiesPhotos());
        clubBrand.setTags(club.getClubTagNames());
        clubBrand.setLogo(club.getLogo());
        return R.ok(clubBrand);
    }

    /**
     * 查询会所活动列表
     */
    @GetMapping("/activity_list")
    @SaIgnore
    public R<List<ClubActivityVO>> queryClubHomeActivity(){
        List<ClubActivityVO> result = clubActivityService.queryClubHomeActivity();
        return R.ok(result);
    }

    /**
     * 查询会所活动详情
     */
    @GetMapping("/activity_detail")
    @SaIgnore
    public R<ClubActivityVO> queryClubActivityDetail(@RequestParam("activityId") Long activityId) {
        ClubActivityVO result = clubActivityService.getDetail(activityId);
        return R.ok(result);
    }

    /**
     * 查询会所设施列表
     */
    @GetMapping("/facilities_list")
    @SaIgnore
    public R<List<ClubFacilitiesVO>> queryFacilitiesList() {
        return R.ok(clubFacilitiesService.queryList());
    }




    /**
     * 活动报名
     * @param activityId
     * @return
     */
    @GetMapping("/activitySignUp")
    @SaIgnore
    public R<Boolean> signUp(@RequestParam("activityId") Long activityId) {
        LoginUser loginUser = LoginHelper.getLoginUser();
        BizClubActivityLogs clubActivityLogs = new BizClubActivityLogs();
        clubActivityLogs.setActivityId(activityId);
        clubActivityLogs.setUserId(loginUser.getUserId());
        clubActivityLogs.setUserName(loginUser.getUsername());
        return R.ok(clubActivityLogsService.save(clubActivityLogs));
    }
}
