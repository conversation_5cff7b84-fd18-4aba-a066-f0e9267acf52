package org.dromara.biz.domain.vo;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 客户信息
 */
@Data
@Accessors(chain = true)
public class CustomerInfoVO {


    /**
     * 客户id
     */
    private Long customerId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 手机号
     */
    private String tel;

    /**
     * 入住房间
     */
    private Long roomId;

    /**
     * 套房名称
     */
    private String suiteName;

    /**
     * 房间号
     */
    private String roomNumber;
}
