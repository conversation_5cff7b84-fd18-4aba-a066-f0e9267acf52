package org.dromara.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.biz.domain.FeedPostLinks;
import org.dromara.biz.domain.vo.FeedPostVO.LikeUsers;
import org.dromara.biz.domain.vo.WechatUserVO;
import org.dromara.biz.mapper.FeedPostLinksMapper;
import org.dromara.biz.service.FeedPostLinksService;
import org.dromara.biz.service.WechatUserService;
import org.dromara.common.satoken.utils.LoginHelper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【biz_feed_post_links(会所朋友圈点赞表)】的数据库操作Service实现
* @createDate 2024-03-15 10:57:13
*/
@Service
@AllArgsConstructor
public class FeedPostLinksServiceImpl extends ServiceImpl<FeedPostLinksMapper, FeedPostLinks>
    implements FeedPostLinksService{

    private final WechatUserService wechatUserService;

    @Override
    public Map<Long, FeedPostLinks> getMapByPostIds(Set<Long> postSet) {
        if(CollectionUtils.isEmpty(postSet)){
            return Map.of();
        }
        LambdaQueryWrapper<FeedPostLinks> lqw = Wrappers.lambdaQuery();
        lqw.in(FeedPostLinks::getPostId, postSet);
        List<FeedPostLinks> feedPostLinks = baseMapper.selectList(lqw);
        return feedPostLinks
            .stream()
            .collect(Collectors.toMap(FeedPostLinks::getPostId, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public Map<Long, FeedPostLinks> getCurrentUserMapByPostIds(Set<Long> postSet){
        Long userId = LoginHelper.getUserId();
        if(CollectionUtils.isEmpty(postSet)){
            return Map.of();
        }
        LambdaQueryWrapper<FeedPostLinks> lqw = Wrappers.lambdaQuery();
        lqw.in(FeedPostLinks::getPostId, postSet);
        lqw.eq(FeedPostLinks::getUserId, userId);
        List<FeedPostLinks> feedPostLinks = baseMapper.selectList(lqw);
        return feedPostLinks
            .stream()
            .collect(Collectors.toMap(FeedPostLinks::getPostId, Function.identity(), (k1, k2) -> k1));
    }

    @Override
    public Map<Long, List<LikeUsers>> getLikeUsers(Set<Long> postSet) {
        if(CollectionUtils.isEmpty(postSet)){
            return Map.of();
        }
        LambdaQueryWrapper<FeedPostLinks> lqw = Wrappers.lambdaQuery();
        lqw.in(FeedPostLinks::getPostId, postSet);
        List<FeedPostLinks> feedPostLinks = baseMapper.selectList(lqw);
        List<Long> userIds = feedPostLinks.stream().map(FeedPostLinks::getUserId).distinct().toList();
        Map<Long, WechatUserVO> userMap = wechatUserService.queryMapByIds(userIds);
        return feedPostLinks.stream().map(feedPostLink -> {
            WechatUserVO wechatUserVO = userMap.getOrDefault(feedPostLink.getUserId(), new WechatUserVO());
            LikeUsers likeUsers = new LikeUsers();
            likeUsers.setPostId(feedPostLink.getPostId());
            likeUsers.setUserId(feedPostLink.getUserId());
            likeUsers.setNickname(wechatUserVO.getNickname());
            likeUsers.setAvatar(wechatUserVO.getAvatar());
            return likeUsers;
        }).collect(Collectors.groupingBy(LikeUsers::getPostId));
    }

    public Boolean isLiked(Long postId, Long userId){
        LambdaQueryWrapper<FeedPostLinks> lqw = Wrappers.lambdaQuery();
        lqw.eq(FeedPostLinks::getPostId, postId);
        lqw.eq(FeedPostLinks::getUserId, userId);
        return count(lqw) != 0L;
    }
}




