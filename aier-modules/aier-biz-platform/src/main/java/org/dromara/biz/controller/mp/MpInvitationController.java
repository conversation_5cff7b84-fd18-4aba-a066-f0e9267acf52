//package org.dromara.biz.controller.mp;
//
//import lombok.AllArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.dromara.biz.domain.query.invitation.InvitationQuery;
//import org.dromara.biz.domain.vo.invitation.InvitationTemplateVO;
//import org.dromara.biz.service.InvitationTemplateService;
//import org.dromara.common.core.domain.R;
//import org.dromara.common.mybatis.core.page.PageQuery;
//import org.dromara.common.mybatis.core.page.TableDataInfo;
//import org.dromara.common.satoken.utils.LoginHelper;
//import org.springframework.web.bind.annotation.*;
//
///**
// * 请帖相关接口
// */
//@AllArgsConstructor
//@Slf4j
//@RestController
//@RequestMapping("/mp/invitation")
//public class MpInvitationController {
//
//    private final InvitationTemplateService templateService;
//
//    /**
//     * 分页查询请帖列表
//     */
//    @GetMapping("/page")
//    public R<TableDataInfo<InvitationTemplateVO>> queryTemplatePage(InvitationQuery query){
//        query.setEnable(true);
//        return R.ok(templateService.queryPage(query));
//    }
//
//    /**
//     * 收藏请帖
//     **/
//    @PutMapping("/collect")
//    public R<Void> templateCollect(@RequestParam("id") Long id) {
//        templateService.templateCollect(id);
//        return R.ok();
//    }
//
//    /**
//     * 分页查询当前用户收藏的请帖列表
//     **/
//    @GetMapping("/collect")
//    public R<TableDataInfo<InvitationTemplateVO>> getTemplateCollectPage(PageQuery pageQuery) {
//        Long userId = LoginHelper.getUserId();
//        return R.ok(templateService.getTemplateCollectPage(pageQuery, userId));
//    }
//}
