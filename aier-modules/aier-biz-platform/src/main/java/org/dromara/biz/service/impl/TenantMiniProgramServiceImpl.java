package org.dromara.biz.service.impl;

import cn.binarywang.wx.miniapp.api.WxMaLinkService;
import cn.binarywang.wx.miniapp.api.WxMaQrcodeService;
import cn.binarywang.wx.miniapp.api.WxMaSchemeService;
import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.scheme.WxMaGenerateSchemeRequest;
import cn.binarywang.wx.miniapp.bean.urllink.GenerateUrlLinkRequest;
import cn.binarywang.wx.miniapp.util.WxMaConfigHolder;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.URLUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.dromara.biz.domain.TenantMiniProgram;
import org.dromara.biz.domain.WechatMiniProgram;
import org.dromara.biz.domain.query.TenantMiniProgramQuery;
import org.dromara.biz.domain.vo.TenantMiniProgramVO;
import org.dromara.biz.mapper.TenantMiniProgramMapper;
import org.dromara.biz.service.TenantMiniProgramService;
import org.dromara.biz.service.WechatMiniProgramService;
import org.dromara.common.core.exception.ServiceException;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.common.oss.core.OssClient;
import org.dromara.common.oss.entity.UploadResult;
import org.dromara.common.oss.factory.OssFactory;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.stereotype.Service;

import java.io.File;

/**
* <AUTHOR>
* @description 针对表【biz_tenant_mini_program(租户小程序表)】的数据库操作Service实现
* @createDate 2024-06-25 20:18:28
*/
@Service
@AllArgsConstructor
@Slf4j
public class TenantMiniProgramServiceImpl extends ServiceImpl<TenantMiniProgramMapper, TenantMiniProgram>
    implements TenantMiniProgramService{

    private final WxMaService wxMaService;
    private final WechatMiniProgramService wechatMiniProgramService;

    @Override
    public TenantMiniProgram getTenantProgram(String tenantId, Long miniProgramId){
        return TenantHelper.ignore(()->{
            LambdaQueryWrapper<TenantMiniProgram> lqw = Wrappers.lambdaQuery();
            lqw.eq(TenantMiniProgram::getTenantId, tenantId);
            lqw.eq(TenantMiniProgram::getMiniProgramId, miniProgramId);
            return getOne(lqw);
        });
    }

    @Override
    public TenantMiniProgram getTenantProgram(String tenantId){
        return TenantHelper.ignore(()->{
            LambdaQueryWrapper<TenantMiniProgram> lqw = Wrappers.lambdaQuery();
            lqw.eq(TenantMiniProgram::getTenantId, tenantId);
            lqw.last("limit 1");
            return getOne(lqw);
        });
    }

    @Override
    public TableDataInfo<TenantMiniProgramVO> queryPage(TenantMiniProgramQuery query){
        return TenantHelper.ignore(()->{
            IPage<TenantMiniProgramVO> page = baseMapper.selectTenantMiniProgramPage(query.build(), query);
            return TableDataInfo.build(page);
        });
    }

    @Override
    public Boolean saveTenantMiniProgram(TenantMiniProgram tenantMiniProgram) {
        return TenantHelper.ignore(()->{
            LambdaQueryWrapper<TenantMiniProgram> lqw = Wrappers.lambdaQuery();
            lqw.eq(TenantMiniProgram::getTenantId, tenantMiniProgram.getTenantId());
            lqw.eq(TenantMiniProgram::getMiniProgramId, tenantMiniProgram.getMiniProgramId());
            if (count(lqw) > 0) {
                throw new ServiceException("已经在当前小程序下配置了商户信息");
            }
            tenantMiniProgram.setIsDefault(true);
            tenantMiniProgram.setEnvVersion("release");
            return save(tenantMiniProgram);
        });
    }

    @Override
    public void generateQrCode(Long tenantConfigId) {
//        generateQrCode("packageA/pages/index/index", tenantConfigId);
        generateQrCode("pageA/home", tenantConfigId);
    }

    @Override
    public void generateQrCodeByPage(String page, Long tenantConfigId) {
        generateQrCode(page, tenantConfigId);
    }

    @Override
    public void generateUrlLike(Long tenantConfigId) {
        TenantHelper.ignore(()->{
            try{
                TenantMiniProgram tenantMiniProgram = getTenantMiniProgram(tenantConfigId);
                String tenantId = tenantMiniProgram.getTenantId();
                String query = StringUtils.format("tenantId={}", tenantId);
                DateUtil.endOfYear(DateUtil.date());
                WxMaLinkService linkService = wxMaService.getLinkService();
                GenerateUrlLinkRequest request = GenerateUrlLinkRequest.builder()
                    .path("pageA/home")
                    .query(query)
                    .envVersion(tenantMiniProgram.getEnvVersion())
                    .build();
                String urlLink = linkService.generateUrlLink(request);
                LambdaUpdateWrapper<TenantMiniProgram> luw = Wrappers.lambdaUpdate();
                luw.eq(TenantMiniProgram::getTenantConfigId, tenantConfigId);
                luw.set(TenantMiniProgram::getUrlLink, urlLink);
                update(luw);
            } catch (WxErrorException e) {
                log.error("生成小程序URL_LINK失败", e);
                throw new ServiceException(e.getMessage());
            } finally {
                WxMaConfigHolder.remove();
            }
        });
    }

    @Override
    public void generateUrlScheme(Long tenantConfigId) {
        TenantHelper.ignore(()->{
            try{
                TenantMiniProgram tenantMiniProgram = getTenantMiniProgram(tenantConfigId);
                String tenantId = tenantMiniProgram.getTenantId();
                String query = StringUtils.format("tenantId={}", tenantId);
                DateUtil.endOfYear(DateUtil.date());
                WxMaSchemeService schemeService = wxMaService.getWxMaSchemeService();
                WxMaGenerateSchemeRequest.JumpWxa jump = WxMaGenerateSchemeRequest.JumpWxa.newBuilder()
                    .path("pageA/home")
                    .query(query)
                    .envVersion(tenantMiniProgram.getEnvVersion())
                    .build();
                WxMaGenerateSchemeRequest request = WxMaGenerateSchemeRequest.newBuilder()
                    .jumpWxa(jump)
                    .build();

                String urlScheme = schemeService.generate(request);
                LambdaUpdateWrapper<TenantMiniProgram> luw = Wrappers.lambdaUpdate();
                luw.eq(TenantMiniProgram::getTenantConfigId, tenantConfigId);
                luw.set(TenantMiniProgram::getUrlScheme, urlScheme);
                update(luw);
            } catch (WxErrorException e) {
                log.error("生成小程序URL_SCHEME失败", e);
                throw new ServiceException(e.getMessage());
            } finally {
                WxMaConfigHolder.remove();
            }
        });
    }

    @Override
    public String generateQrCodeUrl(String pathUrl,String uuid){
        String tenantId = LoginHelper.getTenantId();
        // 生成小程序码
        return TenantHelper.ignore(()->{
            // 生成小程序码
            try {
                TenantMiniProgram tenantMiniProgram = getTenantMiniProgramByTenantId(tenantId);
                WxMaQrcodeService qrcodeService = wxMaService.getQrcodeService();
                String scene = StringUtils.format("tenantId-{}", tenantId);
                scene += StringUtils.format("&uuid-{}", uuid);
                String encode = URLUtil.encode(scene);
                File qrcodeFile = qrcodeService
                    .createWxaCodeUnlimit(encode, pathUrl, false,
                        tenantMiniProgram.getEnvVersion(), 430, false, null, false);
                String suffix = FileUtil.getSuffix(qrcodeFile);
                OssClient storage = OssFactory.instance();
                UploadResult uploadResult = storage.uploadSuffix(qrcodeFile, "." + suffix);
                return uploadResult.getUrl();
            } catch (WxErrorException e) {
                log.error("生成小程序码失败", e);
                throw new ServiceException(e.getMessage());
            } finally {
                WxMaConfigHolder.remove();
            }
        });
    }

    private void generateQrCode(String page, Long tenantConfigId){
        TenantHelper.ignore(()->{
            // 生成小程序码
            try {
                TenantMiniProgram tenantMiniProgram = getTenantMiniProgram(tenantConfigId);
                WxMaQrcodeService qrcodeService = wxMaService.getQrcodeService();
                String tenantId = tenantMiniProgram.getTenantId();
                String scene = StringUtils.format("tenantId-{}", tenantId);
                String encode = URLUtil.encode(scene);
                File qrcodeFile = qrcodeService
                    .createWxaCodeUnlimit(encode, page, false,
                        tenantMiniProgram.getEnvVersion(), 430, false, null, false);
                String suffix = FileUtil.getSuffix(qrcodeFile);
                OssClient storage = OssFactory.instance();
                UploadResult uploadResult = storage.uploadSuffix(qrcodeFile, "." + suffix);
                String url = uploadResult.getUrl();
                LambdaUpdateWrapper<TenantMiniProgram> luw = Wrappers.lambdaUpdate();
                luw.eq(TenantMiniProgram::getTenantConfigId, tenantConfigId);
                luw.set(TenantMiniProgram::getQrCode, url);
                update(luw);
            } catch (WxErrorException e) {
                log.error("生成小程序码失败", e);
                throw new ServiceException(e.getMessage());
            } finally {
                WxMaConfigHolder.remove();
            }
        });
    }

    private TenantMiniProgram getTenantMiniProgram (Long tenantConfigId){
        TenantMiniProgram tenantMiniProgram = getById(tenantConfigId);
        if (ObjectUtil.isNull(tenantMiniProgram)) {
            throw new ServiceException("小程序商户配置不存在");
        }
        Long miniProgramId = tenantMiniProgram.getMiniProgramId();
        WechatMiniProgram miniProgram = wechatMiniProgramService.getById(miniProgramId);
        if(ObjectUtil.isNull(miniProgram)){
            throw new ServiceException("微信小程序不存在");
        }
        if (!wxMaService.switchover(miniProgram.getAppid())) {
            throw new ServiceException(String.format("未找到对应appid=[%s]的配置，请核实！", miniProgram.getAppid()));
        }
        return tenantMiniProgram;
    }
    private TenantMiniProgram getTenantMiniProgramByTenantId (String tenantId){
        TenantMiniProgram tenantMiniProgram = getTenantProgram(tenantId);
        if (ObjectUtil.isNull(tenantMiniProgram)) {
            throw new ServiceException("小程序商户配置不存在");
        }
        Long miniProgramId = tenantMiniProgram.getMiniProgramId();
        WechatMiniProgram miniProgram = wechatMiniProgramService.getById(miniProgramId);
        if(ObjectUtil.isNull(miniProgram)){
            throw new ServiceException("微信小程序不存在");
        }
        if (!wxMaService.switchover(miniProgram.getAppid())) {
            throw new ServiceException(String.format("未找到对应appid=[%s]的配置，请核实！", miniProgram.getAppid()));
        }
        return tenantMiniProgram;
    }
}




