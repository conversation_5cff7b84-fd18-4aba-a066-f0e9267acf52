package org.dromara.biz.controller.mp;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.biz.domain.query.CommunityPostQuery;
import org.dromara.biz.domain.query.TopicQuery;
import org.dromara.biz.domain.vo.CommunityPostVO;
import org.dromara.biz.domain.vo.PageBookmarkVO;
import org.dromara.biz.domain.vo.TopicVO;
import org.dromara.biz.service.CommunityPostsService;
import org.dromara.biz.service.PageBookmarksService;
import org.dromara.biz.service.TopicsService;
import org.dromara.common.core.domain.R;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 小程序收藏相关接口
 * <AUTHOR>
 */
@AllArgsConstructor
@Slf4j
@RestController
@RequestMapping("/mp/bookmark")
public class MpPageBookmarkController {

    private final PageBookmarksService pageBookmarksService;
    private final CommunityPostsService communityPostsService;
    private final TopicsService topicsService;

    /**
     * 新增页面&界面收藏
     * @param pageBookmarkVO 界面信息
     * @return 结果
     */
    @PutMapping("/add_view")
    @RepeatSubmit
    public R<Boolean> save(@RequestBody @Validated PageBookmarkVO pageBookmarkVO){
        return R.ok(pageBookmarksService.addPage(pageBookmarkVO));
    }

    /**
     * 分页查询我收藏页面&界面列表
     * @param query 查询参数
     * @return 结果
     */
    @GetMapping("/page_view")
    public TableDataInfo<PageBookmarkVO> queryPage(PageQuery query) {
        return pageBookmarksService.queryPage(query);
    }

    /**
     * 分页查询我收藏宝妈社区动态列表
     * @param query 查询参数
     * @return 结果
     */
    @GetMapping("/page_post")
    public TableDataInfo<CommunityPostVO> queryCommunityPostPage(CommunityPostQuery query) {
        return communityPostsService.queryBookmark(query);
    }

    /**
     * 分页查询我收藏的话题列表
     * @param query 查询参数
     * @return 结果
     */
    @GetMapping("/page_topic")
    public TableDataInfo<TopicVO> queryTopicPage(TopicQuery query) {
        return topicsService.queryBookmark(query);
    }
}
