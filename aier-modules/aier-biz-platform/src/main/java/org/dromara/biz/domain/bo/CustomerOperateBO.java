package org.dromara.biz.domain.bo;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.dromara.common.core.validate.EditGroup;

/**
 * 客户操作bo
 */
@Data
public class CustomerOperateBO {

    /**
     * 客户id 更新时为必填 新增时可忽略此参数
     */
    @NotNull(message = "客户id不能为空", groups = {EditGroup.class})
    private Long customerId;

    /**
     * 跟进记录
     */
    private String followUpLog;

    /**
     * 标签id
     */
    @NotNull(message = "客户标签不能为空")
    private Long customerTagId;

    /**
     * 意向度
     */
//    @NotBlank(message = "客户意向度不能为空")
    private String intentLeve;


    /**
     * 是否到店
     */
    private Boolean isToStore;

    /**
     * 到店次数
     */
    private String arrivedAtStoreNumber;
}
