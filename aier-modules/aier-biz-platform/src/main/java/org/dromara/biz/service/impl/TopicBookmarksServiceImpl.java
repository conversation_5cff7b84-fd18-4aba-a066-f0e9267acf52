package org.dromara.biz.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.dromara.biz.domain.TopicBookmarks;
import org.dromara.biz.domain.vo.WechatUserVO;
import org.dromara.biz.mapper.TopicBookmarksMapper;
import org.dromara.biz.service.TopicBookmarksService;
import org.dromara.biz.service.WechatUserService;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【biz_topic_bookmarks(话题收藏表)】的数据库操作Service实现
* @createDate 2024-03-26 10:00:23
*/
@Service
@AllArgsConstructor
@Slf4j
public class TopicBookmarksServiceImpl extends ServiceImpl<TopicBookmarksMapper, TopicBookmarks>
    implements TopicBookmarksService{

    private final WechatUserService wechatUserService;

    @Override
    public Long getUserNum(Long topicId) {
        LambdaQueryWrapper<TopicBookmarks> lqw = Wrappers.lambdaQuery();
        lqw.eq(TopicBookmarks::getTopicId, topicId);
        lqw.groupBy(TopicBookmarks::getUserId);
        return baseMapper.selectCount(lqw);
    }

    @Override
    public Map<Long, List<WechatUserVO>> getUserMap(List<Long> topicIds) {
        List<TopicBookmarks> topicBookmarkList = queryTopicBookmarkList(topicIds);
        List<Long> userIds = topicBookmarkList.parallelStream().map(TopicBookmarks::getUserId).distinct().toList();
        Map<Long, WechatUserVO> userMap = wechatUserService.queryMapByIds(userIds);
        Map<Long, List<TopicBookmarks>> bookmarkMap = topicBookmarkList.stream()
            .collect(Collectors.groupingBy(TopicBookmarks::getTopicId));

        Map<Long, List<WechatUserVO>> result = new HashMap<>(bookmarkMap.size());
        bookmarkMap.forEach((topicId, topicBookmarks) -> {
            List<WechatUserVO> userList = topicBookmarks.stream().map(topicBookmark -> {
                Long userId = topicBookmark.getUserId();
                return userMap.get(userId);
            }).filter(Objects::nonNull).toList();
            result.put(topicId, userList);
        });
        return result;
    }


    private List<TopicBookmarks> queryTopicBookmarkList(List<Long> topicIds) {
        if(CollectionUtils.isEmpty(topicIds)){
            return Collections.emptyList();
        }
        LambdaQueryWrapper<TopicBookmarks> lqw = Wrappers.lambdaQuery();
        lqw.in(TopicBookmarks::getTopicId, topicIds);
        return baseMapper.selectList(lqw);
    }
}




