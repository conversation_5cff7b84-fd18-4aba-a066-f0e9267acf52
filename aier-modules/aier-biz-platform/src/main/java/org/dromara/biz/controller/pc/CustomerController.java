package org.dromara.biz.controller.pc;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.io.FileUtil;
import com.alibaba.excel.exception.ExcelAnalysisException;
import lombok.RequiredArgsConstructor;
import org.dromara.biz.domain.query.CustomerQuery;
import org.dromara.biz.domain.vo.CustomerBirthdayVO;
import org.dromara.biz.domain.vo.CustomerVO;
import org.dromara.biz.domain.vo.SelectOptionsVO;
import org.dromara.biz.domain.vo.customer.CustomerExportVO;
import org.dromara.biz.service.CustomersService;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.core.utils.file.MimeTypeUtils;
import org.dromara.common.excel.core.ExcelResult;
import org.dromara.common.excel.utils.ExcelUtil;
import org.dromara.common.idempotent.annotation.RepeatSubmit;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;

/**
 * 客户&客资相关接口
 *
 * <AUTHOR>
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/platform/customer")
public class CustomerController {

    private final CustomersService customersService;

    /**
     * 分页查询客户列表
     */
    @GetMapping("/page")
    public TableDataInfo<CustomerVO> page(CustomerQuery query) {
        return customersService.queryPage(query);
    }

    /**
     * 查询客户信息
     */
    @GetMapping("/info/{customerId}")
    public R<CustomerVO> info(@PathVariable Long customerId) {
        return R.ok(customersService.getByCustomerId(customerId));
    }

    /**
     * 修改客户信息
     * @param vo 客户信息
     * @return 修改结果
     */
    @PostMapping("/update")
    @RepeatSubmit
    public R<Boolean> update(@RequestBody @Validated CustomerVO vo) {
        return R.ok(customersService.update(vo));
    }

    /**
     * 删除客户信息
     * @param customerId 客户id
     * @return 结果
     */
    @DeleteMapping("/{customerId}")
    public R<Boolean> remove(@PathVariable Long customerId){
        return R.ok(customersService.removeById(customerId));
    }


    /**
     * 查询客户生日列表
     * @param type mom=妈妈 baby=宝宝
     * @return 结果
     */
    @GetMapping("/birthday_list/{type}")
    public R<List<CustomerBirthdayVO>> queryBirthdayList(@PathVariable String type){
        return R.ok(customersService.queryBirthdayList(type));
    }

    /**
     * 查询客户生日列表 tips：妈妈宝宝数据混合查询
     * @return 结果
     */
    @GetMapping("/birthday_list")
    public R<List<CustomerBirthdayVO>> queryBirthdayList(@RequestParam(value = "limit", defaultValue = "7") Integer limit){
        List<CustomerBirthdayVO> allBirthdayList = customersService.queryAllBirthdayList();
        List<CustomerBirthdayVO> result = allBirthdayList.stream()
            .limit(limit).toList();
        return R.ok(result);
    }

    /**
     * 查询最近10天过生日的所有客户数量
     * @return 结果
     */
    @GetMapping("/birthday_count")
    public R<Long> queryBirthdayCount(){
        return R.ok(customersService.queryAllBirthdayCount());
    }

    /**
     * 查询客户下拉选项数据
     * @return 结果
     */
    @GetMapping("/options")
    public R<List<SelectOptionsVO>> getSelectOptions(){
        return R.ok(customersService.getSelectOptions());
    }

    /**
     * 导入客资
     * @param avatarfile
     * @return
     * @throws IOException
     */
    @PostMapping("/importExcel")
    @SaIgnore
    public R<Boolean> importExcel(@RequestPart("avatarfile") MultipartFile avatarfile) throws IOException {
        if (!avatarfile.isEmpty()) {
            String extension = FileUtil.extName(avatarfile.getOriginalFilename());
            if (!StringUtils.equalsAnyIgnoreCase(extension, MimeTypeUtils.EXCLUDED_EXTENSION)) {
                return R.fail("文件格式不正确，请上传" + Arrays.toString(MimeTypeUtils.EXCLUDED_EXTENSION) + "格式");
            }
            List<CustomerExportVO> customerExportResult = ExcelUtil.importExcel(avatarfile.getInputStream(), CustomerExportVO.class);
            return R.ok(customersService.importExcel(customerExportResult));

        }
        return R.fail("导入数据异常，请联系管理员");
    }

}
