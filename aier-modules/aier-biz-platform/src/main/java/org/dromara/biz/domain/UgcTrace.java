package org.dromara.biz.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 微信内容审查消息记录表
 * @TableName biz_ugc_trace
 */
@TableName(value ="biz_ugc_trace")
@Data
public class UgcTrace implements Serializable {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     * 唯一请求标识，标记单次请求，用于匹配异步推送结果
     */
    private String traceId;

    /**
     * 场景枚举值（1 资料；2 评论；3 论坛；4 社交日志）
     */
    private Integer scene;

    /**
     * 业务枚举值（1:朋友圈；2:社区；3评价）
     */
    private Integer businessType;

    /**
     * 媒体类型 （1:音频;2:图片）
     */
    private Integer mediaType;

    /**
     * 媒体链接
     */
    private String mediaUrl;

    /**
     * 实体id
     */
    private Long entityId;

    /**
     * 小程序id
     */
    private String appid;

    /**
     * 建议，有risky、pass、review三种值
     */
    private String suggest;

    /**
     * 命中标签枚举值，100 正常；20001 时政；20002 色情；20006 违法犯罪；21000 其他
     */
    private Integer label;

    /**
     *
     */
    private String tenantId;

    /**
     *
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     *
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
