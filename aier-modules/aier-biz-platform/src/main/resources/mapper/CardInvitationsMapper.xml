<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.CardInvitationsMapper">

    <resultMap id="BaseResultMap" type="org.dromara.biz.domain.CardInvitations">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="cardId" column="card_id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="eventTime" column="event_time" jdbcType="TIMESTAMP"/>
            <result property="eventAddress" column="event_address" jdbcType="VARCHAR"/>
            <result property="latitude" column="latitude" jdbcType="DECIMAL"/>
            <result property="longitude" column="longitude" jdbcType="DECIMAL"/>
            <result property="coverImageUrl" column="cover_image_url" jdbcType="VARCHAR"/>
            <result property="text" column="text" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,card_id,name,
        event_time,event_address,latitude,
        longitude,cover_image_url,text,
        created_time,update_time,tenant_id
    </sql>
</mapper>
