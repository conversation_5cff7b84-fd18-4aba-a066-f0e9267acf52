<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.BizMotherRoundsMapper">
    <sql id="BaseColumns">
        SELECT
            u.nickname AS authorName,
            cc.service_start_date AS startTime,
            c.`name` AS momName,
            c.age AS age,
            (SELECT r.room_number FROM biz_rooms r WHERE r.room_id  = cc.room_id) AS roomNUmber,
            a.*
        FROM
            biz_mother_rounds a
                LEFT JOIN biz_customers c ON a.customer_id = c.customer_id
                LEFT JOIN biz_customer_contract cc ON c.customer_id = cc.customer_id
                LEFT JOIN biz_wechat_user u ON a.create_by = u.user_id
    </sql>    <select id="listVo" resultType="org.dromara.biz.domain.vo.nurse.MotherRoundsVO"
            parameterType="java.lang.Long">
    <include refid="BaseColumns"></include>
    <where>
        and c.customer_id = #{customerId}
    </where>
</select>
    <select id="getDatail" resultType="org.dromara.biz.domain.vo.nurse.MotherRoundsVO"
            parameterType="java.lang.Long">
        <include refid="BaseColumns"></include>
        <where>
            and a.id = #{id}
        </where>
    </select>
</mapper>
