<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.CardReplayMapper">

    <resultMap id="BaseResultMap" type="org.dromara.biz.domain.CardReplay">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="cardId" column="card_id" jdbcType="BIGINT"/>
            <result property="replayName" column="replay_name" jdbcType="VARCHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="replayNum" column="replay_num" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="replayStatus" column="replay_status" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,card_id,replay_name,
        tenant_id,replay_num,create_time,
        update_time,replay_status
    </sql>
</mapper>
