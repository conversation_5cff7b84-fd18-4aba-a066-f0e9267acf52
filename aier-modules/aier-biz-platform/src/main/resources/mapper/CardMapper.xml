<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.CardMapper">

    <resultMap id="BaseResultMap" type="org.dromara.biz.domain.Card">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="delFlag" column="del_flag" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,title,content,
        create_time,user_id,del_flag
    </sql>
    <select id="myCollect" resultType="org.dromara.biz.domain.vo.card.UserCardVO"
            parameterType="java.lang.Long">
            SELECT
                s.id  AS  templateId,
                s.del_flag AS delFlag,
                s.content,
                s.title,
                s.view_number,
                s.cover_image,
                s.share_thumb,
                s.is_published,
                s.page_num,
                s.type
            FROM
                l_opus  s
            WHERE
                s.id IN ( SELECT template_id FROM biz_card_collect WHERE user_id = #{userId} ) and s.del_flag = '0'
    </select>
</mapper>
