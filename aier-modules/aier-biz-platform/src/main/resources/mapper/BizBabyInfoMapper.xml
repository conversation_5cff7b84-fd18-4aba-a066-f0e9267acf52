<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.BizBabyInfoMapper">

    <select id="getByVo" resultType="org.dromara.biz.domain.vo.nurse.BabyInfoVO"
            parameterType="java.lang.Long">
        SELECT
            c.`name` AS momName,
            (SELECT r.room_number FROM biz_rooms r WHERE r.room_id  = cc.room_id) AS roomNUmber,
            cc.service_start_date AS startTime,
            a.*
        FROM
            biz_baby_info a
                LEFT JOIN biz_customers c ON a.customer_id = c.customer_id
                LEFT JOIN biz_customer_contract cc ON c.customer_id = cc.customer_id

        <where>
            and a.baby_id = #{babyId}
        </where>
    </select>
    <select id="getBabyAnalysis" resultType="org.dromara.biz.domain.vo.nurse.BabyCareVO">
        SELECT
        u.nickname AS authorName,
        b.baby_name AS babyName,
        cc.service_start_date AS startTime,
        a.*
        FROM
        biz_baby_care a
        LEFT JOIN biz_baby_info b ON a.baby_id = b.baby_id
        LEFT JOIN biz_customers c ON b.customer_id = c.customer_id
        LEFT JOIN biz_customer_contract cc ON c.customer_id  = cc.customer_id
        LEFT JOIN biz_wechat_user u ON a.create_by = u.user_id
        <where>
            <if test="babyId != null ">
                and a.baby_id = #{babyId}
            </if>
            <if test="startTime != null and endTime != null">
                and a.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
        </where>
    </select>
</mapper>
