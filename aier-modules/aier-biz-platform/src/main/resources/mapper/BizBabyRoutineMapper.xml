<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.BizBabyRoutineMapper">
    <sql id="BaseColumns">
        SELECT
            u.nickname AS authorName,
            b.baby_name AS babyName,
            cc.service_start_date AS startTime,
            a.*
        FROM
            biz_baby_routine a
                LEFT JOIN biz_baby_info b ON a.baby_id = b.baby_id
                LEFT JOIN biz_customers c ON b.customer_id = c.customer_id
                LEFT JOIN biz_customer_contract cc ON c.customer_id  = cc.customer_id
                LEFT JOIN biz_wechat_user u ON a.create_by = u.user_id
    </sql>
    <select id="listVo" resultType="org.dromara.biz.domain.vo.nurse.BabyRoutineVO"
            parameterType="java.lang.Long">
        <include refid="BaseColumns"></include>
        <where>
            and c.customer_id = #{customerId}
        </where>
    </select>
    <select id="getDetail" resultType="org.dromara.biz.domain.vo.nurse.BabyRoutineVO"
            parameterType="java.lang.Long">
        <include refid="BaseColumns"></include>
        <where>
            and a.id = #{id}
        </where>
    </select>
</mapper>
