<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.CheckInsMapper">

    <resultMap id="BaseResultMap" type="org.dromara.biz.domain.CheckIns">
            <id property="checkInId" column="check_in_id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="checkInDate" column="check_in_date" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        check_in_id,tenant_id,user_id,
        check_in_date,create_time,create_by,
        update_time,update_by
    </sql>
</mapper>
