<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.SuitesMapper">

<!--    <resultMap id="BaseResultMap" type="org.dromara.biz.domain.Suites">-->
<!--            <id property="suiteId" column="suite_id" jdbcType="BIGINT"/>-->
<!--            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>-->
<!--            <result property="roomName" column="room_name" jdbcType="VARCHAR"/>-->
<!--            <result property="roomType" column="room_type" jdbcType="VARCHAR"/>-->
<!--            <result property="orientation" column="orientation" jdbcType="VARCHAR"/>-->
<!--            <result property="bedType" column="bed_type" jdbcType="VARCHAR"/>-->
<!--            <result property="minFloor" column="min_floor" jdbcType="INTEGER"/>-->
<!--            <result property="maxFloor" column="max_floor" jdbcType="INTEGER"/>-->
<!--            <result property="minArea" column="min_area" jdbcType="DECIMAL"/>-->
<!--            <result property="maxArea" column="max_area" jdbcType="DECIMAL"/>-->
<!--            <result property="outdoorFeatures" column="outdoor_features" jdbcType="VARCHAR"/>-->
<!--            <result property="facilityFeatures" column="facility_features" jdbcType="VARCHAR"/>-->
<!--            <result property="suiteMediaFeatures" column="suite_media_features" jdbcType="VARCHAR"/>-->
<!--            <result property="bathroomFacilities" column="bathroom_facilities" jdbcType="VARCHAR"/>-->
<!--            <result property="cleaningFrequency" column="cleaning_frequency" jdbcType="VARCHAR"/>-->
<!--            <result property="sheetChangeFrequency" column="sheet_change_frequency" jdbcType="VARCHAR"/>-->
<!--            <result property="disinfectionFrequency" column="disinfection_frequency" jdbcType="VARCHAR"/>-->
<!--            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>-->
<!--            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>-->
<!--            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>-->
<!--            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>-->
<!--    </resultMap>-->

<!--    <sql id="Base_Column_List">-->
<!--        suite_id,tenant_id,room_name,-->
<!--        room_type,orientation,bed_type,-->
<!--        min_floor,max_floor,min_area,-->
<!--        max_area,outdoor_features,facility_features,-->
<!--        suite_media_features,bathroom_facilities,cleaning_frequency,-->
<!--        sheet_change_frequency,disinfection_frequency,create_by,-->
<!--        create_time,update_by,update_time-->
<!--    </sql>-->
</mapper>
