<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.InvitationReadMapper">

    <resultMap id="BaseResultMap" type="org.dromara.biz.domain.InvitationRead">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="masterId" column="master_id" jdbcType="BIGINT"/>
            <result property="readuserName" column="readuser_name" jdbcType="VARCHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="readOpenid" column="read_openid" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,master_id,readuser_name,
        tenant_id,read_openid,create_time,
        update_time
    </sql>
</mapper>
