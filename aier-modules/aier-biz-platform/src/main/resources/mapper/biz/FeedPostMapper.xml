<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.FeedPostMapper">

    <resultMap id="BaseResultMap" type="org.dromara.biz.domain.vo.FeedPostVO">
        <id column="post_id" property="postId"/>
        <result column="content" property="content"/>
        <result column="content_photos" property="contentPhotos"
                typeHandler="org.dromara.common.mybatis.handler.type.StringListTypeHandler"/>
        <result column="videos" property="videos"
                typeHandler="org.dromara.common.mybatis.handler.type.StringListTypeHandler"/>
        <result column="create_time" property="createTime"/>
        <result column="likes_count" property="likesCount"/>
        <result column="comments_count" property="commentsCount"/>
        <result column="share_count" property="shareCount"/>
        <result column="favorite_count" property="favoriteCount"/>
        <result column="type" property="type"/>
        <result column="club_name" property="clubName"/>
        <result column="user_id" property="userId"/>
        <result column="nickname" property="nickname"/>
        <result column="avatar" property="avatar"/>
        <result column="staff_post" property="staffPost"/>
        <result column="target_customer_id" property="targetCustomerId"/>
        <result column="room_id" property="roomId"/>
        <result column="mom_id" property="momId"/>
        <result column="age" property="age"/>
        <result column="title" property="title"/>
        <result column="baby_weight" property="babyWeight"/>
        <association property="staffInfo" resultMap="employeeResult"/>
        <association property="postTagInfo" resultMap="postTagResult"/>
        <association property="customerInfo" resultMap="customerResult"/>
        <association property="userInfo" resultMap="userResult"/>
        <collection property="comments" select="selectComments" column="post_id"/>
    </resultMap>

    <resultMap id="BaseClubResultMap" type="org.dromara.biz.domain.vo.FeedPostVO">
        <id column="post_id" property="postId"/>
        <result column="content" property="content"/>
        <result column="content_photos" property="contentPhotos"
                typeHandler="org.dromara.common.mybatis.handler.type.StringListTypeHandler"/>
        <result column="videos" property="videos"
                typeHandler="org.dromara.common.mybatis.handler.type.StringListTypeHandler"/>
        <result column="create_time" property="createTime"/>
        <result column="likes_count" property="likesCount"/>
        <result column="comments_count" property="commentsCount"/>
        <result column="share_count" property="shareCount"/>
        <result column="favorite_count" property="favoriteCount"/>
        <result column="title" property="title"/>
        <result column="type" property="type"/>
        <result column="nickname" property="nickname"/>
        <result column="avatar" property="avatar"/>
        <collection property="likeUsers" select="queryPostLinks" ofType="org.dromara.biz.domain.vo.FeedPostVO$LikeUsers"
                    column="post_id" javaType="java.util.List"/>
    </resultMap>
<resultMap id="CommentsChildrenMap" type="org.dromara.biz.domain.vo.FeedPostCommentVO">
    <id column="comment_id" property="commentId"/>
    <result column="nickname" property="nickname"/>
    <result column="comment" property="comment"/>
    <result column="post_id" property="postId"/>
    <result column="avatar" property="avatar"/>
    <result column="create_time" property="createTime"/>
    <collection property="childrenList" select="selectCommentsChildren" column="comment_id"/>
</resultMap>
    <resultMap id="employeeResult" type="org.dromara.biz.domain.vo.FeedPostVO$EmployeeInfo">
        <id column="staff_id" property="staffId"/>
        <result column="staff_post" property="staffPost"/>
        <result column="tag" property="tags" typeHandler="org.dromara.common.mybatis.handler.type.StringListTypeHandler"/>
        <result column="practice_time" property="practiceTime"/>
        <result column="service_num" property="serviceNum"/>
        <result column="service_time" property="serviceTime"/>
    </resultMap>

    <resultMap id="postTagResult" type="org.dromara.biz.domain.vo.FeedPostVO$PostTagInfo">
        <id column="task_node_id" property="tagId"/>
        <result column="node_name" property="tagName"/>
    </resultMap>

    <resultMap id="customerResult" type="org.dromara.biz.domain.vo.FeedPostVO$CustomerInfo">
        <id column="customer_id" property="customerId"/>
        <result column="customer_name" property="customerName"/>
        <result column="c_user_id" property="userId"/>
        <result column="check_in" property="checkIn"/>
        <result column="check_out" property="checkOut"/>
    </resultMap>

    <resultMap id="userResult" type="org.dromara.biz.domain.vo.FeedPostVO$UserInfo">
        <id column="user_id" property="userId"/>
        <result column="nickname" property="nickname"/>
        <result column="avatar" property="avatar"/>
    </resultMap>

    <select id="queryFeaturedNodeList" resultType="org.dromara.biz.domain.vo.FeedPostVO">
        SELECT
            f.*,
            n.node_name
        FROM
            biz_service_procedure_steps sps
                LEFT JOIN biz_feed_post f ON sps.post_id = f.post_id
                LEFT JOIN biz_task_node n ON sps.task_node_id = n.task_node_id
        WHERE
            n.is_featured = 1
        GROUP BY
            n.task_node_id
        ORDER BY
            n.is_featured DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

<!--    评论列表-->
    <select id="selectComments" resultMap="CommentsChildrenMap" >
        SELECT
            c.comment_id,
            c.user_id,
            u.nickname,
            c.`comment`,
            c.post_id,
            u.avatar,
            c.create_time
        FROM
            biz_feed_post_comment c
                LEFT JOIN biz_wechat_user u ON c.user_id = u.user_id
        WHERE c.post_id = #{postId}
        AND  c.parent_comment_id is null
    </select>

    <select id="selectCommentsChildren" resultType="org.dromara.biz.domain.vo.FeedPostCommentVO">
        SELECT
            c.comment_id,
            c.user_id,
            u.nickname,
            c.`comment`,
            c.post_id,
            u.avatar,
            c.create_time
        FROM
            biz_feed_post_comment c
                LEFT JOIN biz_wechat_user u ON c.user_id = u.user_id
        WHERE c.parent_comment_id = #{comment_id}
    </select>

    <sql id="selectFeedPost">
        select
            fp.title,
            fp.post_id,
            fp.content,
            fp.content_photos,
            fp.videos,
            fp.create_time,
            fp.likes_count,
            fp.comments_count,
            fp.share_count,
            fp.favorite_count,
            fp.type,
            cb.club_name,
            u.user_id,
            u.nickname,
            u.avatar,
            s.staff_id,
            s.staff_post,
            s.tag,
            s.service_num,
            s.practice_time,
            COALESCE(dm.service_start_date, tocc.check_in) AS service_time,
            tn.node_name,
            ps.task_node_id,
            ps.customer_id as target_customer_id,
            ps.room_id,
            c.customer_id,
            c.user_id as c_user_id,
            c.name as customer_name,
            dm.mom_id,
            dm.age,
            dm.baby_weight,
            COALESCE(dm.service_start_date,  occ.`check_in`) AS  check_in,
            occ.`check_out`,
            exists (select 1 from biz_feed_post_links fpl where fpl.post_id = fp.post_id and fpl.user_id = #{userId}) as is_like,
            exists (select 1 from biz_feed_post_favorite fpf where fpf.post_id = fp.post_id and fpf.user_id = #{userId}) as is_favorite,
            exists (select 1 from biz_feed_post_featured fe where fe.feed_post_id = fp.post_id) as is_featured
        from biz_feed_post fp
                 left join biz_service_procedure_steps ps on fp.post_id = ps.post_id
                 left join biz_wechat_user u on fp.author_id = u.user_id
                 left join biz_task_node tn on ps.task_node_id = tn.task_node_id
                 left join biz_staff s on s.user_id = u.user_id
                 left join biz_clubs cb on cb.tenant_id = fp.tenant_id
                 left join biz_customers c on fp.`author_id` = c.user_id
                 left join biz_room_occupations occ on c.customer_id = occ.customer_id
                 left join biz_room_occupations tocc on tocc.customer_id = ps.customer_id and tocc.status = 0 and ps.room_id = tocc.room_id
                 left join biz_diary_mom dm on u.tel = dm.tel
    </sql>

    <select id="getFeedPostPage" resultMap="BaseResultMap">
        <include refid="selectFeedPost"></include>
        ${ew.customSqlSegment}
    </select>

    <select id="getFeedPostList" resultMap="BaseResultMap">
        <include refid="selectFeedPost"></include>
        ${ew.customSqlSegment}
    </select>

    <select id="getDetail" resultMap="BaseResultMap">
        <include refid="selectFeedPost"></include>
        where fp.post_id = #{postId}
    </select>

    <select id="getClubDetail" resultMap="BaseClubResultMap">
        <include refid="BaseClubPostColumn"></include>
        where fp.post_id = #{postId}
    </select>

    <select id="queryPostLinks" parameterType="java.lang.Long" resultType="org.dromara.biz.domain.vo.FeedPostVO$LikeUsers">
        select
            u.user_id,
            pl.post_id,
            u.nickname,
            u.avatar,
            pl.link_time
        from `biz_feed_post_links` pl
                 left join `biz_wechat_user` u on pl.user_id = u.user_id
        where pl.post_id = #{postId}
    </select>

    <select id="queryPostComment" parameterType="java.lang.Long">
        select
            u.user_id,
            pc.post_id,
            u.nickname,
            u.avatar,
            pc.create_time as comment_time
        from `biz_feed_post_comment` pc
                 left join `biz_wechat_user` u on pc.user_id = u.user_id
        where pc.post_id = #{postId}
    </select>

    <select id="queryClubPost" resultMap="BaseClubResultMap">
        <include refid="BaseClubPostColumn"></include>
        ${ew.customSqlSegment}
    </select>

    <sql id="BaseClubPostColumn">
        select
            fp.title,
            fp.post_id,
            fp.content,
            fp.content_photos,
            fp.videos,
            fp.create_time,
            fp.likes_count,
            fp.comments_count,
            fp.share_count,
            fp.favorite_count,
            fp.type,
            cb.club_name as nickname,
            cb.logo as avatar,
            exists (select 1 from biz_feed_post_links fpl where fpl.post_id = fp.post_id and fpl.user_id = #{userId}) as is_like,
            exists (select 1 from biz_feed_post_favorite fpf where fpf.post_id = fp.post_id and fpf.user_id = #{userId}) as is_favorite,
            exists (select 1 from biz_feed_post_featured fe where fe.feed_post_id = fp.post_id) as is_featured
        from biz_feed_post fp
                 left join biz_wechat_user u on fp.author_id = u.user_id
                 left join biz_staff s on s.user_id = u.user_id
                 left join biz_clubs cb on cb.tenant_id = fp.tenant_id
    </sql>
</mapper>
