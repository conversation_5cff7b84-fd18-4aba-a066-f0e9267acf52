<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.FeedPostVideosMapper">

    <resultMap id="BaseResultMap" type="org.dromara.biz.domain.FeedPostVideos">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="postId" column="post_id" jdbcType="BIGINT"/>
            <result property="videoUrl" column="video_url" jdbcType="VARCHAR"/>
            <result property="traceId" column="trace_id" jdbcType="VARCHAR"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,post_id,video_url,
        trace_id,tenant_id
    </sql>
</mapper>
