<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.BizDiaryMapper">

    <select id="selectDiaryPage" resultType="org.dromara.biz.domain.vo.DiaryVO"
            parameterType="org.dromara.biz.domain.query.diary.DiaryQuery">
        <include refid="BaseColumns"></include>
        ${ew.customSqlSegment}
    </select>
    <select id="getDetail" resultType="org.dromara.biz.domain.vo.DiaryVO" parameterType="java.lang.String">
        <include refid="BaseColumns"></include>
        WHERE d.diary_id = #{diaryId}
    </select>

    <sql id="BaseColumns">
        SELECT
            d.* ,
            t.node_name,
            f.staff_post,
            f.staff_name,
            f.staff_photos,
            dt.name as item_name,
            (SELECT count(1) FROM biz_diary d1 WHERE d1.user_id = d.user_id) as user_count,
            d.imgs as contentPhotos,
            mom.name as mom_name,
            mom.uuid
        FROM
            biz_diary d
                LEFT JOIN biz_task_node t ON t.task_node_id = d.task_node_id
                LEFT JOIN biz_staff f ON f.user_id =  d.user_id
                LEFT JOIN biz_diary_item dt  ON dt.time_number = d.time_number AND dt.tenant_id = d.tenant_id
                LEFT JOIN biz_diary_mom mom ON d.mom_id  = mom.mom_id
    </sql>
</mapper>
