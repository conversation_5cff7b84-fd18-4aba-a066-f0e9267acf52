<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.CustomerRemindersMapper">

    <resultMap id="BaseResultMap" type="org.dromara.biz.domain.CustomerReminders">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="title" column="title" jdbcType="VARCHAR"/>
            <result property="description" column="description" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="customerId" column="customer_id" jdbcType="BIGINT"/>
            <result property="reminderDate" column="reminder_date" jdbcType="TIMESTAMP"/>
            <result property="status" column="status" jdbcType="BIT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,title,description,
        user_id,customer_id,reminder_date,
        status,create_time,update_time
    </sql>

    <select id="getDetail" resultType="org.dromara.biz.domain.CustomerReminders">
        SELECT
            cr.*,
            e.appid,
            e.openid
        FROM
            biz_customer_reminders cr
                LEFT JOIN biz_employee e ON cr.user_id = e.user_id
        WHERE cr.id = #{reminderId}
    </select>
</mapper>
