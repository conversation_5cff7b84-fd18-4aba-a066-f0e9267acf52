<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.WechatRoleMapper">

    <resultMap id="BaseResultMap" type="org.dromara.biz.domain.WechatRole">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="code" column="code" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="BaseColumn">
        select
            distinct
            r.`name`,
            r.`code`,
            r.id
        from
            biz_wechat_role r
                left join biz_wechat_user_role ur on r.id = ur.role_id
                left join biz_wechat_user u on ur.user_id = u.user_id
    </sql>

    <select id="selectRolePermissionByUserId" resultMap="BaseResultMap">
        <include refid="BaseColumn"/>
        where u.user_id = #{userId}
    </select>

    <select id="selectPermsByUserId" resultType="java.lang.String">
        select
            distinct p.perms
        from
            biz_wechat_perms p
                left join biz_wechat_role_perms rp on p.id = rp.perms_id
                left join biz_wechat_user_role ur on rp.role_id = ur.role_id
                left join biz_wechat_role r on r.id = ur.role_id
        where ur.user_id = #{userId}
    </select>
</mapper>
