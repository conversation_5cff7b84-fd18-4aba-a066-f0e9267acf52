<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.CustomerIntentLeveRecordMapper">

    <resultMap id="BaseResultMap" type="org.dromara.biz.domain.CustomerIntentLeveRecord">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="intentLeve" column="intent_leve" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="customerId" column="customer_id" jdbcType="BIGINT"/>
            <result property="operId" column="oper_id" jdbcType="BIGINT"/>
            <result property="note" column="note" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,intent_leve,create_time,
        customer_id,oper_id,note
    </sql>
</mapper>
