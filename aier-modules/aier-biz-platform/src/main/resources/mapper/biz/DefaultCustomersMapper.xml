<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.DefaultCustomersMapper">

    <resultMap id="BaseResultMap" type="org.dromara.biz.domain.DefaultCustomers">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="avatar" column="avatar" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="role" column="role" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,avatar,name,
        role
    </sql>
</mapper>
