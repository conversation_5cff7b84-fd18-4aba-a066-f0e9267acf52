<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.MamaMapper">

    <resultMap id="BaseResultMap" type="org.dromara.biz.domain.vo.customer.MamaInfoVO">
        <id column="user_id" property="userId"/>
        <result column="customer_id" property="customerId"/>
        <result column="nickname" property="nickName"/>
        <result column="avatar" property="avatar"/>
        <result column="role_code" property="roleCode"/>
        <result column="role_name" property="roleName"/>
        <result column="check_in_time" property="checkInTime"/>
        <result column="room_id" property="roomId"/>
        <collection property="employeeInfoList" select="getMamaEmployee" column="room_id"/>
        <collection property="tagInfoList" select="getMamaTags" column="room_id"/>
    </resultMap>

    <resultMap id="BaseMamaEmployeeMap" type="org.dromara.biz.domain.vo.customer.MamaInfoVO$EmployeeInfo">
        <id column="employee_id" property="employeeId"/>
        <result column="name" property="name"/>
        <result column="room_id" property="roomId"/>
        <result column="user_id" property="userId"/>
        <result column="staff_id" property="staffId"/>
        <result column="avatar" property="avatar"/>
    </resultMap>

    <resultMap id="BaseMamaTagMap" type="org.dromara.biz.domain.vo.customer.MamaInfoVO$TagInfo">
        <id column="task_node_id" property="id"/>
        <result column="node_name" property="name"/>
    </resultMap>

    <select id="getMamaInfo" resultMap="BaseResultMap">
        select
            u.user_id,
            c.customer_id,
            u.`nickname`,
            u.`avatar`,
            r.`code` as role_code,
            r.name as role_name,
            cc.`service_start_date` as check_in_time,
            cc.room_id
        from `biz_customers` c
            left join `biz_wechat_user` u on c.user_id = u.`user_id`
            left join `biz_wechat_user_role` ur on u.`user_id` = ur.`user_id`
            left join `biz_wechat_role` r on ur.`role_id` = r.id
            left join `biz_customer_contract` cc on c.customer_id = cc.customer_id
        where u.user_id = #{userId} and u.tenant_id = #{tenantId}
    </select>

    <select id="getMamaEmployee" resultMap="BaseMamaEmployeeMap">
        select e.`employee_id`,
               e.user_id,
               concat(e.name,'(',ifnull(r.name, '暂无'),')') as `name`,
               er.room_id,
               s.staff_id,
               u.avatar
        from `biz_employee` e
            left join `biz_employee_room` er on e.`employee_id` = er.`employee_id`
            left join `biz_wechat_user_role` ur on e.`user_id` = ur.`user_id`
            left join `biz_wechat_role` r on ur.`role_id` = r.id
            left join `biz_staff` s on s.user_id = e.user_id
            left join `biz_wechat_user` u on u.user_id = e.user_id
        where er.room_id is not null and e.user_id is not null and er.room_id = #{roomId}
    </select>

    <select id="getMamaTags" resultMap="BaseMamaTagMap">
        select tn.`task_node_id`, tn.`node_name` from `biz_feed_post` fp
            left join `biz_service_procedure_steps` ss on fp.post_id = ss.post_id
            left join `biz_task_node` tn on tn.`task_node_id` = ss.`task_node_id`
        where tn.`task_node_id` is not null and ss.room_id = #{roomId}
        group by tn.`task_node_id`
    </select>

</mapper>
