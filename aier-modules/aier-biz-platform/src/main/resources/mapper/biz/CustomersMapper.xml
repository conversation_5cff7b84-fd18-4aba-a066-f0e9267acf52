<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.CustomersMapper">

    <resultMap id="BaseResultMap" type="org.dromara.biz.domain.vo.CustomerVO">
        <id column="customer_id" property="customerId"/>
        <collection property="intentLeveRecords" select="selectCustomerIntentLeveRecordById" column="customer_id"/>
        <collection property="followUpLogs" select="selectCustomerFollowUpLogsById" column="customer_id"/>
        <collection property="operationLogs" select="selectCustomerOperationLogsById" column="customer_id"/>
    </resultMap>

    <!--查询最近10天过生日的客户-->
    <select id="queryBirthdayList" resultType="org.dromara.biz.domain.vo.CustomerBirthdayVO">
        SELECT
            name,
            birthday,
            TIMESTAMPDIFF(YEAR, birthday, CURDATE()) AS mom_age,
            tel AS tel,
            baby_gender AS baby_gender,
            DATEDIFF(DATE_FORMAT(CONCAT(YEAR(CURDATE()),'-',MONTH(birthday),'-',DAY(birthday)), '%Y-%m-%d'), CURDATE()) AS mom_birthday_num
        FROM
            biz_customers
        WHERE
            (DATEDIFF(CONCAT(YEAR(CURDATE()),'-',MONTH(birthday),'-',DAY(birthday)), CURDATE()) BETWEEN 0 AND 10 AND #{type} = 'mom')
        ORDER BY
            LEAST(mom_birthday_num)
    </select>

    <!--查询最近3天过生日的所有客户 包含：妈妈、宝宝-->
    <select id="queryAllBirthdayList" resultType="org.dromara.biz.domain.vo.CustomerBirthdayVO">
        SELECT
            customer_id,
            name,
            tel,
            CASE
                WHEN DATE(birthday) BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 2 DAY)
            THEN 'mom'
            ELSE NULL
        END AS birthday_type
        FROM biz_customers
        WHERE
          DATE(birthday) BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 3 DAY)
    </select>

    <!--查询最近10天过生日的所有客户数量 包含：妈妈、宝宝-->
    <select id="queryAllBirthdayCount" resultType="java.lang.Long">
        SELECT
            COUNT(1)
        FROM biz_customers
        WHERE
          DATE(birthday) BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 10 DAY)
    </select>

    <select id="selectCustomizePage" resultMap="BaseResultMap">
        <include refid="BaseCustomerColumns"></include>
        ${ew.customSqlSegment}
    </select>

    <select id="selectByPrimaryKey" resultMap="BaseResultMap">
        <include refid="BaseCustomerColumns"></include>
        <where>
            c.customer_id = #{id}
        </where>
    </select>

    <sql id="BaseCustomerColumns">
        select
            c.customer_id,
            c.user_id,
            c.name,
            c.tel,
            c.channel_id,
            c.source,
            c.status,
            c.age,
            c.gender,
            c.mom_birthday,
            c.wechat,
            c.address,
            c.notes,
            c.follower_id,
            c.due_date,
            c.intent_leve,
            c.customer_tag_id,
            c.last_operate_time,
            c.is_valid,
            c.is_to_store,
            c.is_arrived_at_store,
            c.is_contract,
            c.reminder_status,
            c.lead_status,
            c.create_time,
            c.update_time,
            c.arrived_at_store_number,
            c.id_card,
            u.avatar,
            u.nickname as follower_name,
            (SELECT nickname FROM biz_wechat_user WHERE user_id = c.create_by) create_user_name
        from biz_customers c
                 left join biz_wechat_user u on c.follower_id = u.user_id
                 left join biz_customer_contract cc on c.customer_id = cc.customer_id
    </sql>

    <!--查询客户意向度变更记录-->
    <select id="selectCustomerIntentLeveRecordById"
            resultType="org.dromara.biz.domain.CustomerIntentLeveRecord">
        select
            cil.intent_leve,
            cil.create_time,
            cil.note
        from biz_customer_intent_leve_record cil
        where cil.customer_id = #{customerId}
    </select>

    <!--查询客户跟进记录-->
    <select id="selectCustomerFollowUpLogsById"
            resultType="org.dromara.biz.domain.CustomerFollowUpLogs">
        select
            cl.notes,
            cl.follow_up_date,
            cl.create_time,
            cl.customer_tag_id,
            (select ct.tag_name from biz_customer_tag ct where cl.customer_tag_id = ct.id) as customer_tag_name
        from biz_customer_follow_up_logs cl
        where cl.customer_id = #{customerId}
    </select>

    <!--查询客户操作记录-->
    <select id="selectCustomerOperationLogsById"
            resultType="org.dromara.biz.domain.CustomerOperationLogs">
        select
            co.operation_type,
            co.operation_date,
            co.notes,
            (select u.nickname from biz_wechat_user u where u.user_id = co.user_id) as operation_name
        from biz_customer_operation_logs co
        where co.customer_id = #{customerId}
    </select>

    <select id="getAssignmentStats" resultType="org.dromara.biz.domain.vo.CustomerAssignmentStats">
        SELECT
            count( 1 ) AS total,
            sum( CASE WHEN c.follow_up_status = 'FOLLOWED_UP' THEN 1 ELSE 0 END ) AS followed_up_num,
            sum( CASE WHEN c.follow_up_status = 'NOT_FOLLOWED_UP' THEN 1 ELSE 0 END ) AS not_followed_up_num,
            sum( CASE WHEN c.assignment_status = 'ALLOCATED' THEN 1 ELSE 0 END ) AS allocated_num,
            sum( CASE WHEN c.assignment_status = 'NOT_ALLOCATED' THEN 1 ELSE 0 END ) AS not_allocated_num
        FROM
            biz_customers c
        <where>
            <if test="userId != null">
                and c.follower_id = #{userId}
            </if>
        </where>
    </select>

    <select id="getCustomerLeadStats"
            parameterType="org.dromara.biz.domain.query.customer.BuildCustomerStatsQuery"
            resultType="org.dromara.biz.domain.vo.customer.CustomerLeadStats">
        SELECT
            count( 1 ) AS leads,
            sum( CASE WHEN c.is_valid = 1 THEN 1 ELSE 0 END ) AS valid_leads,
            sum( CASE WHEN c.is_to_store = 1 THEN 1 ELSE 0 END ) AS arrivals
--             sum( CASE WHEN c.is_arrived_at_store = 1 THEN 1 ELSE 0 END ) AS deals,
--             sum(ifnull( cc.payment_amount, 0 )) AS amount
        FROM
            biz_customers c
                LEFT JOIN biz_customer_contract cc ON cc.customer_id = c.customer_id
        AND cc.actual_payment_type != 3
        <where>
            <include refid="BaseStatsCondition"></include>
        </where>
    </select>

    <select id="getCustomerDealsStats"
            parameterType="org.dromara.biz.domain.query.customer.BuildCustomerStatsQuery"
            resultType="org.dromara.biz.domain.vo.customer.CustomerLeadStats">
        SELECT
            count( 1 ) deals,
            sum(ifnull( cc.payment_amount, 0 )) AS amount
        FROM biz_customer_contract cc
                LEFT JOIN biz_customers c ON cc.customer_id = c.customer_id
        where
            cc.actual_payment_type != 3
            AND cc.is_cancel is null
            <include refid="BaseStatsCondition"></include>
    </select>

    <select id="getCustomerMyStats"
            parameterType="org.dromara.biz.domain.params.customer.CustomerStatsParams"
            resultType="org.dromara.biz.domain.vo.customer.CustomerInterestStats">
        SELECT
            sum(case when c.intent_leve = '高意向' then 1 else 0 end) as high_intent,
            sum(case when c.intent_leve = '中意向' then 1 else 0 end) as medium_intent,
            sum(case when c.intent_leve = '低意向' then 1 else 0 end) as low_intent
        FROM
            biz_customers c
        <where>
            <if test="userId != null">
                and c.follower_id = #{userId}
            </if>
            <if test="startTime != null and endTime != null">
                and c.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="reminderStatus != null">
                and c.reminder_status = #{reminderStatus}
            </if>
        </where>
    </select>


    <select id="getCustomerTagStats" resultType="org.dromara.biz.domain.vo.customer.CustomerInterestStats$TopIssueCategories">
        SELECT
            c.customer_tag_id,
            ct.tag_name,
            count( 1 ) as num
        FROM
            biz_customers c
                LEFT JOIN biz_customer_tag ct ON ct.id = c.customer_tag_id
        <where>
            c.customer_tag_id IS NOT NULL and ct.tag_name IS NOT NULL
            <if test="userId != null">
                and c.follower_id = #{userId}
            </if>
            <if test="startDate != null and endDate != null">
                and c.create_time BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="source != null and source != ''">
                and c.source = #{source}
            </if>
        </where>
        GROUP BY
            c.customer_tag_id
        ORDER BY
            num desc
    </select>

    <!--查询每个销售员的客户信息统计-->
    <select id="getCustomerSalesStats"
            parameterType="org.dromara.biz.domain.query.customer.BuildCustomerStatsQuery"
            resultType="org.dromara.biz.domain.vo.customer.CustomerSalesStatsVO">
        SELECT
            u.nickname AS salesperson,
            c.follower_id,
            count( 1 ) AS dispatched,
            sum( CASE WHEN c.is_to_store = 1 THEN 1 ELSE 0 END ) AS arrived,
            sum( CASE WHEN c.is_contract = 1 THEN 1 ELSE 0 END ) AS completed,
            IFNULL( sum( cc.payment_amount ), 0 ) AS transaction_amount
        FROM
            biz_customers c
                LEFT JOIN biz_wechat_user u ON c.follower_id = u.user_id
                LEFT JOIN biz_customer_contract cc ON c.customer_id = cc.customer_id
        WHERE
            c.follower_id IS NOT NULL
            <if test="userId != null">
                and c.follower_id = #{userId}
            </if>
            <if test="startDate != null and endDate != null">
                and c.create_time BETWEEN #{startDate} AND #{endDate}
            </if>
        GROUP BY
            c.follower_id
    </select>

    <select id="getCustomerSourceDealsStats"
            parameterType="org.dromara.biz.domain.query.customer.BuildCustomerStatsQuery"
            resultType="org.dromara.biz.domain.vo.customer.CustomerDealsStatsVO">
        SELECT
            count( 1 ) num,
            sum(ifnull( cc.payment_amount, 0 )) amount,
            c.source
        FROM
            biz_customer_contract cc
                LEFT JOIN biz_customers c ON c.customer_id = cc.customer_id
        WHERE
            cc.contract_sign_date between #{startDate} and #{endDate} and c.source = #{source}
        GROUP BY
            c.source
    </select>

    <select id="getCustomerSalesDealsStats"
            parameterType="org.dromara.biz.domain.query.customer.BuildCustomerStatsQuery"
            resultType="org.dromara.biz.domain.vo.customer.CustomerDealsStatsVO">
        SELECT
            count( 1 ) num,
            sum(ifnull( cc.payment_amount, 0 )) amount,
            c.follower_id
        FROM
            biz_customer_contract cc
                LEFT JOIN biz_customers c ON c.customer_id = cc.customer_id
        WHERE
            cc.contract_sign_date BETWEEN #{startDate} AND #{endDate} AND c.follower_id = #{followerId}
        GROUP BY
            c.follower_id
    </select>

    <!--查询每个渠道的客户信息统计-->
    <select id="getCustomerSourceStats"
            parameterType="org.dromara.biz.domain.query.customer.BuildCustomerStatsQuery"
            resultType="org.dromara.biz.domain.vo.customer.CustomerSourceStatsVO">
        SELECT
            c.source AS source,
            count( 1 ) AS dispatched,
            sum( CASE WHEN c.is_to_store = 1 THEN 1 ELSE 0 END ) AS arrived,
            sum( CASE WHEN c.is_contract = 1 THEN 1 ELSE 0 END ) AS completed,
            IFNULL( sum( cc.payment_amount ), 0 ) AS transaction_amount
        FROM
            biz_customers c
                LEFT JOIN biz_customer_contract cc ON c.customer_id = cc.customer_id
        <where>
            c.source IS NOT NULL
            <include refid="BaseStatsCondition"></include>
        </where>
        group by
            c.source
    </select>

    <!--查询每个渠道的客户意向度统计-->
    <select id="getCustomerSourceIntentStats"
            parameterType="org.dromara.biz.domain.query.customer.BuildCustomerStatsQuery"
            resultType="org.dromara.biz.domain.vo.customer.CustomerIntentSourceStatsVO">
        select
            count(if(c.intent_leve = '高意向', 1, null)) as high_intent,
            count(if(c.intent_leve = '中意向', 1, null)) as medium_intent,
            count(if(c.intent_leve = '低意向', 1, null)) as low_intent,
            c.source
        from biz_customers c
        <where>
            c.source is not null and c.source != ''
            <include refid="BaseStatsCondition"></include>
        </where>
        group by c.source
    </select>

    <!--查询每个员工的客户意向度统计-->
    <select id="getCustomerEmployeeIntentStats"
            parameterType="org.dromara.biz.domain.query.customer.BuildCustomerStatsQuery"
            resultType="org.dromara.biz.domain.vo.customer.CustomerIntentEmployeeStatsVO">
        select
            count(if(c.intent_leve = '高意向', 1)) as high_intent,
            count(if(c.intent_leve = '中意向', 1)) as medium_intent,
            count(if(c.intent_leve = '低意向', 1)) as low_intent,
            e.`name`
        from biz_customers c
                 left join biz_employee e on c.follower_id = e.user_id
        <where>
            c.follower_id is not null and e.`name` is not null
            <include refid="BaseStatsCondition"></include>
        </where>
        group by c.follower_id
    </select>

    <!--查询客户年龄分布统计-->
    <select id="getCustomerAgeRangeStats"
            parameterType="org.dromara.biz.domain.query.customer.BuildCustomerStatsQuery"
            resultType="org.dromara.biz.domain.vo.customer.CustomerAgeRangeStatsVO">
        select
            CASE
            WHEN TIMESTAMPDIFF(YEAR, c.birthday, CURDATE()) &lt;= 20 THEN '20以下'
            WHEN TIMESTAMPDIFF(YEAR, c.birthday, CURDATE()) BETWEEN 20 AND 25 THEN '20-25'
            WHEN TIMESTAMPDIFF(YEAR, c.birthday, CURDATE()) BETWEEN 26 AND 30 THEN '26-30'
            WHEN TIMESTAMPDIFF(YEAR, c.birthday, CURDATE()) BETWEEN 31 AND 35 THEN '31-35'
            ELSE '36以上' END AS age_range,
            count(1) num
        from
            biz_customers c
        <where>
            c.birthday is not null
            <include refid="BaseStatsCondition"></include>
        </where>
        group by age_range
    </select>

    <!--查询客户来源列表-->
    <select id="getCustomerSourceList" resultType="java.lang.String">
        select
            c.source
        from
            biz_customers c
        where
            c.source is not null and c.source != ''
        group by c.source
    </select>

    <!--查询客户收入统计-->
    <select id="getCustomerIncomeStats"
            parameterType="org.dromara.biz.domain.query.customer.BuildCustomerStatsQuery"
            resultType="org.dromara.biz.domain.vo.customer.CustomerIncomeStatsVO">
        select
            sum(ifnull(cc.deposit_amount, 0)) deposit_amount,
            sum(ifnull(cc.contract_amount, 0)) contract_amount,
            sum(ifnull(cc.other_amount, 0)) other_amount,
            sum(ifnull(cc.payment_amount, 0)) payment_amount
        from
            biz_customers c
        left join
            biz_customer_contract cc on c.customer_id = cc.customer_id
        <where>
            <include refid="BaseStatsCondition"></include>
        </where>
    </select>

    <!--查询客户套餐收入统计-->
    <select id="getCustomerPackageStats"
            parameterType="org.dromara.biz.domain.query.customer.BuildCustomerStatsQuery"
            resultType="org.dromara.biz.domain.vo.customer.CustomerPackageStatsVO">
        select
            sum(ifnull(cc.deposit_amount, 0)) deposit_amount,
            sum(ifnull(cc.contract_amount, 0)) contract_amount,
            sum(ifnull(cc.payment_amount, 0)) payment_amount,
            p.package_name,
            count(1) num
        from biz_customers c
                 left join biz_customer_contract cc on c.customer_id = cc.customer_id
                 left join biz_packages p on cc.meal_id = p.package_id
        where
            p.package_name is not null
            AND cc.is_cancel is NULL
            AND cc.actual_payment_type != 3
            <include refid="BaseStatsCondition"></include>
        group by cc.meal_id
        order by num desc
        limit 5
    </select>

    <!--查询每日客户统计-->
    <select id="getCustomerDayStoreStats"
            resultType="org.dromara.biz.domain.vo.customer.CustomerDayStoreStatsVO"
            parameterType="org.dromara.biz.domain.query.customer.BuildCustomerStatsQuery">
        select
            concat('第', week(c.create_time), '周') as `date`,
            count( 1 ) as dispatched,
            sum( case when c.is_to_store = 1 then 1 else 0 end ) as arrived,
            sum( case when c.is_contract = 1 then 1 else 0 end ) as completed,
            ifnull( sum( cc.payment_amount ), 0 ) as transaction_amount
        from biz_customers c
            left join biz_customer_contract cc on c.customer_id = cc.customer_id
        and  cc.actual_payment_type != 3
        <where>
            c.create_time is not null
            <include refid="BaseStatsCondition"></include>
        </where>
        group by `date`
    </select>

    <!--查询渠道每日交易统计-->
    <select id="getCustomerDayDealsStats"
            parameterType="org.dromara.biz.domain.query.customer.BuildCustomerStatsQuery"
            resultType="org.dromara.biz.domain.vo.customer.CustomerDayDealsStatsVO">
        select
            count( 1 ) completed,
            sum(ifnull( cc.payment_amount, 0 )) transaction_amount,
            concat('第', week(cc.contract_sign_date), '周') as `date`
        from
            biz_customer_contract cc
            left join biz_customers c on c.customer_id = cc.customer_id
        <where>
            c.create_time is not null
            <include refid="BaseStatsCondition"></include>
        </where>
        group by
            `date`
    </select>

    <sql id="BaseStatsCondition">
        <if test="startDate != null and endDate != null">
            and c.create_time between #{startDate} and #{endDate}
        </if>
        <if test="contractStartDate != null and contractEndDate != null">
            and cc.contract_sign_date between #{contractStartDate} and #{contractEndDate}
        </if>
        <if test="userId != null">
            and c.follower_id = #{userId}
        </if>
        <if test="followerId != null">
            and c.follower_id = #{followerId}
        </if>
        <if test="source != null">
            and c.source = #{source}
        </if>
    </sql>

    <select id="getByUserId" resultType="org.dromara.biz.domain.vo.CustomerVO">
        select
            c.*,
            r.room_id,
            r.room_number,
            occ.check_in,
            occ.check_out,
            occ.status as occ_status
        from biz_customers c
            left join biz_room_occupations occ on c.customer_id = occ.customer_id
            left join biz_rooms r on occ.room_id = r.room_id
        where c.user_id = #{userId} and occ.status = 0
    </select>
    <select id="getGoodMon" resultType="org.dromara.biz.domain.vo.CustomerVO">
        select
            c.customer_id as customerId,
            c.user_id as userId,
            c.tel,
            c.source,
            c.status,
            c.intent_leve as intentLeve,
            u.avatar,
            u.nickname as name
        from biz_customers c
                 left join biz_wechat_user u on c.user_id = u.user_id
    where  1=1
        <if test="customerIds != null and customerIds.size()> 0">
        and c.customer_id  IN
        <foreach item="item" index="index" collection="customerIds" open="(" separator="," close=")">
            #{item}
        </foreach >
        </if>
    </select>
    <select id="selectSeasCustomizePage" resultType="org.dromara.biz.domain.vo.CustomerVO">
    select
        c.customer_id,
        c.user_id,
        c.name,
        c.tel,
        c.channel_id,
        c.source,
        c.status,
        c.age,
        c.gender,
        c.birthday,
        c.wechat,
        c.address,
        c.notes,
        c.follower_id,
        c.due_date,
        c.intent_leve,
        c.customer_tag_id,
        c.last_operate_time,
        c.is_valid,
        c.is_to_store,
        c.is_arrived_at_store,
        c.is_contract,
        c.reminder_status,
        c.lead_status,
        c.create_time,
        c.update_time,
        c.arrived_at_store_number,
        u.avatar,
        u.nickname as follower_name,
        (SELECT nickname FROM biz_wechat_user WHERE user_id = c.create_by) create_user_name
    from biz_customers c
             left join biz_wechat_user u on c.follower_id = u.user_id
             left join biz_customer_contract cc on c.customer_id = cc.customer_id
        ${ew.customSqlSegment}
</select>
    <select id="getMomList" resultType="org.dromara.biz.domain.vo.CustomerVO">
        select
            c.customer_id,
            c.user_id,
            c.name,
            c.tel,
            c.channel_id,
            c.source,
            c.status,
            c.age,
            c.gender,
            c.birthday,
            c.wechat,
            c.address,
            c.notes,
            c.follower_id,
            c.due_date,
            c.intent_leve,
            c.customer_tag_id,
            c.last_operate_time,
            c.is_valid,
            c.is_to_store,
            c.is_arrived_at_store,
            c.is_contract,
            c.reminder_status,
            c.lead_status,
            c.create_time,
            c.update_time,
            c.arrived_at_store_number,
            u.avatar,
            u.nickname as follower_name,
            (SELECT nickname FROM biz_wechat_user WHERE user_id = c.create_by) create_user_name
        from biz_customer_contract cc
                 left join biz_customers c on c.customer_id = cc.customer_id
                 left join biz_wechat_user u on c.follower_id = u.user_id
    </select>
    <select id="getCustomerSalesTable" resultType="org.dromara.biz.domain.vo.customer.CustomerSalesTableVO"
            parameterType="org.dromara.biz.domain.query.customer.BuildCustomerStatsQuery">
        SELECT
            s.*,
            CAST( ( arrived / s.dispatched * 100 ) AS UNSIGNED ) AS storeRate,
            CAST( ( completed / s.dispatched * 100 ) AS UNSIGNED ) AS orderRate
        FROM
            (
                SELECT
                    e.`name` AS salesperson,
                    e.user_id AS followerId,
                    ( SELECT count( 1 ) FROM biz_customers c WHERE c.follower_id = e.user_id and c.create_time BETWEEN #{startDate} AND #{endDate}) AS dispatched,
                    (
                        SELECT
                            SUM( cc.payment_amount )
                        FROM
                            biz_customer_contract cc
                        WHERE
                            cc.customer_id IN ( SELECT c.customer_id FROM biz_customers c WHERE c.follower_id = e.user_id and c.create_time BETWEEN #{startDate} AND #{endDate})
                    ) transactionAmount,
                    ( SELECT count( 1 ) FROM biz_customers c WHERE c.follower_id = e.user_id AND is_to_store = 1 and  c.create_time BETWEEN #{startDate} AND #{endDate} ) AS arrived,
                    ( SELECT count( 1 ) FROM biz_customers c WHERE c.follower_id = e.user_id AND is_contract = 1 and c.create_time BETWEEN #{startDate} AND #{endDate} ) AS completed
                FROM
                    biz_employee e
                where e.disable = 0 and role_code = 'SALES'
            ) s ORDER BY s.dispatched DESC
    </select>
    <select id="getCustomerSourceTable" resultType="org.dromara.biz.domain.vo.customer.CustomerSalesTableVO"
            parameterType="org.dromara.biz.domain.query.customer.BuildCustomerStatsQuery">
        SELECT
            s.*,
            CAST( ( arrived / s.dispatched * 100 ) AS UNSIGNED ) AS storeRate,
            CAST( ( completed / s.dispatched * 100 ) AS UNSIGNED ) AS orderRate,
            (
                SELECT
                    SUM( cc.payment_amount )
                FROM
                    biz_customer_contract cc
                WHERE
                    cc.customer_id IN ( SELECT c.customer_id FROM biz_customers c WHERE c.is_contract = 1 AND c.source = s.salesperson and  c.create_time BETWEEN #{startDate} AND #{endDate})
                  and (cc.is_cancel = 0  OR cc.is_cancel is null )
            ) transactionAmount

        FROM
            (
                SELECT
                    count( 1 ) AS dispatched,
                    count( IF ( c.is_to_store = 1, 1, NULL ) ) AS arrived,
                    count( IF ( c.is_contract = 1, 1, NULL ) ) AS completed,
                    c.source AS salesperson
                FROM
                    biz_customers c
                where
                    c.create_time BETWEEN #{startDate} AND #{endDate}
                GROUP BY
                    c.source
            ) s ORDER BY s.dispatched DESC
    </select>
    <select id="getCustomerFollowUp" resultType="org.dromara.biz.domain.vo.customer.CustomerLeadStats"
            parameterType="org.dromara.biz.domain.query.customer.BuildCustomerStatsQuery">
        SELECT
        count( 1 ) followUp
        FROM
        biz_customer_follow_up_logs c
        LEFT JOIN biz_customers cc ON cc.customer_id = c.customer_id
        LEFT JOIN biz_wechat_user w ON c.user_id = w.user_id
        where w.user_id is not null
            <if test="startDate != null and endDate != null">
                and c.follow_up_date between #{startDate} and #{endDate}
            </if>
            <if test="userId != null">
                and c.user_id = #{userId}
            </if>
    </select>
    <select id="getFollowedLogsNumber" resultType="org.dromara.biz.domain.vo.CustomersFollowVO"
            parameterType="org.dromara.biz.domain.query.customer.CustomerFollowLogQuery">
        SELECT
            w.nickname as name,
            l.user_id as userId,
            COUNT(1) as upNumber

        FROM
            biz_customer_follow_up_logs l
                LEFT JOIN biz_wechat_user w ON l.user_id = w.user_id
        WHERE w.user_id is not null
        GROUP BY l.user_id
    </select>
</mapper>
