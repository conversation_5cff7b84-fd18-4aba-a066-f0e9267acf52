<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.DefaultNodesMapper">

    <resultMap id="BaseResultMap" type="org.dromara.biz.domain.DefaultNodes">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="nodeName" column="node_name" jdbcType="VARCHAR"/>
            <result property="shortOrder" column="short_order" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,node_name,short_order
    </sql>
</mapper>
