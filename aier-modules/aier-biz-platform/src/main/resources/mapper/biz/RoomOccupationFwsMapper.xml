<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.RoomOccupationFwsMapper">

    <resultMap id="BaseResultMap" type="org.dromara.biz.domain.RoomOccupationFws">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="occupationId" column="occupation_id" jdbcType="BIGINT"/>
            <result property="employeeId" column="employee_id" jdbcType="BIGINT"/>
            <result property="serviceTime" column="service_time" jdbcType="TIMESTAMP"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,occupation_id,employee_id,
        service_time,tenant_id,create_time,
        update_time
    </sql>
</mapper>
