<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.FeedPostNotificationMapper">

    <resultMap id="BaseResultMap" type="org.dromara.biz.domain.FeedPostNotification">
            <id property="notificationId" column="notification_id" jdbcType="BIGINT"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="senderId" column="sender_id" jdbcType="BIGINT"/>
            <result property="receiverId" column="receiver_id" jdbcType="BIGINT"/>
            <result property="postId" column="post_id" jdbcType="BIGINT"/>
            <result property="message" column="message" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="BIT"/>
            <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        notification_id,type,sender_id,
        receiver_id,post_id,message,
        status,create_by,create_time,
        update_by,update_time
    </sql>
</mapper>
