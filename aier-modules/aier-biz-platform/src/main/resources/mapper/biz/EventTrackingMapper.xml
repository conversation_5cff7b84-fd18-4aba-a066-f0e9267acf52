<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.EventTrackingMapper">

    <!--查询 总访问用户量、浏览总时间、总访问次数-->
    <select id="selectTotalStats" resultType="org.dromara.biz.domain.vo.TotalStatsVO">
        SELECT
            COUNT(1) as visit_count_total,
            sum(case when t.leave_time is not null then TIMESTAMPDIFF(SECOND, t.event_time, t.leave_time) else 0 end) visit_time_total,
            (select count(1) from biz_wechat_user) as user_total
        FROM
            biz_event_tracking t
    </select>


    <!--查询 用户总浏览总时间、总访问次数-->
    <select id="selectUserTotalStats" resultType="org.dromara.biz.domain.vo.TotalStatsVO">
        SELECT
            COUNT(1) AS visit_count_total,
            SUM(CASE WHEN t.leave_time IS NOT NULL THEN TIMESTAMPDIFF(SECOND, t.event_time, t.leave_time) ELSE 0 END) visit_time_total
        FROM
            biz_event_tracking t
        WHERE t.user_id = #{userId}
    </select>

    <!--查询时间段内每日访问用户数量-->
    <select id="selectVisitStatsGroupDay" resultType="org.dromara.biz.domain.vo.UserDayViewStatsVO">
        SELECT
            DATE (t.event_time) AS event_time,
            COUNT(DISTINCT t.user_id) AS uv,
            COUNT(1) AS pv
        FROM biz_event_tracking t
        <where>
            <if test="startDate != null and endDate != null">
                and t.event_time BETWEEN #{startDate} AND #{endDate}
            </if>
        </where>
        GROUP BY DATE(t.event_time)
        ORDER BY event_time
    </select>

    <!--查询指定时间内的访客数量-->
    <select id="selectVisitTotal" resultType="org.dromara.biz.domain.vo.UserVisitTotalStatsVO">
        SELECT
            uv,
            pv,
            AVG(uv) AS avg_uv,
            loss_rate_source,
            avg_duration_seconds
        FROM (
            SELECT
                COUNT(DISTINCT t.user_id) as uv,
                COUNT(1) as pv,
                SUM(CASE WHEN TIMESTAMPDIFF(SECOND, t.event_time, t.leave_time) &lt;= 5 THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS
                loss_rate_source,
                AVG(TIMESTAMPDIFF(SECOND, t.event_time, t.leave_time)) AS avg_duration_seconds
            FROM
            biz_event_tracking t
            <where>
                <if test="startDate != null and endDate != null">
                    and t.event_time BETWEEN #{startDate} AND #{endDate}
                </if>
            </where>
        ) AS subquery
    </select>

    <!--根据用户id查询用户每个模块的访问时间-->
    <select id="selectViewsTimeByEventType" resultType="org.dromara.biz.domain.vo.UserVisitDetailStatsVO">
        select
            u.nickname,
            u.tel,
            u.due_date,
            sum(case when t.module = 'room' then TIMESTAMPDIFF(second, t.event_time, t.leave_time) else 0 end) as room_stay_time,
            sum(case when t.module = 'community' then TIMESTAMPDIFF(second, t.event_time, t.leave_time) else 0 end) as community_stay_time,
            sum(case when t.module = 'meal' then TIMESTAMPDIFF(second, t.event_time, t.leave_time) else 0 end) as meal_stay_time,
            sum(case when t.module = 'recovery' then TIMESTAMPDIFF(second, t.event_time, t.leave_time) else 0 end) as recovery_stay_time,
            sum(case when t.module = 'review' then TIMESTAMPDIFF(second, t.event_time, t.leave_time) else 0 end) as review_stay_time,
            sum(case when t.module = 'staff' then TIMESTAMPDIFF(second, t.event_time, t.leave_time) else 0 end) as staff_stay_time
        from biz_wechat_user u
            left join biz_event_tracking t on u.user_id = t.user_id and t.leave_time is not null and t.event_type = 'PAGE_VIEW'
        <where>
            <if test="userId != null">
                and u.user_id = #{userId}
            </if>
        </where>
    </select>

    <!--查询用户近7天浏览次数-->
    <select id="selectViewsCountByUser" resultType="org.dromara.biz.domain.vo.UserDayStatsVO">
        select
            count(1) as pv,
            DATE (t.event_time) AS event_time
        from biz_event_tracking t
        <where>
            t.event_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            <if test="userId != null">
                and t.user_id = #{userId}
            </if>
        </where>
        group by DATE (t.event_time)
        order by event_time DESC
    </select>

    <!--查询模块统计 访问时长、访问次数、访问人数、分享次数、分享人数-->
    <select id="selectModuleStats"
            resultType="org.dromara.biz.domain.vo.EventTrackingModuleStatsVO"
            parameterType="org.dromara.biz.domain.query.EventTrackingBaseQuery">
        select
            t.module,
            TIMESTAMPDIFF(minute, t.event_time, t.leave_time) as view_time,
            count(1) as view_num,
            count(DISTINCT t.user_id) as user_num,
            count(case when t.event_type = 'SHARE' then 1 else 0 end) as share_num,
            (select count(DISTINCT t1.user_id) from biz_event_tracking t1 where t1.event_type = 'SHARE') as share_user_num
        from biz_event_tracking t
        where t.leave_time is not null and t.event_time BETWEEN #{startDate} AND #{endDate}
        group by t.module
    </select>

    <!--查询单个模块统计 访问时长、访问次数、访问人数、分享次数、分享人数-->
    <select id="selectOneModuleStats"
            parameterType="org.dromara.biz.domain.query.ModuleStatsQuery"
            resultType="org.dromara.biz.domain.vo.EventTrackingModuleStatsVO">
        select
            sum(TIMESTAMPDIFF(minute, t.event_time, t.leave_time)) as view_time,
            count(1) as view_num,
            count(DISTINCT t.user_id) as user_num,
            count(case when t.event_type = 'SHARE' then 1 else 0 end) as share_num,
            (select count(DISTINCT t1.user_id) from biz_event_tracking t1 where t1.event_type = 'SHARE') as share_user_num
        from biz_event_tracking t
        where t.module = #{module} and t.event_time BETWEEN #{startDate} AND #{endDate}
    </select>

    <!--查询模块每天访问次数、访问时长-->
    <select id="selectModuleStatsByDate"
            resultType="org.dromara.biz.domain.vo.ModuleDayViewStatsVO"
            parameterType="org.dromara.biz.domain.query.ModuleStatsQuery">
        SELECT
        DATE (t.event_time) AS event_time,
        sum(TIMESTAMPDIFF(minute, t.event_time, t.leave_time)) as view_time,
        COUNT(1) AS pv
        FROM biz_event_tracking t
        where t.module = #{module} and t.event_time BETWEEN #{startDate} AND #{endDate}
        GROUP BY DATE(t.event_time)
        ORDER BY event_time
    </select>

    <resultMap id="userModuleStatsVO" type="org.dromara.biz.domain.vo.UserTotalStatsVO$ModuleStats">
        <result column="module" property="module"/>
        <result column="duration" property="duration"/>
        <result column="view_num" property="viewNum"/>
        <collection property="pageStats" column="{userId=user_id,module=module}"
            select="selectUserModulePageStats" ofType="org.dromara.biz.domain.vo.UserTotalStatsVO$PageStats">
        </collection>
    </resultMap>

    <!--查询用户每个模块访问总次数和总时间-->
    <select id="selectUserModuleStats"
            resultMap="userModuleStatsVO">
        select
            t.module,
            t.user_id,
            sum(ifnull(TIMESTAMPDIFF(minute, t.event_time, t.leave_time),0)) as duration,
            count(1) as view_num
        from biz_event_tracking t
        where t.user_id = #{userId}
        group by t.module
    </select>

    <!--查询用户每个页面访问总次数和总时间-->
    <select id="selectUserModulePageStats"
            resultType="org.dromara.biz.domain.vo.UserTotalStatsVO$PageStats">
        select
            t.page_title,
            t.page_url,
            sum(ifnull(TIMESTAMPDIFF(minute, t.event_time, t.leave_time), 0)) as duration,
            count(1) as page_num
        from biz_event_tracking t
        where t.user_id = #{userId} and t.module = #{module}
        group by t.page_title
    </select>

    <!--按模块查询访问最长时间和访问最多次用户列表-->
    <select id="selectModuleUserList" parameterType="org.dromara.biz.domain.query.WechatUserQuery"
            resultType="org.dromara.biz.domain.vo.WechatUserVO">
        SELECT DISTINCT
            e.user_id,
            SUM(
                TIMESTAMPDIFF( SECOND, e.event_time, e.leave_time )) AS viewTime,
            COUNT( 1 ) AS viewNum,
            u.nickname,
            u.tel,
            u.last_login_ip,
            e.event_time AS lastLoginTime,
            u.avatar
        FROM
            biz_event_tracking e
                LEFT JOIN biz_wechat_user u ON e.user_id = u.user_id AND e.user_id IS NOT NULL
        WHERE
            e.module = #{params.module}
             AND u.last_login_ip is not null
        GROUP BY
            e.user_id
        <if test="params.orderBy != null and params.orderBy != ''">
            ORDER BY ${params.orderBy} DESC
        </if>
    </select>

</mapper>
