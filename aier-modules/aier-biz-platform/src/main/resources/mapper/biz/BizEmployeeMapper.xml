<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.BizEmployeeMapper">
    <select id="selectCustomPage"
            resultType="org.dromara.biz.domain.vo.employee.EmployeeVO">
        <include refid="BaseColumnSql"></include>
        ${ew.customSqlSegment}
    </select>

    <select id="selectCustomList" resultType="org.dromara.biz.domain.vo.employee.EmployeeVO">
        <include refid="BaseColumnSql"></include>
        ${ew.customSqlSegment}
    </select>

    <select id="detail" resultType="org.dromara.biz.domain.vo.employee.EmployeeVO">
        <include refid="BaseColumnSql"></include>
        ${ew.customSqlSegment}
    </select>

    <select id="getEmployeeRoomsByUserId" resultType="org.dromara.biz.domain.Rooms">
        select
            r.room_id,
            r.room_number
        from biz_employee_room er
            left join biz_employee e on er.employee_id = e.employee_id
            left join biz_rooms r on er.room_id = r.room_id
        where e.user_id = #{userId}
    </select>

    <sql id="BaseColumnSql">
        SELECT
            e.employee_id,
            e.`name`,
            e.gender,
            e.user_id,
            e.phone,
            e.hire_date,
            e.description,
            e.post_name,
            e.`disable`,
            e.audit_status,
            e.`password`,
            e.role_code,
            wu.nickname,
            t.`company_name`,
            t.tenant_id,
            r.id as role_id,
            r.name as role_name,
            sa.staff_photos AS photos,
            sa.staff_post,
            sa.staff_id
        FROM
            biz_employee e
                LEFT JOIN biz_wechat_user wu ON e.user_id = wu.user_id
                LEFT JOIN sys_tenant t ON e.tenant_id = t.tenant_id
                LEFT JOIN biz_wechat_user_role ur on wu.user_id = ur.user_id
                LEFT JOIN biz_wechat_role r on r.id = ur.role_id
                LEFT JOIN biz_staff sa ON e.user_id = sa.user_id
    </sql>

</mapper>
