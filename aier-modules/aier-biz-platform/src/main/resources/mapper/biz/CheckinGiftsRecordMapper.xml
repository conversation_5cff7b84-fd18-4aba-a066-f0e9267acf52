<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.CheckinGiftsRecordMapper">

    <resultMap id="BaseResultMap" type="org.dromara.biz.domain.CheckinGiftsRecord">
            <id property="checkGiftsId" column="check_gifts_id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="userId" column="user_id" jdbcType="BIGINT"/>
            <result property="giftsId" column="gifts_id" jdbcType="BIGINT"/>
            <result property="checkInDays" column="check_in_days" jdbcType="INTEGER"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="createBy" column="create_by" jdbcType="BIGINT"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="updateBy" column="update_by" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        check_gifts_id,tenant_id,user_id,
        gifts_id,check_in_days,create_time,
        create_by,update_time,update_by
    </sql>
</mapper>
