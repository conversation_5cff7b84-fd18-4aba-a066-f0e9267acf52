<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.CardTemplateMapper">

    <select id="queryPage" resultType="org.dromara.biz.domain.vo.card.CardTemplateVO" parameterType="org.dromara.biz.domain.query.card.CardQuery" >
        select
            <include refid="BaseColumn"></include>
        from l_opus
        <if test="params.tagIds != null and params.tagIds.size() > 0">
            where id in (
                select opus_id from l_opus_tag
                where tag_id in
                <foreach collection="params.tagIds" item="tagId" open="(" separator="," close=")">
                    #{tagId}
                </foreach>
            )
            and del_flag = '0' and is_published = 1
        </if>
        <if test="params.tagIds == null or params.tagIds.size() == 0">
            where del_flag = '0' and is_published = 1
        </if>
        <if test="params.type != null ">
            and type =  #{params.type}
        </if>
    </select>

    <select id="getTemplateDetail" resultType="org.dromara.biz.domain.vo.card.CardTemplateVO">
        select
            <include refid="BaseColumn"></include>
        from l_opus
        where del_flag = '0' and id = #{templateId} and is_published = 1
    </select>

    <sql id="BaseColumn">
        title, id, content, create_time, user_id, del_flag, view_number, cover_image, share_thumb, is_published,page_num,`type`
    </sql>

    <!-- 获取所有标签列表 -->
    <select id="getAllTags" resultType="org.dromara.biz.domain.vo.card.CardTagVO">
        SELECT
            id,
            tag_name as tagName,
            tag_desc as tagDesc,
            tag_color as tagColor,
            sort_order as sortOrder,
            create_time as createTime,
            update_time as updateTime
        FROM l_tag
        WHERE del_flag = '0'
        ORDER BY sort_order DESC, create_time DESC
    </select>
</mapper>
