<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.biz.mapper.WechatNotificationsMapper">

    <resultMap id="BaseResultMap" type="org.dromara.biz.domain.WechatNotifications">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="sendTime" column="send_time" jdbcType="TIMESTAMP"/>
            <result property="sendStatus" column="send_status" jdbcType="VARCHAR"/>
            <result property="recipientCount" column="recipient_count" jdbcType="INTEGER"/>
            <result property="senderCount" column="sender_count" jdbcType="INTEGER"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="type" column="type" jdbcType="VARCHAR"/>
            <result property="templateKey" column="template_key" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,content,send_time,
        send_status,recipient_count,sender_count,
        tenant_id,type,template_key,
        create_time,update_time
    </sql>
</mapper>
