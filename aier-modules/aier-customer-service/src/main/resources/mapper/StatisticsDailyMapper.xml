<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.cs.mapper.StatisticsDailyMapper">

    <resultMap id="BaseResultMap" type="org.dromara.cs.entity.StatisticsDaily">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="agentId" column="agent_id" jdbcType="BIGINT"/>
            <result property="statDate" column="stat_date" jdbcType="DATE"/>
            <result property="newConversationCount" column="new_conversation_count" jdbcType="INTEGER"/>
            <result property="closedConversationCount" column="closed_conversation_count" jdbcType="INTEGER"/>
            <result property="messageCount" column="message_count" jdbcType="INTEGER"/>
            <result property="userMessageCount" column="user_message_count" jdbcType="INTEGER"/>
            <result property="agentMessageCount" column="agent_message_count" jdbcType="INTEGER"/>
            <result property="avgResponseTime" column="avg_response_time" jdbcType="INTEGER"/>
            <result property="avgConversationDuration" column="avg_conversation_duration" jdbcType="INTEGER"/>
            <result property="evaluationCount" column="evaluation_count" jdbcType="INTEGER"/>
            <result property="evaluationScoreSum" column="evaluation_score_sum" jdbcType="INTEGER"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,agent_id,
        stat_date,new_conversation_count,closed_conversation_count,
        message_count,user_message_count,agent_message_count,
        avg_response_time,avg_conversation_duration,evaluation_count,
        evaluation_score_sum,created_time,updated_time
    </sql>
</mapper>
