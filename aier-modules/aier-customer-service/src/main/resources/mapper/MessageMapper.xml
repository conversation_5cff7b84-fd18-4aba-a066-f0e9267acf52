<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.dromara.cs.mapper.MessageMapper">

    <resultMap id="BaseResultMap" type="org.dromara.cs.entity.Message">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="tenantId" column="tenant_id" jdbcType="VARCHAR"/>
            <result property="conversationId" column="conversation_id" jdbcType="BIGINT"/>
            <result property="senderType" column="sender_type" jdbcType="OTHER"/>
            <result property="senderId" column="sender_id" jdbcType="BIGINT"/>
            <result property="receiverType" column="receiver_type" jdbcType="OTHER"/>
            <result property="receiverId" column="receiver_id" jdbcType="BIGINT"/>
            <result property="messageType" column="message_type" jdbcType="VARCHAR"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,tenant_id,conversation_id,
        sender_type,sender_id,receiver_type,
        receiver_id,message_type,content,
        status,created_time
    </sql>
</mapper>
