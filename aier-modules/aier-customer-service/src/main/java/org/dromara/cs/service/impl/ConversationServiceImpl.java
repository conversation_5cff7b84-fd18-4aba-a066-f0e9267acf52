package org.dromara.cs.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.dromara.cs.constant.MessageType;
import org.dromara.cs.entity.Agent;
import org.dromara.cs.entity.Conversation;
import org.dromara.cs.mapper.ConversationMapper;
import org.dromara.cs.service.AgentService;
import org.dromara.cs.service.ConversationService;
import org.dromara.cs.service.MessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cs_conversations(会话表)】的数据库操作Service实现
* @createDate 2025-03-27 16:09:35
*/
@Service
@Slf4j
public class ConversationServiceImpl extends ServiceImpl<ConversationMapper, Conversation>
    implements ConversationService {

    @Autowired
    private AgentService agentService;
    @Autowired
    @Lazy
    private MessageService messageService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Conversation getOrCreateConversation(Long userId) {
        // 查找用户当前活跃会话
        LambdaQueryWrapper<Conversation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Conversation::getUserId, userId)
                .in(Conversation::getStatus, 0, 1); // 待接入或进行中

        Conversation conversation = getOne(queryWrapper);

        if (ObjectUtil.isNull(conversation)) {
            // 创建新会话
            conversation = new Conversation();
            conversation.setUserId(userId);
            conversation.setStatus(0); // 待接入
            conversation.setUserUnreadCount(0);
            conversation.setAgentUnreadCount(0);
            conversation.setSource("MINI_PROGRAM");
            conversation.setCreatedTime(DateUtil.date());
            conversation.setUpdatedTime(DateUtil.date());
            conversation.setLastActiveAt(DateUtil.date());
            save(conversation);
            // 尝试分配客服
            assignAvailableAgent(conversation.getId());
        }
        // 发送问候语
        messageService.sendSystemMessage(conversation.getId(), "亲亲我在的，您是需要找婚宴酒店吗？需要什么帮助随时找我", MessageType.TEXT.name());
        return conversation;
    }

    @Override
    @Transactional
    public boolean assignAgent(Long conversationId, Long agentId) {
        Conversation conversation = getById(conversationId);
        if (conversation == null) {
            return false;
        }

        // 更新会话信息
        conversation.setAgentId(agentId);
        conversation.setStatus(1); // 进行中
        conversation.setUpdatedTime(DateUtil.date());
        boolean updated = updateById(conversation);

        if (updated) {
            // 增加客服当前接待量
            agentService.incrementCurrentChats(agentId);

            // 发送系统消息通知用户
            messageService.sendSystemMessage(conversationId, "客服已接入，请问有什么可以帮您？", "EVENT_AGENT_JOIN");
        }

        return updated;
    }

    @Override
    public List<Conversation> getConversationListForUser(Long userId) {
        LambdaQueryWrapper<Conversation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Conversation::getUserId, userId)
                .orderByDesc(Conversation::getLastActiveAt);

        return list(queryWrapper);
    }

    @Override
    public List<Conversation> getConversationListForAgent(Long agentId) {
        LambdaQueryWrapper<Conversation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Conversation::getAgentId, agentId)
                .in(Conversation::getStatus, 0, 1) // 待接入或进行中
                .orderByDesc(Conversation::getLastActiveAt);

        return list(queryWrapper);
    }

    @Override
    public List<Conversation> getPendingConversations() {
        LambdaQueryWrapper<Conversation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Conversation::getStatus, 0) // 待接入
                .orderByAsc(Conversation::getCreatedTime);
        return list(queryWrapper);
    }

    @Override
    @Transactional
    public boolean transferConversation(Long conversationId, Long targetAgentId) {
        Conversation conversation = getById(conversationId);
        if (conversation == null || conversation.getStatus() != 1) {
            return false;
        }

        Long originalAgentId = conversation.getAgentId();

        // 更新会话信息
        conversation.setAgentId(targetAgentId);
        conversation.setUpdatedTime(DateUtil.date());
        boolean updated = updateById(conversation);

        if (updated) {
            // 减少原客服当前接待量
            agentService.decrementCurrentChats(originalAgentId);

            // 增加目标客服当前接待量
            agentService.incrementCurrentChats(targetAgentId);

            // 发送系统消息通知用户
            messageService.sendSystemMessage(conversationId, "您的会话已被转接给其他客服", "EVENT_TRANSFER");
        }

        return updated;
    }

    @Override
    @Transactional
    public boolean closeConversation(Long conversationId, String closerType) {
        Conversation conversation = getById(conversationId);
        if (conversation == null || conversation.getStatus() > 1) {
            return false;
        }

        // 更新会话状态
        int status;
        if ("USER".equals(closerType)) {
            status = 2; // 用户结束
        } else if ("AGENT".equals(closerType)) {
            status = 3; // 客服结束
        } else {
            status = 4; // 系统结束
        }

        conversation.setStatus(status);
        conversation.setUpdatedTime(DateUtil.date());
        boolean updated = updateById(conversation);

        if (updated && conversation.getAgentId() != null) {
            // 减少客服当前接待量
            agentService.decrementCurrentChats(conversation.getAgentId());

            // 发送系统消息
            String content = "AGENT".equals(closerType) ? "客服已结束会话" : "会话已结束";
            messageService.sendSystemMessage(conversationId, content, "EVENT_END");
        }

        return updated;
    }

    @Override
    public boolean submitEvaluation(Long conversationId, Integer score, String content) {
        Conversation conversation = getById(conversationId);
        if (conversation == null) {
            return false;
        }

        conversation.setEvaluationScore(score);
        conversation.setEvaluationContent(content);
        conversation.setUpdatedTime(DateUtil.date());
        return updateById(conversation);
    }

    /**
     * 分配可用客服
     * @param conversationId 会话ID
     * @return 是否成功
     */
    private boolean assignAvailableAgent(Long conversationId) {
        Conversation conversation = getById(conversationId);
        if (conversation == null) {
            return false;
        }

        Agent agent = agentService.findAvailableAgent();

        if (agent != null) {
            return assignAgent(conversationId, agent.getId());
        }

        return false;
    }
}




