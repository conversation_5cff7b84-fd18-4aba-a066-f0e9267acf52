package org.dromara.cs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 会话表
 * @TableName cs_conversations
 */
@TableName(value ="cs_conversation")
@Data
public class Conversation {
    /**
     * 会话ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 所属租户ID
     */
    private String tenantId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 当前接待客服ID (NULL表示待分配或已结束)
     */
    private Long agentId;

    /**
     * 最后一条消息ID
     */
    private Long lastMessageId;

    /**
     * 用户未读消息数
     */
    private Integer userUnreadCount;

    /**
     * 客服未读消息数
     */
    private Integer agentUnreadCount;

    /**
     * 状态(0:待接入, 1:进行中, 2:用户结束, 3:客服结束, 4:系统结束)
     */
    private Integer status;

    /**
     * 来源(MINI_PROGRAM:小程序, OFFICIAL_ACCOUNT:公众号, WEB:网页)
     */
    private String source;

    /**
     * 来源详情 (如商品ID、页面路径等)
     */
    private String sourceInfo;

    /**
     * 评价分数(1-5)
     */
    private Integer evaluationScore;

    /**
     * 评价内容
     */
    private String evaluationContent;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

    /**
     * 最后活跃时间
     */
    private Date lastActiveAt;
}