package org.dromara.cs.websocket;

import cn.hutool.core.convert.Convert;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.cs.dto.WebSocketMessage;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * WebSocket会话管理器
 * 用于管理WebSocket会话和标识符的映射关系
 */
@Slf4j
@Component
public class WebSocketSessionManager {
    
    // 标识符 -> 会话
    private final Map<String, WebSocketSession> sessions = new ConcurrentHashMap<>();
    // 会话ID -> 标识符
    private final Map<String, String> sessionToIdentifier = new ConcurrentHashMap<>();
    // 用户ID -> 租户ID_用户类型_用户ID (方便根据用户ID找到会话)
    private final Map<String, String> userToIdentifier = new ConcurrentHashMap<>();

    /**
     * 注册会话
     * @param session WebSocket会话
     * @return 临时标识符
     */
    public String registerSession(WebSocketSession session) {
        Map<String, Object> attributes = session.getAttributes();
        String sessionId = session.getId();

        String tenantId = Convert.convert(String.class, attributes.get("tenantId"));
        Long userId = Convert.convert(Long.class, attributes.get("userId"));
        Long agentId = Convert.convert(Long.class, attributes.get("agentId"));
        String userType = Convert.convert(String.class, attributes.get("userType"));

        // 如果握手属性中没有，则使用临时标识，等待REGISTER消息
        if (tenantId == null || (userId == null && agentId == null) || userType == null) {
            String tempIdentifier = "temp_" + sessionId;
            sessions.put(tempIdentifier, session);
            sessionToIdentifier.put(sessionId, tempIdentifier);
            log.info("注册临时会话: {}", tempIdentifier);
            return tempIdentifier;
        }

        // 构建标识
        String identifier;
        if ("USER".equals(userType) && userId != null) {
            identifier = tenantId + "_USER_" + userId;
            // 维护用户ID到标识的映射
            userToIdentifier.put(userId.toString(), identifier);
        } else if ("AGENT".equals(userType) && agentId != null) {
            identifier = tenantId + "_AGENT_" + agentId;
            // 维护客服ID到标识的映射
            userToIdentifier.put("AGENT_" + agentId, identifier);
        } else {
            // 标识不完整，使用临时标识
            identifier = "temp_" + sessionId;
        }

        sessions.put(identifier, session);
        sessionToIdentifier.put(sessionId, identifier);
        log.info("注册会话: {}", identifier);
        return identifier;
//
//        String identifier = "temp_" + sessionId;
//        sessions.put(identifier, session);
//        sessionToIdentifier.put(sessionId, identifier);
//        log.info("Registered WebSocket session: {}, Temporary identifier: {}", sessionId, identifier);
//        return identifier;
    }

    /**
     * 更新标识符
     * @param session WebSocket会话
     * @param newIdentifier 新标识符
     */
    public void updateIdentifier(WebSocketSession session, String newIdentifier) {
        String sessionId = session.getId();
        String oldIdentifier = sessionToIdentifier.get(sessionId);
        if (oldIdentifier != null) {
            sessions.remove(oldIdentifier); // 移除旧的临时映射
        }
        sessions.put(newIdentifier, session);
        sessionToIdentifier.put(sessionId, newIdentifier);
        log.info("Updated WebSocket identifier for session {}: {}", sessionId, newIdentifier);
    }

    /**
     * 移除会话
     * @param session WebSocket会话
     */
    public void removeSession(WebSocketSession session) {
        String sessionId = session.getId();
        String identifier = sessionToIdentifier.remove(sessionId);
        if (identifier != null) {
            sessions.remove(identifier);
            log.info("Removed WebSocket session: {}, Identifier: {}", sessionId, identifier);
        }
    }

    /**
     * 获取会话
     * @param identifier 标识符
     * @return WebSocket会话
     */
    public WebSocketSession getSession(String identifier) {
        return sessions.get(identifier);
    }

    /**
     * 获取标识符
     * @param session WebSocket会话
     * @return 标识符
     */
    public String getIdentifier(WebSocketSession session) {
        return sessionToIdentifier.get(session.getId());
    }

    /**
     * 发送消息给指定标识的用户/客服
     * @param identifier 标识符
     * @param message 消息
     * @throws IOException 发送异常
     */
    public void sendMessage(String identifier, WebSocketMessage message) throws IOException {
        WebSocketSession session = getSession(identifier);
        if (session != null && session.isOpen()) {
            session.sendMessage(new TextMessage(JsonUtils.getObjectMapper().writeValueAsString(message)));
        } else {
            log.warn("Cannot send message, session not found or closed for identifier: {}", identifier);
            // 这里可以触发离线逻辑
        }
    }
}
