package org.dromara.cs.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.cs.dto.AgentQuery;
import org.dromara.cs.dto.CreateAgentDTO;
import org.dromara.cs.entity.Agent;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cs_agents(客服信息表)】的数据库操作Service
* @createDate 2025-03-27 16:09:35
*/
public interface AgentService extends IService<Agent> {

    /**
     * 更新客服状态
     * @param agentId 客服ID
     * @param status 状态(0:离线, 1:在线, 2:忙碌)
     * @return 是否成功
     */
    boolean updateAgentStatus(Long agentId, Integer status);

    /**
     * 获取在线客服列表
     * @return 在线客服列表
     */
    List<Agent> getOnlineAgents();

    /**
     * 创建客服
     */
    boolean createAgent(List<CreateAgentDTO> agentList);

    /**
     * 查找可用客服
     * @return 可用客服
     */
    Agent findAvailableAgent();

    /**
     * 增加客服当前接待量
     * @param agentId 客服ID
     * @return 是否成功
     */
    boolean incrementCurrentChats(Long agentId);

    /**
     * 减少客服当前接待量
     * @param agentId 客服ID
     * @return 是否成功
     */
    boolean decrementCurrentChats(Long agentId);

    TableDataInfo<Agent> queryPage(AgentQuery query);
}
