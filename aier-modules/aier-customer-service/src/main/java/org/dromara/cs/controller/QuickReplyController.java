//package org.dromara.cs.controller;
//
//
//import lombok.extern.slf4j.Slf4j;
//import org.dromara.common.core.domain.R;
//import org.dromara.cs.entity.QuickReplies;
//import org.dromara.cs.service.QuickRepliesService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
///**
// * 快捷回复控制器
// */
//@Slf4j
//@RestController
//@RequestMapping("/mp/quick-reply")
//public class QuickReplyController {
//
//    @Autowired
//    private QuickRepliesService quickReplyService;
//
//    /**
//     * 获取客服的快捷回复列表
//     * @param agentId 客服ID
//     * @return 快捷回复列表
//     */
//    @GetMapping("/agent")
//    public R<List<QuickReplies>> getAgentQuickReplies(@RequestParam Long agentId) {
//        List<QuickReplies> quickReplies = quickReplyService.getAgentQuickReplies(agentId);
//        return R.success(quickReplies);
//    }
//
//    /**
//     * 获取公共快捷回复列表
//     * @param tenantId 租户ID
//     * @return 快捷回复列表
//     */
//    @GetMapping("/public")
//    public ApiResponse<List<QuickReply>> getPublicQuickReplies(@RequestParam Long tenantId) {
//        try {
//            List<QuickReply> quickReplies = quickReplyService.getPublicQuickReplies(tenantId);
//            return ApiResponse.success(quickReplies);
//        } catch (Exception e) {
//            log.error("获取公共快捷回复列表失败", e);
//            return ApiResponse.error("获取公共快捷回复列表失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 创建快捷回复
//     * @param quickReply 快捷回复
//     * @return 创建结果
//     */
//    @PostMapping("/create")
//    public ApiResponse<Boolean> createQuickReply(@RequestBody QuickReply quickReply) {
//        try {
//            boolean result = quickReplyService.createQuickReply(quickReply);
//            return ApiResponse.success(result);
//        } catch (Exception e) {
//            log.error("创建快捷回复失败", e);
//            return ApiResponse.error("创建快捷回复失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 更新快捷回复
//     * @param quickReply 快捷回复
//     * @return 更新结果
//     */
//    @PostMapping("/update")
//    public ApiResponse<Boolean> updateQuickReply(@RequestBody QuickReply quickReply) {
//        try {
//            boolean result = quickReplyService.updateQuickReply(quickReply);
//            return ApiResponse.success(result);
//        } catch (Exception e) {
//            log.error("更新快捷回复失败", e);
//            return ApiResponse.error("更新快捷回复失败: " + e.getMessage());
//        }
//    }
//
//    /**
//     * 删除快捷回复
//     * @param id 快捷回复ID
//     * @return 删除结果
//     */
//    @DeleteMapping("/{id}")
//    public ApiResponse<Boolean> deleteQuickReply(@PathVariable Long id) {
//        try {
//            boolean result = quickReplyService.deleteQuickReply(id);
//            return ApiResponse.success(result);
//        } catch (Exception e) {
//            log.error("删除快捷回复失败", e);
//            return ApiResponse.error("删除快捷回复失败: " + e.getMessage());
//        }
//    }
//}
