package org.dromara.cs.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * WebSocket消息数据传输对象
 */
@Data
@NoArgsConstructor
public class WebSocketMessage {
    /**
     * 消息类型: REGISTER, MESSAGE, HEARTBEAT, SYSTEM_NOTICE, AGENT_TRANSFER, CHAT_END, etc.
     */
    private String type;
    
    /**
     * 发送者标识 (后端填充或校验) e.g., "tenant1_USER_123", "tenant1_AGENT_456"
     */
    private String senderId;
    
    /**
     * 接收者标识 e.g., "tenant1_USER_123", "tenant1_AGENT_456", "tenant1_SYSTEM"
     */
    private String receiverId;
    
    /**
     * 关联的会话ID
     */
    private Long conversationId;
    
    /**
     * 具体内容类型: TEXT, IMAGE, CARD_ACTIVITY, etc. (同数据库字段)
     */
    private String messageType;
    
    /**
     * 消息内容 (JSON for cards)
     */
    private String content;
    
    /**
     * 消息时间戳 (毫秒)
     */
    private Long timestamp;
    
    /**
     * 附加数据，根据type不同而不同，如转接时的目标客服ID
     */
    private Object payload;
}
