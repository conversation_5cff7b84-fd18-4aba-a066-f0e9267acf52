package org.dromara.cs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 常见问题表
 * @TableName cs_faqs
 */
@TableName(value ="cs_faq")
@Data
public class Faq {
    /**
     * 问题ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 所属租户ID
     */
    private String tenantId;

    /**
     * 问题
     */
    private String question;

    /**
     * 答案
     */
    private String answer;

    /**
     * 分类
     */
    private String category;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 是否自动发送(0:否, 1:是)
     */
    private Integer isAutoSend;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;
}