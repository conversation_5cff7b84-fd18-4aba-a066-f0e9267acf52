package org.dromara.cs.controller;

import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.cs.entity.Conversation;
import org.dromara.cs.entity.Message;
import org.dromara.cs.service.ConversationService;
import org.dromara.cs.service.MessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 会话控制器
 */
@Slf4j
@RestController
@RequestMapping("/mp/conversation")
public class ConversationController {

    @Autowired
    private ConversationService conversationService;

    @Autowired
    private MessageService messageService;

    /**
     * 获取或创建会话
     * @return 会话信息
     */
    @GetMapping("/get-or-create")
    public R<Conversation> getOrCreateConversation() {
        Long userId = LoginHelper.getUserId();
        Conversation conversation = conversationService.getOrCreateConversation(userId);
        return R.success(conversation);
    }

    /**
     * 获取用户会话列表
     * @param userId 用户ID
     * @return 会话列表
     */
    @GetMapping("/user-list")
    public R<List<Conversation>> getConversationListForUser(@RequestParam Long userId) {
        List<Conversation> conversations = conversationService.getConversationListForUser(userId);
        return R.success(conversations);
    }

    /**
     * 获取客服会话列表
     * @param agentId 客服ID
     * @return 会话列表
     */
    @GetMapping("/agent-list")
    public R<List<Conversation>> getConversationListForAgent(@RequestParam Long agentId) {
        List<Conversation> conversations = conversationService.getConversationListForAgent(agentId);
        return R.success(conversations);
    }

    /**
     * 获取待接入会话列表
     * @return 待接入会话列表
     */
    @GetMapping("/pending-list")
    public R<List<Conversation>> getPendingConversations() {
        List<Conversation> conversations = conversationService.getPendingConversations();
        return R.success(conversations);
    }

    /**
     * 分配客服
     * @param conversationId 会话ID
     * @param agentId 客服ID
     * @return 分配结果
     */
    @PostMapping("/assign")
    public R<Boolean> assignAgent(@RequestParam Long conversationId, @RequestParam Long agentId) {
        boolean result = conversationService.assignAgent(conversationId, agentId);
        return R.success(result);
    }

    /**
     * 转接会话
     * @param conversationId 会话ID
     * @param targetAgentId 目标客服ID
     * @return 转接结果
     */
    @PostMapping("/transfer")
    public R<Boolean> transferConversation(@RequestParam Long conversationId, @RequestParam Long targetAgentId) {
        boolean result = conversationService.transferConversation(conversationId, targetAgentId);
        return R.success(result);
    }

    /**
     * 结束会话
     * @param conversationId 会话ID
     * @param closerType 结束者类型 (USER, AGENT, SYSTEM)
     * @return 结束结果
     */
    @PostMapping("/close")
    public R<Boolean> closeConversation(@RequestParam Long conversationId, @RequestParam String closerType) {
        boolean result = conversationService.closeConversation(conversationId, closerType);
        return R.success(result);
    }

    /**
     * 提交会话评价
     * @param conversationId 会话ID
     * @param score 评分
     * @param content 评价内容
     * @return 评价结果
     */
    @PostMapping("/evaluate")
    public R<Boolean> submitEvaluation(@RequestParam Long conversationId, @RequestParam Integer score, @RequestParam(required = false) String content) {
        boolean result = conversationService.submitEvaluation(conversationId, score, content);
        return R.success(result);
    }

    /**
     * 获取历史消息
     * @param conversationId 会话ID
     * @param page 页码
     * @param size 每页大小
     * @return 消息列表
     */
    @GetMapping("/messages")
    public R<List<Message>> getHistoryMessages(@RequestParam Long conversationId, @RequestParam(defaultValue = "1") int page, @RequestParam(defaultValue = "20") int size) {
        List<Message> messages = messageService.getHistoryMessages(conversationId, page, size);
        return R.success(messages);
    }

    /**
     * 标记消息为已读
     * @param conversationId 会话ID
     * @param receiverType 接收者类型 (USER, AGENT)
     * @return 标记结果
     */
    @PostMapping("/read")
    public R<Boolean> markMessagesAsRead(@RequestParam Long conversationId, @RequestParam String receiverType) {
        boolean result = messageService.markMessagesAsRead(conversationId, receiverType);
        return R.success(result);
    }
}
