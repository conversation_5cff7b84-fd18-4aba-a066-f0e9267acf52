package org.dromara.cs.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 快捷回复表
 * @TableName cs_quick_replies
 */
@TableName(value ="cs_quick_replie")
@Data
public class QuickReplie {
    /**
     * 快捷回复ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 所属租户ID
     */
    private String tenantId;

    /**
     * 所属客服ID (NULL表示公共快捷回复)
     */
    private Long agentId;

    /**
     * 标题
     */
    private String title;

    /**
     * 内容
     */
    private String content;

    /**
     * 排序
     */
    private Integer sortOrder;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;
}