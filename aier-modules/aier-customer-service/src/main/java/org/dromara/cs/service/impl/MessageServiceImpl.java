package org.dromara.cs.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.cs.dto.WebSocketMessage;
import org.dromara.cs.entity.Conversation;
import org.dromara.cs.entity.Message;
import org.dromara.cs.mapper.MessageMapper;
import org.dromara.cs.service.ConversationService;
import org.dromara.cs.service.MessageService;
import org.dromara.cs.websocket.WebSocketSessionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cs_messages(消息表)】的数据库操作Service实现
* @createDate 2025-03-27 16:09:35
*/
@Service
@Slf4j
public class MessageServiceImpl extends ServiceImpl<MessageMapper, Message>
    implements MessageService {

    @Autowired
    private WebSocketSessionManager sessionManager;

    @Autowired
    @Lazy
    private ConversationService conversationService;

    @Override
    @Transactional
    public Message saveMessage(WebSocketMessage wsMessage) {
        Message message = new Message();
        message.setTenantId(extractTenantId(wsMessage.getSenderId()));
        message.setConversationId(wsMessage.getConversationId());
        message.setSenderType(extractUserType(wsMessage.getSenderId()));
        message.setSenderId(extractUserId(wsMessage.getSenderId()));
        message.setReceiverType(extractUserType(wsMessage.getReceiverId()));
        message.setReceiverId(extractUserId(wsMessage.getReceiverId()));
        message.setMessageType(wsMessage.getMessageType());
        message.setContent(wsMessage.getContent());
        message.setStatus(0); // 未读
        message.setCreatedTime(DateUtil.date());

        save(message);

        // 更新会话最后活跃时间和未读数
        updateConversation(message);

        return message;
    }

    @Override
    public void handleWebSocketMessage(WebSocketMessage wsMessage) {
        // 保存消息到数据库
        Message message = saveMessage(wsMessage);

        // 设置消息ID和时间戳
        wsMessage.setTimestamp(System.currentTimeMillis());

        // 发送消息给接收者
        try {
            sessionManager.sendMessage(wsMessage.getReceiverId(), wsMessage);

            // 如果是普通消息，也发送给发送者（确认消息已发送）
            if ("MESSAGE".equals(wsMessage.getType())) {
                sessionManager.sendMessage(wsMessage.getSenderId(), wsMessage);
            }
        } catch (Exception e) {
            log.error("Failed to send WebSocket message", e);
            // 如果发送失败，触发离线通知
            triggerOfflineNotification(wsMessage);
        }
    }

    @Override
    public List<Message> getHistoryMessages(Long conversationId, int page, int size) {
        Page<Message> messagePage = new Page<>(page, size);
        LambdaQueryWrapper<Message> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Message::getConversationId, conversationId)
                .orderByAsc(Message::getCreatedTime);

        return page(messagePage, queryWrapper).getRecords();
    }

    @Override
    @Transactional
    public Message sendSystemMessage(Long conversationId, String content, String systemMessageType) {
        Conversation conversation = conversationService.getById(conversationId);
        if (conversation == null) {
            return null;
        }

        // 创建系统消息
        Message message = new Message();
        message.setTenantId(conversation.getTenantId());
        message.setConversationId(conversationId);
        message.setSenderType("SYSTEM");
        message.setSenderId(0L); // 系统ID为0

        // 接收者为用户
        message.setReceiverType("USER");
        message.setReceiverId(conversation.getUserId());

        message.setMessageType(systemMessageType);
        message.setContent(content);
        message.setStatus(0); // 未读
        message.setCreatedTime(DateUtil.date());

        save(message);

        // 更新会话最后活跃时间和未读数
        updateConversation(message);

        // 创建WebSocket消息并发送
        WebSocketMessage wsMessage = new WebSocketMessage();
        wsMessage.setType("SYSTEM_NOTICE");
        wsMessage.setSenderId(conversation.getTenantId() + "_SYSTEM_0");
        wsMessage.setReceiverId(conversation.getTenantId() + "_USER_" + conversation.getUserId());
        wsMessage.setConversationId(conversationId);
        wsMessage.setMessageType(systemMessageType);
        wsMessage.setContent(content);
        wsMessage.setTimestamp(System.currentTimeMillis());

        try {
            sessionManager.sendMessage(wsMessage.getReceiverId(), wsMessage);

            // 如果有客服，也发送给客服
            if (conversation.getAgentId() != null) {
                String agentIdentifier = conversation.getTenantId() + "_AGENT_" + conversation.getAgentId();
                sessionManager.sendMessage(agentIdentifier, wsMessage);
            }
        } catch (Exception e) {
            log.error("Failed to send system message", e);
            // 如果发送失败，触发离线通知
            triggerOfflineNotification(wsMessage);
        }

        return message;
    }

    @Override
    public void triggerOfflineNotification(WebSocketMessage wsMessage) {
        // 实现微信订阅消息推送
        // 这里需要调用微信API发送订阅消息
        log.info("Triggering offline notification for message: {}", JsonUtils.toJsonString(wsMessage));

        // TODO: 实现微信订阅消息推送
        // 1. 获取接收者的OpenID
        // 2. 检查是否已授权接收订阅消息
        // 3. 调用微信API发送订阅消息
    }

    @Override
    public void pushOfflineMessages(String identifier) {
        // 获取用户/客服ID和类型
        String userType = extractUserType(identifier);
        Long userId = extractUserId(identifier);
        String tenantId = extractTenantId(identifier);

        if (userId == null || tenantId == null) {
            return;
        }

        // 查找未读消息
        LambdaQueryWrapper<Message> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Message::getTenantId, tenantId)
                .eq(Message::getReceiverType, userType)
                .eq(Message::getReceiverId, userId)
                .eq(Message::getStatus, 0) // 未读
                .orderByAsc(Message::getCreatedTime);

        List<Message> messages = list(queryWrapper);

        // 推送未读消息
        for (Message message : messages) {
            WebSocketMessage wsMessage = new WebSocketMessage();
            wsMessage.setType("MESSAGE");
            wsMessage.setSenderId(tenantId + "_" + message.getSenderType() + "_" + message.getSenderId());
            wsMessage.setReceiverId(identifier);
            wsMessage.setConversationId(message.getConversationId());
            wsMessage.setMessageType(message.getMessageType());
            wsMessage.setContent(message.getContent());
            wsMessage.setTimestamp(message.getCreatedTime().toInstant().toEpochMilli());

            try {
                sessionManager.sendMessage(identifier, wsMessage);
            } catch (Exception e) {
                log.error("Failed to push offline message", e);
            }
        }
    }

    @Override
    @Transactional
    public boolean markMessagesAsRead(Long conversationId, String receiverType) {
        LambdaQueryWrapper<Message> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Message::getConversationId, conversationId)
                .eq(Message::getReceiverType, receiverType)
                .eq(Message::getStatus, 0); // 未读

        Message updateMessage = new Message();
        updateMessage.setStatus(1); // 已读

        boolean updated = update(updateMessage, queryWrapper);

        if (updated) {
            // 更新会话未读数
            Conversation conversation = conversationService.getById(conversationId);
            if (conversation != null) {
                if ("USER".equals(receiverType)) {
                    conversation.setUserUnreadCount(0);
                } else if ("AGENT".equals(receiverType)) {
                    conversation.setAgentUnreadCount(0);
                }
                conversation.setUpdatedTime(DateUtil.date());
                conversationService.updateById(conversation);
            }
        }

        return updated;
    }

    /**
     * 更新会话最后活跃时间和未读数
     * @param message 消息
     */
    private void updateConversation(Message message) {
        Conversation conversation = conversationService.getById(message.getConversationId());
        if (conversation != null) {
            // 更新最后一条消息ID
            conversation.setLastMessageId(message.getId());

            // 更新最后活跃时间
            conversation.setLastActiveAt(DateUtil.date());

            // 更新未读数
            if ("USER".equals(message.getSenderType())) {
                // 用户发送的消息，增加客服未读数
                conversation.setAgentUnreadCount(conversation.getAgentUnreadCount() + 1);
            } else if ("AGENT".equals(message.getSenderType())) {
                // 客服发送的消息，增加用户未读数
                conversation.setUserUnreadCount(conversation.getUserUnreadCount() + 1);
            }

            conversation.setUpdatedTime(DateUtil.date());
            conversationService.updateById(conversation);
        }
    }

    /**
     * 从标识符中提取用户类型
     * @param identifier 标识符
     * @return 用户类型
     */
    private String extractUserType(String identifier) {
        System.out.println("extractUserType: " + identifier);
        if (identifier == null) {
            return null;
        }

        String[] parts = identifier.split("_");
        if (parts.length >= 2) {
            return parts[1];
        }

        return null;
    }

    /**
     * 从标识符中提取用户ID
     * @param identifier 标识符
     * @return 用户ID
     */
    private Long extractUserId(String identifier) {
        if (identifier == null) {
            return null;
        }

        String[] parts = identifier.split("_");
        if (parts.length >= 3) {
            try {
                return Long.parseLong(parts[2]);
            } catch (NumberFormatException e) {
                return null;
            }
        }

        return null;
    }

    /**
     * 从标识符中提取租户ID
     * @param identifier 标识符
     * @return 租户ID
     */
    private String extractTenantId(String identifier) {
        if (identifier == null) {
            return null;
        }
        String[] parts = identifier.split("_");
        if (parts.length >= 1) {
            try {
                return String.valueOf(parts[0]);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        return null;
    }

}




