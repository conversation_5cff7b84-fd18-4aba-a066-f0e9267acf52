package org.dromara.cs.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.dromara.cs.dto.WebSocketMessage;
import org.dromara.cs.entity.Message;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【cs_messages(消息表)】的数据库操作Service
* @createDate 2025-03-27 16:09:35
*/
public interface MessageService extends IService<Message> {

    /**
     * 保存消息
     * @param wsMessage WebSocket消息
     * @return 保存的消息对象
     */
    Message saveMessage(WebSocketMessage wsMessage);

    /**
     * 处理WebSocket消息
     * @param wsMessage WebSocket消息
     */
    void handleWebSocketMessage(WebSocketMessage wsMessage);

    /**
     * 获取历史消息
     * @param conversationId 会话ID
     * @param page 页码
     * @param size 每页大小
     * @return 消息列表
     */
    List<Message> getHistoryMessages(Long conversationId, int page, int size);

    /**
     * 发送系统消息
     * @param conversationId 会话ID
     * @param content 消息内容
     * @param systemMessageType 系统消息类型
     * @return 发送的消息对象
     */
    Message sendSystemMessage(Long conversationId, String content, String systemMessageType);

    /**
     * 触发离线通知
     * @param wsMessage WebSocket消息
     */
    void triggerOfflineNotification(WebSocketMessage wsMessage);

    /**
     * 推送离线消息
     * @param identifier 用户/客服标识
     */
    void pushOfflineMessages(String identifier);

    /**
     * 标记消息为已读
     * @param conversationId 会话ID
     * @param receiverType 接收者类型
     * @return 是否成功
     */
    boolean markMessagesAsRead(Long conversationId, String receiverType);
}
