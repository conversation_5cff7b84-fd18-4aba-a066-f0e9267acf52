/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.ai.domain.query;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.common.mybatis.core.page.PageQuery;

/**
 * 知识库查询对象
 *
 * <AUTHOR> <PERSON>uppy
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class KnowledgeBaseQuery extends PageQuery {

    /**
     * 知识库名称（模糊查询）
     */
    private String knowledgeName;

    /**
     * 知识库状态（0正常 1停用）
     */
    private String status;

    /**
     * 知识库类型（PUBLIC公共 PRIVATE私有）
     */
    private String knowledgeType;

    /**
     * 关键字搜索（名称或描述）
     */
    private String keyword;

}
