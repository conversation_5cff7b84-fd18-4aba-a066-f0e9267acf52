/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.ai.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.ai.domain.bo.KnowledgeBaseBo;
import org.dromara.ai.domain.entity.AiKnowledgeBase;
import org.dromara.ai.domain.query.KnowledgeBaseQuery;
import org.dromara.ai.domain.vo.KnowledgeBaseVO;
import org.dromara.ai.mapper.AiKnowledgeBaseMapper;
import org.dromara.ai.service.IKnowledgeBaseService;
import org.dromara.ai.service.IVectorService;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;

/**
 * 知识库服务实现
 *
 * <AUTHOR> Puppy
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class KnowledgeBaseServiceImpl implements IKnowledgeBaseService {

    private final AiKnowledgeBaseMapper baseMapper;
    private final IVectorService vectorService;

    @Override
    public IPage<KnowledgeBaseVO> queryPageList(KnowledgeBaseQuery query) {
        LambdaQueryWrapper<AiKnowledgeBase> lqw = buildQueryWrapper(query);
        IPage<AiKnowledgeBase> result = baseMapper.selectPage(query.build(), lqw);
        return result.convert(entity -> {
            KnowledgeBaseVO vo = MapstructUtils.convert(entity, KnowledgeBaseVO.class);
            // 可以在这里添加额外的业务逻辑，比如统计信息
            return vo;
        });
    }

    @Override
    public List<KnowledgeBaseVO> queryList(KnowledgeBaseQuery query) {
        LambdaQueryWrapper<AiKnowledgeBase> lqw = buildQueryWrapper(query);
        List<AiKnowledgeBase> list = baseMapper.selectList(lqw);
        return MapstructUtils.convert(list, KnowledgeBaseVO.class);
    }

    @Override
    public KnowledgeBaseVO queryById(Long knowledgeId) {
        AiKnowledgeBase entity = baseMapper.selectById(knowledgeId);
        if (entity == null) {
            return null;
        }
        
        KnowledgeBaseVO vo = MapstructUtils.convert(entity, KnowledgeBaseVO.class);
        
        // 实时统计向量数量
        try {
            String tenantId = TenantHelper.getTenantId();
            long vectorCount = vectorService.countVectorsByKnowledgeBase(tenantId, knowledgeId.toString());
            vo.setVectorCount(vectorCount);
        } catch (Exception e) {
            log.warn("获取知识库向量统计失败 - 知识库ID: {}", knowledgeId, e);
        }
        
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(KnowledgeBaseBo bo) {
        // 校验知识库名称唯一性
        if (!checkKnowledgeNameUnique(bo.getKnowledgeName(), null)) {
            throw new RuntimeException("知识库名称已存在");
        }
        
        AiKnowledgeBase entity = MapstructUtils.convert(bo, AiKnowledgeBase.class);
        
        // 设置默认值
        if (StrUtil.isBlank(entity.getStatus())) {
            entity.setStatus("0"); // 默认启用
        }
        if (StrUtil.isBlank(entity.getKnowledgeType())) {
            entity.setKnowledgeType("PRIVATE"); // 默认私有
        }
        if (entity.getDocumentCount() == null) {
            entity.setDocumentCount(0);
        }
        if (entity.getVectorCount() == null) {
            entity.setVectorCount(0L);
        }
        if (entity.getSort() == null) {
            entity.setSort(0);
        }
        
        boolean result = baseMapper.insert(entity) > 0;
        if (result) {
            bo.setKnowledgeId(entity.getKnowledgeId());
            log.info("成功创建知识库 - ID: {}, 名称: {}", entity.getKnowledgeId(), entity.getKnowledgeName());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(KnowledgeBaseBo bo) {
        // 校验知识库名称唯一性
        if (!checkKnowledgeNameUnique(bo.getKnowledgeName(), bo.getKnowledgeId())) {
            throw new RuntimeException("知识库名称已存在");
        }
        
        AiKnowledgeBase entity = MapstructUtils.convert(bo, AiKnowledgeBase.class);
        boolean result = baseMapper.updateById(entity) > 0;
        
        if (result) {
            log.info("成功更新知识库 - ID: {}, 名称: {}", entity.getKnowledgeId(), entity.getKnowledgeName());
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (ObjectUtil.isEmpty(ids)) {
            return false;
        }
        
        if (isValid) {
            // 校验是否可以删除
            for (Long id : ids) {
                AiKnowledgeBase entity = baseMapper.selectById(id);
                if (entity != null && entity.getDocumentCount() != null && entity.getDocumentCount() > 0) {
                    throw new RuntimeException("知识库 [" + entity.getKnowledgeName() + "] 中还有文档，无法删除");
                }
            }
        }
        
        // 删除向量数据
        String tenantId = TenantHelper.getTenantId();
        for (Long id : ids) {
            try {
                vectorService.deleteByKnowledgeBase(tenantId, id.toString());
                log.info("删除知识库向量数据 - 知识库ID: {}", id);
            } catch (Exception e) {
                log.error("删除知识库向量数据失败 - 知识库ID: {}", id, e);
            }
        }
        
        // 删除数据库记录
        boolean result = baseMapper.deleteBatchIds(ids) > 0;
        if (result) {
            log.info("成功删除知识库 - IDs: {}", ids);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean enableKnowledgeBase(Long knowledgeId) {
        AiKnowledgeBase entity = new AiKnowledgeBase();
        entity.setKnowledgeId(knowledgeId);
        entity.setStatus("0");
        
        boolean result = baseMapper.updateById(entity) > 0;
        if (result) {
            log.info("成功启用知识库 - ID: {}", knowledgeId);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean disableKnowledgeBase(Long knowledgeId) {
        AiKnowledgeBase entity = new AiKnowledgeBase();
        entity.setKnowledgeId(knowledgeId);
        entity.setStatus("1");
        
        boolean result = baseMapper.updateById(entity) > 0;
        if (result) {
            log.info("成功停用知识库 - ID: {}", knowledgeId);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateStatistics(Long knowledgeId, Integer documentCount, Long vectorCount) {
        AiKnowledgeBase entity = new AiKnowledgeBase();
        entity.setKnowledgeId(knowledgeId);
        if (documentCount != null) {
            entity.setDocumentCount(documentCount);
        }
        if (vectorCount != null) {
            entity.setVectorCount(vectorCount);
        }
        
        boolean result = baseMapper.updateById(entity) > 0;
        if (result) {
            log.info("成功更新知识库统计信息 - ID: {}, 文档数: {}, 向量数: {}", knowledgeId, documentCount, vectorCount);
        }
        return result;
    }

    @Override
    public Boolean checkKnowledgeNameUnique(String knowledgeName, Long knowledgeId) {
        LambdaQueryWrapper<AiKnowledgeBase> lqw = Wrappers.lambdaQuery(AiKnowledgeBase.class)
            .eq(AiKnowledgeBase::getKnowledgeName, knowledgeName);
        
        if (knowledgeId != null) {
            lqw.ne(AiKnowledgeBase::getKnowledgeId, knowledgeId);
        }
        
        return baseMapper.selectCount(lqw) == 0;
    }

    @Override
    public List<KnowledgeBaseVO> getCurrentTenantKnowledgeBases() {
        LambdaQueryWrapper<AiKnowledgeBase> lqw = Wrappers.lambdaQuery(AiKnowledgeBase.class)
            .eq(AiKnowledgeBase::getStatus, "0") // 只查询启用的知识库
            .orderByAsc(AiKnowledgeBase::getSort)
            .orderByDesc(AiKnowledgeBase::getCreateTime);
        
        List<AiKnowledgeBase> list = baseMapper.selectList(lqw);
        return MapstructUtils.convert(list, KnowledgeBaseVO.class);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<AiKnowledgeBase> buildQueryWrapper(KnowledgeBaseQuery query) {
        LambdaQueryWrapper<AiKnowledgeBase> lqw = Wrappers.lambdaQuery(AiKnowledgeBase.class)
            .like(StrUtil.isNotBlank(query.getKnowledgeName()), AiKnowledgeBase::getKnowledgeName, query.getKnowledgeName())
            .eq(StrUtil.isNotBlank(query.getStatus()), AiKnowledgeBase::getStatus, query.getStatus())
            .eq(StrUtil.isNotBlank(query.getKnowledgeType()), AiKnowledgeBase::getKnowledgeType, query.getKnowledgeType())
            .and(StrUtil.isNotBlank(query.getKeyword()), wrapper -> 
                wrapper.like(AiKnowledgeBase::getKnowledgeName, query.getKeyword())
                    .or()
                    .like(AiKnowledgeBase::getDescription, query.getKeyword())
            )
            .orderByAsc(AiKnowledgeBase::getSort)
            .orderByDesc(AiKnowledgeBase::getCreateTime);
        
        return lqw;
    }

}
