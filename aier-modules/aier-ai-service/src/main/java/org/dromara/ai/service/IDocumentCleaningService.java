package org.dromara.ai.service;

import org.springframework.ai.document.Document;

import java.util.List;

/**
 * 文档清洗服务接口
 * 提供文档内容清洗、格式规范化、噪声去除等功能
 * 
 * <AUTHOR> Puppy
 */
public interface IDocumentCleaningService {

    /**
     * 清洗文档内容
     * 去除无关内容、规范化格式、统一编码
     *
     * @param content 原始文档内容
     * @param documentType 文档类型（pdf、docx、txt等）
     * @return 清洗后的内容
     */
    String cleanContent(String content, String documentType);

    /**
     * 批量清洗文档
     *
     * @param documents 文档列表
     * @param documentType 文档类型
     * @return 清洗后的文档列表
     */
    List<Document> cleanDocuments(List<Document> documents, String documentType);

    /**
     * 格式规范化
     * 统一标点符号、空白字符、编码格式
     *
     * @param content 文档内容
     * @return 规范化后的内容
     */
    String normalizeFormat(String content);

    /**
     * 去除噪声内容
     * 清理页眉页脚、水印、重复内容等
     *
     * @param content 文档内容
     * @param documentType 文档类型
     * @return 去噪后的内容
     */
    String removeNoise(String content, String documentType);

    /**
     * 评估文档内容质量
     * 基于内容长度、语义完整性、噪声比例等指标
     *
     * @param content 文档内容
     * @return 质量分数（0-100）
     */
    double evaluateContentQuality(String content);

    /**
     * 检查内容是否有效
     * 过滤掉空白、无意义的内容
     *
     * @param content 文档内容
     * @return 是否有效
     */
    boolean isValidContent(String content);
}
