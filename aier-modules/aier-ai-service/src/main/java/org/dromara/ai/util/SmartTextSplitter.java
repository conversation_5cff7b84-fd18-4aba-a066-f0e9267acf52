package org.dromara.ai.util;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 智能文本分割器
 * 实现递归字符分割、语义感知分块、动态块大小调整
 * 
 * <AUTHOR> Puppy
 */
@Slf4j
@Component
public class SmartTextSplitter {

    // 分割优先级：段落 > 句子 > 子句 > 强制分割
    private static final String[] SEPARATORS = {
        "\n\n",     // 段落分隔符
        "\n",       // 行分隔符
        "。",       // 中文句号
        "！",       // 中文感叹号
        "？",       // 中文问号
        ".",        // 英文句号
        "!",        // 英文感叹号
        "?",        // 英文问号
        "；",       // 中文分号
        ";",        // 英文分号
        "，",       // 中文逗号
        ",",        // 英文逗号
        " "         // 空格（最后的分割选择）
    };

    // 默认配置参数
    private static final int DEFAULT_CHUNK_SIZE = 600;      // 目标块大小（字符）
    private static final int MIN_CHUNK_SIZE = 100;         // 最小块大小
    private static final int MAX_CHUNK_SIZE = 1200;        // 最大块大小
    private static final double OVERLAP_RATIO = 0.15;      // 重叠比例（15%）
    private static final int MIN_OVERLAP_SIZE = 20;        // 最小重叠大小

    private final int targetChunkSize;
    private final int minChunkSize;
    private final int maxChunkSize;
    private final int overlapSize;

    /**
     * 默认构造函数，使用默认配置
     */
    public SmartTextSplitter() {
        this(DEFAULT_CHUNK_SIZE, MIN_CHUNK_SIZE, MAX_CHUNK_SIZE, OVERLAP_RATIO);
    }

    /**
     * 自定义配置构造函数
     *
     * @param targetChunkSize 目标块大小
     * @param minChunkSize 最小块大小
     * @param maxChunkSize 最大块大小
     * @param overlapRatio 重叠比例
     */
    public SmartTextSplitter(int targetChunkSize, int minChunkSize, int maxChunkSize, double overlapRatio) {
        this.targetChunkSize = targetChunkSize;
        this.minChunkSize = minChunkSize;
        this.maxChunkSize = maxChunkSize;
        this.overlapSize = Math.max(MIN_OVERLAP_SIZE, (int) (targetChunkSize * overlapRatio));
    }

    /**
     * 分割文档列表
     *
     * @param documents 原始文档列表
     * @param documentType 文档类型
     * @return 分割后的文档列表
     */
    public List<Document> splitDocuments(List<Document> documents, String documentType) {
        if (documents == null || documents.isEmpty()) {
            return new ArrayList<>();
        }

        List<Document> allChunks = new ArrayList<>();
        
        for (int i = 0; i < documents.size(); i++) {
            Document document = documents.get(i);
            List<Document> chunks = splitDocument(document, documentType, i);
            allChunks.addAll(chunks);
        }

        log.info("智能分割完成 - 原始文档数: {}, 分割后块数: {}", documents.size(), allChunks.size());
        return allChunks;
    }

    /**
     * 分割单个文档
     *
     * @param document 原始文档
     * @param documentType 文档类型
     * @param documentIndex 文档索引
     * @return 分割后的文档块列表
     */
    public List<Document> splitDocument(Document document, String documentType, int documentIndex) {
        String content = document.getText();
        if (StrUtil.isBlank(content)) {
            return new ArrayList<>();
        }

        log.debug("开始智能分割文档 - 类型: {}, 长度: {}", documentType, content.length());

        // 根据文档类型选择分割策略
        List<String> textChunks = splitByDocumentType(content, documentType);
        
        // 应用重叠策略
        textChunks = applyOverlapStrategy(textChunks);
        
        // 转换为Document对象
        List<Document> documentChunks = convertToDocuments(textChunks, document, documentIndex);

        log.debug("文档分割完成 - 原始长度: {}, 块数: {}", content.length(), documentChunks.size());
        return documentChunks;
    }

    /**
     * 根据文档类型选择分割策略
     */
    private List<String> splitByDocumentType(String content, String documentType) {
        switch (documentType.toLowerCase()) {
            case "pdf":
                return splitPdfContent(content);
            case "docx":
            case "doc":
                return splitWordContent(content);
            case "md":
            case "markdown":
                return splitMarkdownContent(content);
            case "txt":
            default:
                return splitTextContent(content);
        }
    }

    /**
     * PDF内容分割策略
     * 优先按段落分割，考虑页面结构
     */
    private List<String> splitPdfContent(String content) {
        // PDF通常有明确的段落结构
        return recursiveSplit(content, Arrays.asList("\n\n", "\n", "。", ".", "！", "!", "？", "?"));
    }

    /**
     * Word内容分割策略
     * 优先按段落和标题分割
     */
    private List<String> splitWordContent(String content) {
        // Word文档可能有标题结构
        List<String> separators = new ArrayList<>();
        separators.add("\n\n");  // 段落
        separators.addAll(Arrays.asList(SEPARATORS).subList(1, SEPARATORS.length));
        return recursiveSplit(content, separators);
    }

    /**
     * Markdown内容分割策略
     * 优先按标题和段落分割
     */
    private List<String> splitMarkdownContent(String content) {
        List<String> separators = new ArrayList<>();
        // Markdown标题分隔符
        separators.add("\n## ");
        separators.add("\n### ");
        separators.add("\n#### ");
        separators.add("\n\n");
        separators.addAll(Arrays.asList(SEPARATORS).subList(1, SEPARATORS.length));
        return recursiveSplit(content, separators);
    }

    /**
     * 纯文本内容分割策略
     * 使用标准的递归分割
     */
    private List<String> splitTextContent(String content) {
        return recursiveSplit(content, Arrays.asList(SEPARATORS));
    }

    /**
     * 递归分割算法
     * 按照分隔符优先级递归分割文本
     */
    private List<String> recursiveSplit(String text, List<String> separators) {
        if (StrUtil.isBlank(text) || text.length() <= targetChunkSize) {
            return text.length() >= minChunkSize ? List.of(text) : new ArrayList<>();
        }

        // 尝试使用当前分隔符分割
        for (String separator : separators) {
            if (text.contains(separator)) {
                List<String> chunks = splitBySeparator(text, separator, separators);
                if (!chunks.isEmpty()) {
                    return chunks;
                }
            }
        }

        // 如果所有分隔符都无法有效分割，进行强制分割
        return forceSplit(text);
    }

    /**
     * 按指定分隔符分割
     */
    private List<String> splitBySeparator(String text, String separator, List<String> allSeparators) {
        String[] parts = text.split(Pattern.quote(separator), -1);
        List<String> chunks = new ArrayList<>();
        StringBuilder currentChunk = new StringBuilder();

        for (int i = 0; i < parts.length; i++) {
            String part = parts[i];
            
            // 如果当前部分加上分隔符后仍在目标大小内
            String potentialChunk = currentChunk.length() == 0 ? part : 
                                   currentChunk + separator + part;
            
            if (potentialChunk.length() <= maxChunkSize) {
                if (currentChunk.length() > 0) {
                    currentChunk.append(separator);
                }
                currentChunk.append(part);
            } else {
                // 保存当前块
                if (currentChunk.length() >= minChunkSize) {
                    chunks.add(currentChunk.toString());
                }
                
                // 如果单个部分太大，需要进一步分割
                if (part.length() > maxChunkSize) {
                    List<String> subChunks = recursiveSplit(part, 
                        allSeparators.subList(allSeparators.indexOf(separator) + 1, allSeparators.size()));
                    chunks.addAll(subChunks);
                    currentChunk = new StringBuilder();
                } else {
                    currentChunk = new StringBuilder(part);
                }
            }
        }

        // 添加最后一个块
        if (currentChunk.length() >= minChunkSize) {
            chunks.add(currentChunk.toString());
        }

        return chunks;
    }

    /**
     * 强制分割（当无法按分隔符分割时）
     */
    private List<String> forceSplit(String text) {
        List<String> chunks = new ArrayList<>();
        
        for (int i = 0; i < text.length(); i += targetChunkSize) {
            int end = Math.min(i + targetChunkSize, text.length());
            String chunk = text.substring(i, end);
            
            if (chunk.length() >= minChunkSize) {
                chunks.add(chunk);
            }
        }
        
        return chunks;
    }

    /**
     * 应用重叠策略
     * 在相邻块之间添加重叠内容
     */
    private List<String> applyOverlapStrategy(List<String> chunks) {
        if (chunks.size() <= 1) {
            return chunks;
        }

        List<String> overlappedChunks = new ArrayList<>();
        
        for (int i = 0; i < chunks.size(); i++) {
            String currentChunk = chunks.get(i);
            
            // 添加前一个块的尾部重叠
            if (i > 0) {
                String previousChunk = chunks.get(i - 1);
                String overlap = getOverlapText(previousChunk, true);
                if (StrUtil.isNotBlank(overlap)) {
                    currentChunk = overlap + "\n" + currentChunk;
                }
            }
            
            overlappedChunks.add(currentChunk);
        }
        
        return overlappedChunks;
    }

    /**
     * 获取重叠文本
     */
    private String getOverlapText(String text, boolean fromEnd) {
        if (text.length() <= overlapSize) {
            return text;
        }
        
        if (fromEnd) {
            return text.substring(text.length() - overlapSize);
        } else {
            return text.substring(0, overlapSize);
        }
    }

    /**
     * 转换为Document对象
     */
    private List<Document> convertToDocuments(List<String> textChunks, Document originalDocument, int documentIndex) {
        List<Document> documents = new ArrayList<>();
        
        for (int i = 0; i < textChunks.size(); i++) {
            String chunkText = textChunks.get(i);
            
            // 复制原始元数据
            Map<String, Object> metadata = new HashMap<>(originalDocument.getMetadata());
            
            // 添加分块相关的元数据
            metadata.put("chunk_index", i);
            metadata.put("total_chunks", textChunks.size());
            metadata.put("document_index", documentIndex);
            metadata.put("chunk_size", chunkText.length());
            metadata.put("splitter_type", "smart");
            metadata.put("target_chunk_size", targetChunkSize);
            metadata.put("overlap_size", overlapSize);
            
            Document chunkDocument = new Document(chunkText, metadata);
            documents.add(chunkDocument);
        }
        
        return documents;
    }
}
