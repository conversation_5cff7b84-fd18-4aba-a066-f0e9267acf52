/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.ai.config;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.milvus.MilvusVectorStore;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Configuration
public class VectorDataInit implements ApplicationRunner {

    private final Logger logger = LoggerFactory.getLogger(VectorDataInit.class);

    private final MilvusVectorStore vectorStore;

    public VectorDataInit(MilvusVectorStore vectorStore) {
        this.vectorStore = vectorStore;
    }

    @Override
    public void run(ApplicationArguments args) {
        // 创建多租户测试数据用于验证租户隔离
//        createMultiTenantTestData();
    }

    /**
     * 创建多租户测试数据
     */
    private void createMultiTenantTestData() {
        List<Document> allDocuments = new ArrayList<>();

        // 租户194338的知识库1数据
        Map<String, Object> tenant194338Knowledge1 = Map.of(
                "tenant_id", "194338",
                "knowledge_id", "1"
        );

        allDocuments.addAll(List.of(
                new Document("【租户194338-知识库1】使用SpringAIAlibaba创建一个Spring Boot项目，并添加spring-ai-alibaba-starter-dashscope依赖。", tenant194338Knowledge1),
                new Document("【租户194338-知识库1】在SpringAIAlibaba项目的pom.xml中添加Spring Milestone和Snapshot存储库。", tenant194338Knowledge1),
                new Document("【租户194338-知识库1】通过SpringAIAlibaba申请阿里云通义API Key，在application.yml中进行配置。", tenant194338Knowledge1),
                new Document("【租户194338-知识库1】使用SpringAIAlibaba的ChatClient和Prompt功能实现对话模型。", tenant194338Knowledge1),
                new Document("【租户194338-知识库1】SpringAIAlibaba支持文本生成、翻译、摘要等生成式AI功能。", tenant194338Knowledge1)
        ));

        // 租户194338的知识库2数据
        Map<String, Object> tenant194338Knowledge2 = Map.of(
                "tenant_id", "194338",
                "knowledge_id", "2"
        );

        allDocuments.addAll(List.of(
                new Document("【租户194338-知识库2】产品管理系统使用指南：如何创建新产品。", tenant194338Knowledge2),
                new Document("【租户194338-知识库2】订单处理流程：从下单到发货的完整流程。", tenant194338Knowledge2),
                new Document("【租户194338-知识库2】客户服务标准：如何提供优质的客户服务。", tenant194338Knowledge2)
        ));

        // 其他租户999999的数据（用于验证隔离）
        Map<String, Object> tenant999999Knowledge1 = Map.of(
                "tenant_id", "999999",
                "knowledge_id", "1"
        );

        allDocuments.addAll(List.of(
                new Document("【租户999999-知识库1】这是其他租户的数据，不应该被租户194338查询到。", tenant999999Knowledge1),
                new Document("【租户999999-知识库1】其他租户的SpringAI配置信息，应该被过滤掉。", tenant999999Knowledge1),
                new Document("【租户999999-知识库1】其他租户的敏感业务数据，必须确保隔离。", tenant999999Knowledge1)
        ));

        // 另一个租户888888的数据
        Map<String, Object> tenant888888Knowledge1 = Map.of(
                "tenant_id", "888888",
                "knowledge_id", "1"
        );

        allDocuments.addAll(List.of(
                new Document("【租户888888-知识库1】第三方租户的数据，也不应该被其他租户访问。", tenant888888Knowledge1),
                new Document("【租户888888-知识库1】租户隔离测试数据，用于验证过滤效果。", tenant888888Knowledge1)
        ));

        // 批量添加所有文档
        vectorStore.add(allDocuments);

        logger.info("多租户测试数据初始化完成:");
        logger.info("- 租户194338知识库1: 5条数据");
        logger.info("- 租户194338知识库2: 3条数据");
        logger.info("- 租户999999知识库1: 3条数据");
        logger.info("- 租户888888知识库1: 2条数据");
        logger.info("总计: {}条数据", allDocuments.size());
    }

}
