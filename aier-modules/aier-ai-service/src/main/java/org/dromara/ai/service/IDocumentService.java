/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.ai.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import org.dromara.ai.domain.bo.DocumentBo;
import org.dromara.ai.domain.query.DocumentQuery;
import org.dromara.ai.domain.vo.DocumentVO;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;

/**
 * 文档服务接口
 *
 * <AUTHOR> <PERSON>uppy
 */
public interface IDocumentService {

    /**
     * 查询文档分页列表
     *
     * @param query 查询条件
     * @return 文档分页列表
     */
    IPage<DocumentVO> queryPageList(DocumentQuery query);

    /**
     * 查询文档列表
     *
     * @param query 查询条件
     * @return 文档列表
     */
    List<DocumentVO> queryList(DocumentQuery query);

    /**
     * 根据文档ID查询文档详情
     *
     * @param documentId 文档ID
     * @return 文档详情
     */
    DocumentVO queryById(Long documentId);

    /**
     * 新增文档
     *
     * @param bo 文档业务对象
     * @return 新增结果
     */
    Boolean insertByBo(DocumentBo bo);

    /**
     * 修改文档
     *
     * @param bo 文档业务对象
     * @return 修改结果
     */
    Boolean updateByBo(DocumentBo bo);

    /**
     * 校验并批量删除文档信息
     *
     * @param ids     文档ID集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return 删除结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 上传文档
     *
     * @param knowledgeId 知识库ID
     * @param file        上传的文件
     * @return 上传结果
     */
    DocumentVO uploadDocument(Long knowledgeId, MultipartFile file);

    /**
     * 批量上传文档
     *
     * @param knowledgeId 知识库ID
     * @param files       上传的文件列表
     * @return 上传结果列表
     */
    List<DocumentVO> batchUploadDocuments(Long knowledgeId, List<MultipartFile> files);

    /**
     * 重新处理文档
     *
     * @param documentId 文档ID
     * @return 处理结果
     */
    Boolean reprocessDocument(Long documentId);

    /**
     * 更新文档处理状态
     *
     * @param documentId    文档ID
     * @param processStatus 处理状态
     * @param errorMessage  错误信息
     * @return 更新结果
     */
    Boolean updateProcessStatus(Long documentId, String processStatus, String errorMessage);

    /**
     * 更新文档向量化状态
     *
     * @param documentId   文档ID
     * @param vectorStatus 向量化状态
     * @param chunkCount   分块数量
     * @param vectorCount  向量数量
     * @return 更新结果
     */
    Boolean updateVectorStatus(Long documentId, String vectorStatus, Integer chunkCount, Integer vectorCount);

    /**
     * 启用文档
     *
     * @param documentId 文档ID
     * @return 操作结果
     */
    Boolean enableDocument(Long documentId);

    /**
     * 停用文档
     *
     * @param documentId 文档ID
     * @return 操作结果
     */
    Boolean disableDocument(Long documentId);

    /**
     * 获取文档内容预览
     *
     * @param documentId 文档ID
     * @param maxLength  最大长度
     * @return 文档内容预览
     */
    String getDocumentPreview(Long documentId, Integer maxLength);

    /**
     * 根据知识库ID查询文档列表
     *
     * @param knowledgeId 知识库ID
     * @return 文档列表
     */
    List<DocumentVO> queryByKnowledgeId(Long knowledgeId);

    /**
     * 统计知识库中的文档数量
     *
     * @param knowledgeId 知识库ID
     * @return 文档数量
     */
    Integer countByKnowledgeId(Long knowledgeId);

}
