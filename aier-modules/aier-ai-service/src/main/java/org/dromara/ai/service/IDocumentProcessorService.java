/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.ai.service;

import org.springframework.ai.document.Document;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * 文档处理服务接口
 * 定义文档解析、处理和向量化的核心操作
 *
 * <AUTHOR> Puppy
 */
public interface IDocumentProcessorService {

    /**
     * 处理上传的文件
     *
     * @param file        上传的文件
     * @param tenantId    租户ID
     * @param knowledgeId 知识库ID
     * @param documentId  文档ID
     * @return 处理后的文档列表
     * @throws Exception 处理异常
     */
    List<Document> processUploadedFile(MultipartFile file, String tenantId, String knowledgeId, String documentId) throws Exception;

    /**
     * 处理文档流
     *
     * @param inputStream 文档输入流
     * @param fileName    文件名
     * @param metadata    元数据
     * @return 处理后的文档列表
     * @throws Exception 处理异常
     */
    List<Document> processDocument(InputStream inputStream, String fileName, Map<String, Object> metadata) throws Exception;

    /**
     * 检查文件类型是否支持
     *
     * @param fileName 文件名
     * @return 是否支持
     */
    boolean isFileTypeSupported(String fileName);

    /**
     * 获取文件类型
     *
     * @param fileName 文件名
     * @return 文件类型
     */
    String getFileType(String fileName);

    /**
     * 获取支持的文件类型列表
     *
     * @return 支持的文件类型列表
     */
    List<String> getSupportedFileTypes();

    /**
     * 异步处理文档并向量化
     *
     * @param documentId  文档ID
     * @param tenantId    租户ID
     * @param knowledgeId 知识库ID
     * @param filePath    文件路径
     */
    void processDocumentAsync(Long documentId, String tenantId, String knowledgeId, String filePath);

    /**
     * 重新处理文档
     *
     * @param documentId  文档ID
     * @param tenantId    租户ID
     * @param knowledgeId 知识库ID
     * @param filePath    文件路径
     * @throws Exception 处理异常
     */
    void reprocessDocument(Long documentId, String tenantId, String knowledgeId, String filePath) throws Exception;

}
