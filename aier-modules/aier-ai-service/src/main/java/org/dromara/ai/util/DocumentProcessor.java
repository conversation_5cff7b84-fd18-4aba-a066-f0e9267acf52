/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.ai.util;

import org.springframework.ai.document.Document;

import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * 文档处理器接口
 * 定义文档解析和处理的统一规范，支持智能化文档处理流程
 *
 * <AUTHOR> Puppy
 */
public interface DocumentProcessor {

    /**
     * 检查是否支持指定的文件类型
     *
     * @param fileType 文件类型（如：pdf、docx、txt等）
     * @return 是否支持
     */
    boolean supports(String fileType);

    /**
     * 处理文档，将文档内容解析为Spring AI Document对象列表
     * 包含完整的智能化处理流程：清洗、分割、元数据增强
     *
     * @param inputStream 文档输入流
     * @param metadata    文档元数据
     * @return Spring AI Document对象列表
     * @throws Exception 处理异常
     */
    List<Document> process(InputStream inputStream, Map<String, Object> metadata) throws Exception;

    /**
     * 预处理文档内容
     * 进行文档清洗、格式规范化等预处理操作
     *
     * @param inputStream 文档输入流
     * @param metadata    文档元数据
     * @return 预处理后的文档列表
     * @throws Exception 处理异常
     */
    default List<Document> preprocess(InputStream inputStream, Map<String, Object> metadata) throws Exception {
        // 默认实现：直接调用原有的process方法
        return process(inputStream, metadata);
    }

    /**
     * 智能分割文档
     * 使用智能分割策略将文档分割为合适的块
     *
     * @param documents 原始文档列表
     * @return 分割后的文档块列表
     */
    default List<Document> smartSplit(List<Document> documents) {
        // 默认实现：返回原文档（子类可以重写）
        return documents;
    }

    /**
     * 增强文档元数据
     * 为文档添加丰富的元数据信息
     *
     * @param documents 文档列表
     * @param additionalMetadata 额外的元数据
     * @return 元数据增强后的文档列表
     */
    default List<Document> enhanceMetadata(List<Document> documents, Map<String, Object> additionalMetadata) {
        // 默认实现：返回原文档（子类可以重写）
        return documents;
    }

    /**
     * 验证文档质量
     * 检查文档内容的质量，过滤低质量内容
     *
     * @param documents 文档列表
     * @return 质量验证后的文档列表
     */
    default List<Document> validateQuality(List<Document> documents) {
        // 默认实现：返回原文档（子类可以重写）
        return documents;
    }

    /**
     * 获取支持的文件类型列表
     *
     * @return 支持的文件类型列表
     */
    List<String> getSupportedFileTypes();

    /**
     * 获取处理器名称
     *
     * @return 处理器名称
     */
    String getProcessorName();

    /**
     * 获取处理器版本
     *
     * @return 处理器版本
     */
    default String getProcessorVersion() {
        return "2.0.0-smart";
    }

    /**
     * 检查是否支持智能处理
     *
     * @return 是否支持智能处理
     */
    default boolean supportsSmartProcessing() {
        return true;
    }

    /**
     * 获取处理器配置信息
     *
     * @return 配置信息描述
     */
    default String getConfigurationInfo() {
        return String.format("Processor[name=%s, version=%s, smart=%s]",
                           getProcessorName(), getProcessorVersion(), supportsSmartProcessing());
    }

}
