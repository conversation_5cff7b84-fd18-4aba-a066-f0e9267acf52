/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.ai.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.ai.config.DocumentProcessingConfig;
import org.dromara.ai.service.IDocumentProcessorService;
import org.dromara.ai.service.IVectorService;
import org.dromara.ai.util.DocumentProcessor;
import org.dromara.common.oss.core.OssClient;
import org.dromara.common.oss.factory.OssFactory;
import org.springframework.ai.document.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.FileInputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 文档处理服务实现
 * 集成智能化文档处理流程，提供完整的处理监控和配置管理
 *
 * <AUTHOR> Puppy
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DocumentProcessorServiceImpl implements IDocumentProcessorService {

    private final List<DocumentProcessor> documentProcessors;
    private final IVectorService vectorService;

    @Autowired
    private DocumentProcessingConfig processingConfig;

    @Override
    public List<Document> processUploadedFile(MultipartFile file, String tenantId, String knowledgeId, String documentId) throws Exception {
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("上传文件不能为空");
        }

        String fileName = file.getOriginalFilename();
        if (StrUtil.isBlank(fileName)) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        log.info("开始智能化处理上传文件 - 文件名: {}, 租户ID: {}, 知识库ID: {}, 文档ID: {}, 配置: {}",
                fileName, tenantId, knowledgeId, documentId, processingConfig.getConfigSummary());

        // 构建增强元数据
        Map<String, Object> metadata = buildEnhancedMetadata(tenantId, knowledgeId, documentId, fileName, file);

        // 记录处理开始时间
        long startTime = System.currentTimeMillis();

        // 处理文档
        try (InputStream inputStream = file.getInputStream()) {
            List<Document> documents = processDocument(inputStream, fileName, metadata);

            // 记录处理统计信息
            long processingTime = System.currentTimeMillis() - startTime;
            logProcessingStatistics(fileName, documents, processingTime);

            return documents;
        }
    }

    @Override
    public List<Document> processDocument(InputStream inputStream, String fileName, Map<String, Object> metadata) throws Exception {
        String fileType = getFileType(fileName);

        if (!isFileTypeSupported(fileName)) {
            throw new UnsupportedOperationException("不支持的文件类型: " + fileType);
        }

        log.info("开始智能化处理文档 - 文件名: {}, 文件类型: {}", fileName, fileType);

        // 查找合适的文档处理器
        DocumentProcessor processor = findProcessor(fileType);
        if (processor == null) {
            throw new UnsupportedOperationException("未找到适合的文档处理器: " + fileType);
        }

        // 记录处理器信息
        log.debug("使用处理器: {}", processor.getConfigurationInfo());

        // 处理文档
        List<Document> documents = processor.process(inputStream, metadata);

        // 验证处理结果
        validateProcessingResults(documents, fileName, fileType);

        log.info("智能化文档处理完成 - 文件名: {}, 生成文档块数: {}", fileName, documents.size());
        return documents;
    }

    @Override
    public boolean isFileTypeSupported(String fileName) {
        if (StrUtil.isBlank(fileName)) {
            return false;
        }
        
        String fileType = getFileType(fileName);
        return documentProcessors.stream()
            .anyMatch(processor -> processor.supports(fileType));
    }

    @Override
    public String getFileType(String fileName) {
        if (StrUtil.isBlank(fileName)) {
            return "";
        }
        
        String extension = FileUtil.extName(fileName);
        return StrUtil.isBlank(extension) ? "" : extension.toLowerCase();
    }

    @Override
    public List<String> getSupportedFileTypes() {
        return documentProcessors.stream()
            .flatMap(processor -> processor.getSupportedFileTypes().stream())
            .distinct()
            .collect(Collectors.toList());
    }

    @Override
    @Async
    public void processDocumentAsync(Long documentId, String tenantId, String knowledgeId, String filePath) {
        log.info("开始异步处理文档 - 文档ID: {}, 租户ID: {}, 知识库ID: {}, 文件路径: {}", documentId, tenantId, knowledgeId, filePath);

        try {
            // 构建元数据
            Map<String, Object> metadata = new HashMap<>();
            metadata.put("tenant_id", tenantId);
            metadata.put("knowledge_id", knowledgeId);
            metadata.put("document_id", documentId.toString());

            String fileName = FileUtil.getName(filePath);
            metadata.put("file_name", fileName);

            // 获取OSS客户端并处理文档
            OssClient ossClient = OssFactory.instance();
            try (InputStream inputStream = ossClient.getObjectContent(filePath)) {
                List<Document> documents = processDocument(inputStream, fileName, metadata);

                // 添加到向量存储
                if (!documents.isEmpty()) {
                    vectorService.addDocuments(documents);
                    log.info("异步文档处理完成 - 文档ID: {}, 向量数量: {}", documentId, documents.size());
                } else {
                    log.warn("异步文档处理完成但未生成向量 - 文档ID: {}", documentId);
                }
            }
            
        } catch (Exception e) {
            log.error("异步文档处理失败 - 文档ID: {}", documentId, e);
            // 这里可以更新文档状态为失败
            // documentService.updateProcessStatus(documentId, "FAILED", e.getMessage());
        }
    }

    @Override
    public void reprocessDocument(Long documentId, String tenantId, String knowledgeId, String filePath) throws Exception {
        log.info("开始重新处理文档 - 文档ID: {}, 租户ID: {}, 知识库ID: {}, 文件路径: {}", documentId, tenantId, knowledgeId, filePath);
        
        try {
            // 先删除现有的向量数据
            vectorService.deleteByDocument(tenantId, knowledgeId, documentId.toString());
            
            // 重新处理文档
            processDocumentAsync(documentId, tenantId, knowledgeId, filePath);
            
            log.info("文档重新处理启动完成 - 文档ID: {}", documentId);
            
        } catch (Exception e) {
            log.error("重新处理文档失败 - 文档ID: {}", documentId, e);
            throw new RuntimeException("重新处理文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 查找合适的文档处理器
     */
    private DocumentProcessor findProcessor(String fileType) {
        return documentProcessors.stream()
            .filter(processor -> processor.supports(fileType))
            .findFirst()
            .orElse(null);
    }

    /**
     * 构建增强元数据
     */
    private Map<String, Object> buildEnhancedMetadata(String tenantId, String knowledgeId, String documentId,
                                                     String fileName, MultipartFile file) {
        Map<String, Object> metadata = new HashMap<>();

        // 基础元数据
        metadata.put("tenant_id", tenantId);
        metadata.put("knowledge_id", knowledgeId);
        metadata.put("document_id", documentId);
        metadata.put("source", fileName);
        metadata.put("file_name", fileName);
        metadata.put("file_size", file.getSize());
        metadata.put("content_type", file.getContentType());

        // 处理相关元数据
        metadata.put("processing_timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        metadata.put("processor_version", "2.0.0-smart");
        metadata.put("processing_config", processingConfig.getConfigSummary());

        // 文件类型相关元数据
        String fileType = getFileType(fileName);
        metadata.put("file_type", fileType);
        metadata.put("chunk_size", processingConfig.getChunkSizeForType(fileType));
        metadata.put("overlap_ratio", processingConfig.getOverlapRatioForType(fileType));

        return metadata;
    }

    /**
     * 记录处理统计信息
     */
    private void logProcessingStatistics(String fileName, List<Document> documents, long processingTime) {
        if (documents.isEmpty()) {
            log.warn("文档处理完成但未生成任何文档块 - 文件名: {}, 处理时间: {}ms", fileName, processingTime);
            return;
        }

        // 统计信息
        int totalChunks = documents.size();
        int totalCharacters = documents.stream().mapToInt(doc -> doc.getText().length()).sum();
        double avgChunkSize = (double) totalCharacters / totalChunks;

        // 质量统计
        long highQualityChunks = documents.stream()
            .filter(doc -> {
                Object qualityScore = doc.getMetadata().get("overall_quality_score");
                return qualityScore instanceof Double && (Double) qualityScore >= 70.0;
            })
            .count();

        double qualityRatio = (double) highQualityChunks / totalChunks * 100;

        log.info("文档处理统计 - 文件名: {}, 处理时间: {}ms, 文档块数: {}, 总字符数: {}, 平均块大小: {:.1f}, 高质量块比例: {:.1f}%",
                fileName, processingTime, totalChunks, totalCharacters, avgChunkSize, qualityRatio);
    }

    /**
     * 验证处理结果
     */
    private void validateProcessingResults(List<Document> documents, String fileName, String fileType) {
        if (documents == null) {
            throw new RuntimeException("文档处理返回null结果: " + fileName);
        }

        if (documents.isEmpty()) {
            log.warn("文档处理未生成任何文档块 - 文件名: {}, 文件类型: {}", fileName, fileType);
            return;
        }

        // 验证文档块质量
        int validChunks = 0;
        int emptyChunks = 0;
        int lowQualityChunks = 0;

        for (Document document : documents) {
            String content = document.getText();
            if (StrUtil.isBlank(content)) {
                emptyChunks++;
                continue;
            }

            Object qualityScore = document.getMetadata().get("overall_quality_score");
            if (qualityScore instanceof Double) {
                if ((Double) qualityScore < 30.0) {
                    lowQualityChunks++;
                } else {
                    validChunks++;
                }
            } else {
                validChunks++; // 没有质量分数的默认为有效
            }
        }

        // 记录验证结果
        if (emptyChunks > 0 || lowQualityChunks > 0) {
            log.warn("文档处理质量检查 - 文件名: {}, 总块数: {}, 有效块: {}, 空块: {}, 低质量块: {}",
                    fileName, documents.size(), validChunks, emptyChunks, lowQualityChunks);
        } else {
            log.debug("文档处理质量检查通过 - 文件名: {}, 有效块数: {}", fileName, validChunks);
        }

        // 如果所有块都无效，抛出异常
        if (validChunks == 0) {
            throw new RuntimeException("文档处理失败：所有生成的文档块都无效 - " + fileName);
        }
    }

}
