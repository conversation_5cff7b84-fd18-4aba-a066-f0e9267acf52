package org.dromara.ai.util;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 文档元数据增强器
 * 提供结构元数据提取、语义元数据生成、质量评估等功能
 * 
 * <AUTHOR> Puppy
 */
@Slf4j
@Component
public class DocumentMetadataEnhancer {

    // 中文关键词提取模式
    private static final Pattern CHINESE_KEYWORD_PATTERN = Pattern.compile("[\\u4e00-\\u9fa5]{2,}");
    
    // 英文关键词提取模式
    private static final Pattern ENGLISH_KEYWORD_PATTERN = Pattern.compile("\\b[a-zA-Z]{3,}\\b");
    
    // 数字模式
    private static final Pattern NUMBER_PATTERN = Pattern.compile("\\d+");
    
    // 标题模式（Markdown风格）
    private static final Pattern TITLE_PATTERN = Pattern.compile("^#{1,6}\\s+(.+)$", Pattern.MULTILINE);
    
    // 列表模式
    private static final Pattern LIST_PATTERN = Pattern.compile("^[\\s]*[\\-\\*\\+]\\s+(.+)$", Pattern.MULTILINE);
    
    // 常见停用词
    private static final Set<String> STOP_WORDS = Set.of(
        "的", "了", "在", "是", "我", "有", "和", "就", "不", "人", "都", "一", "一个", "上", "也", "很", "到", "说", "要", "去", "你", "会", "着", "没有", "看", "好", "自己", "这",
        "the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by", "from", "up", "about", "into", "through", "during", "before", "after", "above", "below", "between", "among", "within", "without", "under", "over"
    );

    /**
     * 增强文档元数据
     *
     * @param document 原始文档
     * @param documentType 文档类型
     * @param additionalMetadata 额外的元数据
     * @return 增强后的文档
     */
    public Document enhanceMetadata(Document document, String documentType, Map<String, Object> additionalMetadata) {
        if (document == null || StrUtil.isBlank(document.getText())) {
            return document;
        }

        String content = document.getText();
        Map<String, Object> enhancedMetadata = new HashMap<>(document.getMetadata());

        // 添加额外元数据
        if (additionalMetadata != null) {
            enhancedMetadata.putAll(additionalMetadata);
        }

        // 1. 基础结构元数据
        enhancedMetadata.putAll(extractStructuralMetadata(content, documentType));

        // 2. 语义元数据
        enhancedMetadata.putAll(extractSemanticMetadata(content));

        // 3. 质量元数据
        enhancedMetadata.putAll(extractQualityMetadata(content));

        // 4. 处理时间戳
        enhancedMetadata.put("processing_timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        enhancedMetadata.put("document_type", documentType);

        log.debug("文档元数据增强完成，元数据项数: {}", enhancedMetadata.size());
        return new Document(content, enhancedMetadata);
    }

    /**
     * 批量增强文档元数据
     */
    public List<Document> enhanceDocumentsMetadata(List<Document> documents, String documentType, Map<String, Object> baseMetadata) {
        if (documents == null || documents.isEmpty()) {
            return new ArrayList<>();
        }

        List<Document> enhancedDocuments = new ArrayList<>();
        
        for (int i = 0; i < documents.size(); i++) {
            Document document = documents.get(i);
            
            // 为每个文档添加位置信息
            Map<String, Object> documentMetadata = new HashMap<>(baseMetadata != null ? baseMetadata : new HashMap<>());
            documentMetadata.put("document_sequence", i);
            documentMetadata.put("total_documents", documents.size());
            
            Document enhancedDocument = enhanceMetadata(document, documentType, documentMetadata);
            enhancedDocuments.add(enhancedDocument);
        }

        log.info("批量元数据增强完成，处理文档数: {}", documents.size());
        return enhancedDocuments;
    }

    /**
     * 提取结构元数据
     * 包括文档长度、段落数、句子数等结构信息
     */
    private Map<String, Object> extractStructuralMetadata(String content, String documentType) {
        Map<String, Object> metadata = new HashMap<>();

        // 基础长度信息
        metadata.put("content_length", content.length());
        metadata.put("word_count", countWords(content));
        metadata.put("character_count", content.length());

        // 段落和句子统计
        metadata.put("paragraph_count", countParagraphs(content));
        metadata.put("sentence_count", countSentences(content));
        metadata.put("line_count", content.split("\n").length);

        // 根据文档类型提取特定结构信息
        switch (documentType.toLowerCase()) {
            case "md":
            case "markdown":
                metadata.putAll(extractMarkdownStructure(content));
                break;
            case "pdf":
                metadata.putAll(extractPdfStructure(content));
                break;
            case "docx":
            case "doc":
                metadata.putAll(extractWordStructure(content));
                break;
        }

        return metadata;
    }

    /**
     * 提取语义元数据
     * 包括关键词、主题、摘要等语义信息
     */
    private Map<String, Object> extractSemanticMetadata(String content) {
        Map<String, Object> metadata = new HashMap<>();

        // 关键词提取
        List<String> keywords = extractKeywords(content);
        metadata.put("keywords", keywords);
        metadata.put("keyword_count", keywords.size());

        // 生成简单摘要（取前100字符）
        String summary = generateSummary(content);
        metadata.put("summary", summary);

        // 语言检测
        String language = detectLanguage(content);
        metadata.put("language", language);

        // 内容类型分析
        String contentType = analyzeContentType(content);
        metadata.put("content_type", contentType);

        return metadata;
    }

    /**
     * 提取质量元数据
     * 包括可读性、完整性、信息密度等质量指标
     */
    private Map<String, Object> extractQualityMetadata(String content) {
        Map<String, Object> metadata = new HashMap<>();

        // 可读性评分
        double readabilityScore = calculateReadabilityScore(content);
        metadata.put("readability_score", readabilityScore);

        // 信息密度
        double informationDensity = calculateInformationDensity(content);
        metadata.put("information_density", informationDensity);

        // 完整性评分
        double completenessScore = calculateCompletenessScore(content);
        metadata.put("completeness_score", completenessScore);

        // 综合质量分数
        double overallQuality = (readabilityScore + informationDensity + completenessScore) / 3.0;
        metadata.put("overall_quality_score", overallQuality);

        return metadata;
    }

    /**
     * 提取Markdown结构信息
     */
    private Map<String, Object> extractMarkdownStructure(String content) {
        Map<String, Object> metadata = new HashMap<>();

        // 标题统计
        Matcher titleMatcher = TITLE_PATTERN.matcher(content);
        List<String> titles = new ArrayList<>();
        while (titleMatcher.find()) {
            titles.add(titleMatcher.group(1));
        }
        metadata.put("title_count", titles.size());
        metadata.put("titles", titles);

        // 列表统计
        Matcher listMatcher = LIST_PATTERN.matcher(content);
        int listItemCount = 0;
        while (listMatcher.find()) {
            listItemCount++;
        }
        metadata.put("list_item_count", listItemCount);

        return metadata;
    }

    /**
     * 提取PDF结构信息
     */
    private Map<String, Object> extractPdfStructure(String content) {
        Map<String, Object> metadata = new HashMap<>();

        // PDF特有的结构分析
        // 这里可以根据PDF的特点进行扩展
        metadata.put("estimated_pages", Math.max(1, content.length() / 2000)); // 估算页数

        return metadata;
    }

    /**
     * 提取Word结构信息
     */
    private Map<String, Object> extractWordStructure(String content) {
        Map<String, Object> metadata = new HashMap<>();

        // Word特有的结构分析
        // 这里可以根据Word的特点进行扩展
        metadata.put("estimated_pages", Math.max(1, content.length() / 2500)); // 估算页数

        return metadata;
    }

    /**
     * 提取关键词
     */
    private List<String> extractKeywords(String content) {
        Map<String, Integer> wordFreq = new HashMap<>();

        // 提取中文关键词
        Matcher chineseMatcher = CHINESE_KEYWORD_PATTERN.matcher(content);
        while (chineseMatcher.find()) {
            String word = chineseMatcher.group().toLowerCase();
            if (!STOP_WORDS.contains(word) && word.length() >= 2) {
                wordFreq.put(word, wordFreq.getOrDefault(word, 0) + 1);
            }
        }

        // 提取英文关键词
        Matcher englishMatcher = ENGLISH_KEYWORD_PATTERN.matcher(content);
        while (englishMatcher.find()) {
            String word = englishMatcher.group().toLowerCase();
            if (!STOP_WORDS.contains(word) && word.length() >= 3) {
                wordFreq.put(word, wordFreq.getOrDefault(word, 0) + 1);
            }
        }

        // 按频率排序，返回前10个关键词
        return wordFreq.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .limit(10)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    /**
     * 生成摘要
     */
    private String generateSummary(String content) {
        if (content.length() <= 100) {
            return content;
        }

        // 简单的摘要生成：取前100个字符，确保在句子边界结束
        String summary = content.substring(0, 100);
        int lastSentenceEnd = Math.max(
            Math.max(summary.lastIndexOf('。'), summary.lastIndexOf('.')),
            Math.max(summary.lastIndexOf('！'), summary.lastIndexOf('!'))
        );
        
        if (lastSentenceEnd > 20) {
            summary = summary.substring(0, lastSentenceEnd + 1);
        }
        
        return summary;
    }

    /**
     * 检测语言
     */
    private String detectLanguage(String content) {
        long chineseCharCount = content.chars().filter(c -> c >= 0x4e00 && c <= 0x9fa5).count();
        long englishCharCount = content.chars().filter(c -> (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z')).count();
        
        if (chineseCharCount > englishCharCount) {
            return "zh";
        } else if (englishCharCount > 0) {
            return "en";
        } else {
            return "unknown";
        }
    }

    /**
     * 分析内容类型
     */
    private String analyzeContentType(String content) {
        if (content.contains("```") || content.contains("function") || content.contains("class")) {
            return "code";
        } else if (content.contains("#") && content.contains("##")) {
            return "documentation";
        } else if (content.length() > 1000 && countSentences(content) > 10) {
            return "article";
        } else {
            return "text";
        }
    }

    /**
     * 计算可读性评分
     */
    private double calculateReadabilityScore(String content) {
        int wordCount = countWords(content);
        int sentenceCount = countSentences(content);
        
        if (sentenceCount == 0) return 0.0;
        
        double avgWordsPerSentence = (double) wordCount / sentenceCount;
        
        // 简化的可读性评分：理想的句子长度是10-20个词
        if (avgWordsPerSentence >= 10 && avgWordsPerSentence <= 20) {
            return 100.0;
        } else if (avgWordsPerSentence < 5 || avgWordsPerSentence > 30) {
            return 30.0;
        } else {
            return 70.0;
        }
    }

    /**
     * 计算信息密度
     */
    private double calculateInformationDensity(String content) {
        int totalChars = content.length();
        int meaningfulChars = content.replaceAll("\\s+", "").length();
        
        if (totalChars == 0) return 0.0;
        
        return (double) meaningfulChars / totalChars * 100.0;
    }

    /**
     * 计算完整性评分
     */
    private double calculateCompletenessScore(String content) {
        // 检查是否有完整的句子结构
        boolean hasCompleteStructure = content.contains("。") || content.contains(".") || 
                                     content.contains("！") || content.contains("!") ||
                                     content.contains("？") || content.contains("?");
        
        if (!hasCompleteStructure) return 30.0;
        
        // 检查长度是否合适
        if (content.length() < 20) return 50.0;
        if (content.length() > 50) return 100.0;
        
        return 80.0;
    }

    /**
     * 统计词数
     */
    private int countWords(String content) {
        if (StrUtil.isBlank(content)) return 0;
        
        // 中文按字符计算，英文按单词计算
        long chineseChars = content.chars().filter(c -> c >= 0x4e00 && c <= 0x9fa5).count();
        String[] englishWords = content.replaceAll("[\\u4e00-\\u9fa5]", "").split("\\s+");
        long englishWordCount = Arrays.stream(englishWords).filter(word -> !word.trim().isEmpty()).count();
        
        return (int) (chineseChars + englishWordCount);
    }

    /**
     * 统计段落数
     */
    private int countParagraphs(String content) {
        if (StrUtil.isBlank(content)) return 0;
        return content.split("\n\n").length;
    }

    /**
     * 统计句子数
     */
    private int countSentences(String content) {
        if (StrUtil.isBlank(content)) return 0;
        return content.split("[。！？.!?]").length;
    }
}
