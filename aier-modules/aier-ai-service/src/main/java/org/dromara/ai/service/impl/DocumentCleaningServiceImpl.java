package org.dromara.ai.service.impl;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.dromara.ai.service.IDocumentCleaningService;
import org.springframework.ai.document.Document;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * 文档清洗服务实现
 * 提供智能化的文档内容清洗、格式规范化功能
 * 
 * <AUTHOR> Puppy
 */
@Slf4j
@Service
public class DocumentCleaningServiceImpl implements IDocumentCleaningService {

    // 页眉页脚模式（常见的页眉页脚标识）
    private static final Pattern HEADER_FOOTER_PATTERN = Pattern.compile(
        "(?i)(第\\s*\\d+\\s*页|page\\s*\\d+|共\\s*\\d+\\s*页|\\d+\\s*/\\s*\\d+|页码|目录|contents|index)", 
        Pattern.MULTILINE
    );

    // 重复内容模式（连续重复的字符或短语）
    private static final Pattern DUPLICATE_PATTERN = Pattern.compile(
        "(.)\\1{10,}|(.{2,10})\\2{5,}", 
        Pattern.MULTILINE
    );

    // 无意义字符序列模式
    private static final Pattern MEANINGLESS_PATTERN = Pattern.compile(
        "[\\s\\-_=]{10,}|[\\*#@]{5,}|[\\d\\s]{20,}", 
        Pattern.MULTILINE
    );

    // 多余空白字符模式
    private static final Pattern EXCESSIVE_WHITESPACE_PATTERN = Pattern.compile(
        "\\s{3,}|\\n{3,}|\\t{2,}", 
        Pattern.MULTILINE
    );

    @Override
    public String cleanContent(String content, String documentType) {
        if (StrUtil.isBlank(content)) {
            return "";
        }

        log.debug("开始清洗{}类型文档内容，原始长度: {}", documentType, content.length());

        // 1. 去除噪声内容
        String cleanedContent = removeNoise(content, documentType);
        
        // 2. 格式规范化
        cleanedContent = normalizeFormat(cleanedContent);
        
        // 3. 最终验证和清理
        cleanedContent = finalCleanup(cleanedContent);

        log.debug("文档内容清洗完成，清洗后长度: {}", cleanedContent.length());
        return cleanedContent;
    }

    @Override
    public List<Document> cleanDocuments(List<Document> documents, String documentType) {
        if (documents == null || documents.isEmpty()) {
            return new ArrayList<>();
        }

        List<Document> cleanedDocuments = new ArrayList<>();
        
        for (Document document : documents) {
            String originalContent = document.getText();
            String cleanedContent = cleanContent(originalContent, documentType);
            
            // 只保留有效内容的文档
            if (isValidContent(cleanedContent)) {
                // 创建新的文档对象，保留原有元数据
                Document cleanedDocument = new Document(cleanedContent, document.getMetadata());
                
                // 添加清洗质量信息到元数据
                cleanedDocument.getMetadata().put("content_quality_score", evaluateContentQuality(cleanedContent));
                cleanedDocument.getMetadata().put("original_length", originalContent.length());
                cleanedDocument.getMetadata().put("cleaned_length", cleanedContent.length());
                cleanedDocument.getMetadata().put("cleaning_ratio", 
                    String.format("%.2f", (double) cleanedContent.length() / originalContent.length()));
                
                cleanedDocuments.add(cleanedDocument);
            } else {
                log.warn("文档内容清洗后无效，已过滤：{}", originalContent.substring(0, Math.min(50, originalContent.length())));
            }
        }

        log.info("批量文档清洗完成，原始文档数: {}, 有效文档数: {}", documents.size(), cleanedDocuments.size());
        return cleanedDocuments;
    }

    @Override
    public String normalizeFormat(String content) {
        if (StrUtil.isBlank(content)) {
            return "";
        }

        String normalized = content;

        // 1. 统一换行符
        normalized = normalized.replaceAll("\\r\\n|\\r", "\n");

        // 2. 统一中文标点符号
        normalized = normalized.replaceAll("，", "，")
                              .replaceAll("。", "。")
                              .replaceAll("；", "；")
                              .replaceAll("：", "：")
                              .replaceAll("？", "？")
                              .replaceAll("！", "！")
                              .replaceAll("\\u201C", "\"")  // 中文左引号转英文双引号
                              .replaceAll("\\u201D", "\"")  // 中文右引号转英文双引号
                              .replaceAll("\\u2018", "'")   // 中文左单引号转英文单引号
                              .replaceAll("\\u2019", "'");  // 中文右单引号转英文单引号

        // 3. 清理多余的空白字符
        normalized = EXCESSIVE_WHITESPACE_PATTERN.matcher(normalized).replaceAll(" ");

        // 4. 去除行首行尾空白
        normalized = normalized.lines()
                              .map(String::trim)
                              .filter(line -> !line.isEmpty())
                              .reduce((a, b) -> a + "\n" + b)
                              .orElse("");

        return normalized.trim();
    }

    @Override
    public String removeNoise(String content, String documentType) {
        if (StrUtil.isBlank(content)) {
            return "";
        }

        String denoised = content;

        // 1. 去除页眉页脚
        denoised = HEADER_FOOTER_PATTERN.matcher(denoised).replaceAll("");

        // 2. 去除重复内容
        denoised = DUPLICATE_PATTERN.matcher(denoised).replaceAll("$1$2");

        // 3. 去除无意义字符序列
        denoised = MEANINGLESS_PATTERN.matcher(denoised).replaceAll("");

        // 4. 根据文档类型进行特殊处理
        switch (documentType.toLowerCase()) {
            case "pdf":
                denoised = removePdfSpecificNoise(denoised);
                break;
            case "docx":
            case "doc":
                denoised = removeWordSpecificNoise(denoised);
                break;
            case "txt":
            case "md":
            case "markdown":
                denoised = removeTextSpecificNoise(denoised);
                break;
        }

        return denoised;
    }

    @Override
    public double evaluateContentQuality(String content) {
        if (StrUtil.isBlank(content)) {
            return 0.0;
        }

        double score = 100.0;
        int length = content.length();

        // 1. 长度评分（20分）
        if (length < 10) {
            score -= 20;
        } else if (length < 50) {
            score -= 10;
        } else if (length > 2000) {
            score -= 5; // 过长的内容可能包含噪声
        }

        // 2. 字符多样性评分（20分）
        long uniqueChars = content.chars().distinct().count();
        double diversity = (double) uniqueChars / length;
        if (diversity < 0.1) {
            score -= 20;
        } else if (diversity < 0.2) {
            score -= 10;
        }

        // 3. 语义完整性评分（30分）
        // 检查是否包含完整的句子
        long sentenceCount = content.split("[。！？.!?]").length;
        if (sentenceCount == 0) {
            score -= 30;
        } else if (sentenceCount == 1 && length > 100) {
            score -= 15; // 过长的单句可能不完整
        }

        // 4. 噪声比例评分（30分）
        // 检查数字、特殊字符的比例
        long digitCount = content.chars().filter(Character::isDigit).count();
        long specialCharCount = content.chars().filter(c -> !Character.isLetterOrDigit(c) && !Character.isWhitespace(c)).count();
        
        double noiseRatio = (double) (digitCount + specialCharCount) / length;
        if (noiseRatio > 0.5) {
            score -= 30;
        } else if (noiseRatio > 0.3) {
            score -= 15;
        }

        return Math.max(0, Math.min(100, score));
    }

    @Override
    public boolean isValidContent(String content) {
        if (StrUtil.isBlank(content)) {
            return false;
        }

        // 最小长度要求
        if (content.trim().length() < 5) {
            return false;
        }

        // 质量分数要求
        double qualityScore = evaluateContentQuality(content);
        if (qualityScore < 30) {
            return false;
        }

        // 检查是否包含有意义的文字
        long letterCount = content.chars().filter(Character::isLetter).count();
        if (letterCount < content.length() * 0.3) {
            return false; // 文字比例太低
        }

        return true;
    }

    /**
     * 最终清理
     * 进行最后的格式整理和验证
     */
    private String finalCleanup(String content) {
        if (StrUtil.isBlank(content)) {
            return "";
        }

        // 去除首尾空白
        String cleaned = content.trim();

        // 确保段落间有适当的分隔
        cleaned = cleaned.replaceAll("\\n{2,}", "\n\n");

        // 去除孤立的标点符号行
        cleaned = cleaned.lines()
                         .filter(line -> !line.matches("^[\\s\\p{Punct}]*$"))
                         .reduce((a, b) -> a + "\n" + b)
                         .orElse("");

        return cleaned;
    }

    /**
     * 去除PDF特有的噪声
     */
    private String removePdfSpecificNoise(String content) {
        // PDF特有的噪声模式
        return content.replaceAll("(?i)adobe|acrobat|pdf", "")
                     .replaceAll("\\b\\d{1,3}\\s*$", "") // 行尾页码
                     .replaceAll("^\\s*\\d{1,3}\\s*", ""); // 行首页码
    }

    /**
     * 去除Word特有的噪声
     */
    private String removeWordSpecificNoise(String content) {
        // Word特有的噪声模式
        return content.replaceAll("(?i)microsoft|word|office", "")
                     .replaceAll("\\[.*?\\]", "") // 去除方括号内容（可能是批注）
                     .replaceAll("\\{.*?\\}", ""); // 去除花括号内容（可能是域代码）
    }

    /**
     * 去除文本特有的噪声
     */
    private String removeTextSpecificNoise(String content) {
        // 文本文件特有的噪声模式
        return content.replaceAll("^#.*$", "") // 去除注释行
                     .replaceAll("^//.*$", "") // 去除注释行
                     .replaceAll("^\\*.*$", ""); // 去除星号开头的行
    }
}
