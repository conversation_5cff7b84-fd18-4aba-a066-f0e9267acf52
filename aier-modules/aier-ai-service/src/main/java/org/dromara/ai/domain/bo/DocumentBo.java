/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.ai.domain.bo;

import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.dromara.ai.domain.entity.AiDocument;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.core.validate.EditGroup;
import org.dromara.common.mybatis.core.domain.BaseEntity;

/**
 * 文档业务对象
 *
 * <AUTHOR> <PERSON>py
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AutoMapper(target = AiDocument.class, reverseConvertGenerate = false)
public class DocumentBo extends BaseEntity {

    /**
     * 文档ID
     */
    @NotNull(message = "文档ID不能为空", groups = {EditGroup.class})
    private Long documentId;

    /**
     * 知识库ID
     */
    @NotNull(message = "知识库ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long knowledgeId;

    /**
     * 文档名称
     */
    @NotBlank(message = "文档名称不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(min = 1, max = 200, message = "文档名称长度不能超过{max}个字符")
    private String documentName;

    /**
     * 原始文件名
     */
    private String originalFileName;

    /**
     * 文件类型
     */
    private String fileType;

    /**
     * 文件大小（字节）
     */
    private Long fileSize;

    /**
     * 文件存储路径
     */
    private String filePath;

    /**
     * 文档内容（文本内容）
     */
    private String content;

    /**
     * 文档摘要
     */
    @Size(max = 1000, message = "文档摘要长度不能超过{max}个字符")
    private String summary;

    /**
     * 文档状态（0正常 1停用）
     */
    private String status;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 备注
     */
    @Size(max = 500, message = "备注长度不能超过{max}个字符")
    private String remark;

}
