/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.ai.util;

import lombok.extern.slf4j.Slf4j;
import org.dromara.ai.config.DocumentProcessingConfig;
import org.dromara.ai.service.IDocumentCleaningService;
import org.springframework.ai.document.Document;
import org.springframework.ai.reader.pdf.PagePdfDocumentReader;
import org.springframework.ai.reader.pdf.config.PdfDocumentReaderConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * PDF文档处理器
 * 使用Spring AI的PDF文档读取器处理PDF文件
 * 集成智能化文档处理流程：清洗、分割、元数据增强
 *
 * <AUTHOR> Puppy
 */
@Slf4j
@Component
public class PdfDocumentProcessor implements DocumentProcessor {

    @Autowired
    private IDocumentCleaningService documentCleaningService;

    @Autowired
    private DocumentMetadataEnhancer metadataEnhancer;

    @Autowired
    private DocumentProcessingConfig processingConfig;

    @Override
    public boolean supports(String fileType) {
        return "pdf".equalsIgnoreCase(fileType);
    }

    @Override
    public List<Document> process(InputStream inputStream, Map<String, Object> metadata) throws Exception {
        log.info("开始智能化处理PDF文档，元数据: {}", metadata);

        try {
            // 1. 预处理：读取PDF文档
            List<Document> rawDocuments = preprocess(inputStream, metadata);

            // 2. 文档清洗
            List<Document> cleanedDocuments = documentCleaningService.cleanDocuments(rawDocuments, "pdf");

            // 3. 智能分割
            List<Document> splitDocuments = smartSplit(cleanedDocuments);

            // 4. 元数据增强
            List<Document> enhancedDocuments = enhanceMetadata(splitDocuments, metadata);

            // 5. 质量验证
            List<Document> finalDocuments = validateQuality(enhancedDocuments);

            log.info("PDF文档智能化处理完成 - 原始: {}, 清洗后: {}, 分割后: {}, 最终: {}",
                rawDocuments.size(), cleanedDocuments.size(), splitDocuments.size(), finalDocuments.size());

            return finalDocuments;

        } catch (Exception e) {
            log.error("PDF文档智能化处理失败", e);
            throw new RuntimeException("PDF文档智能化处理失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<Document> preprocess(InputStream inputStream, Map<String, Object> metadata) throws Exception {
        log.debug("开始PDF文档预处理");

        // 创建PDF文档读取器配置
        PdfDocumentReaderConfig config = PdfDocumentReaderConfig.builder()
            .withPageTopMargin(0)
            .withPagesPerDocument(1)  // 每页作为一个文档
            .build();

        // 创建PDF文档读取器
        PagePdfDocumentReader pdfReader = new PagePdfDocumentReader(
            new InputStreamResource(inputStream), config);

        // 读取文档
        List<Document> documents = pdfReader.get();

        // 为每个文档添加基础元数据和页面信息
        for (int i = 0; i < documents.size(); i++) {
            Document document = documents.get(i);
            Map<String, Object> docMetadata = new HashMap<>(metadata);

            // 添加PDF特有的元数据
            docMetadata.put("page_number", i + 1);
            docMetadata.put("total_pages", documents.size());
            docMetadata.put("document_type", "pdf");
            docMetadata.put("source_format", "pdf");

            document.getMetadata().putAll(docMetadata);
        }

        log.debug("PDF文档预处理完成，页数: {}", documents.size());
        return documents;
    }

    @Override
    public List<Document> smartSplit(List<Document> documents) {
        if (documents == null || documents.isEmpty()) {
            return new ArrayList<>();
        }

        log.debug("开始PDF文档智能分割，文档数: {}", documents.size());

        // 获取PDF特定的分块配置
        int chunkSize = processingConfig.getChunkSizeForType("pdf");
        double overlapRatio = processingConfig.getOverlapRatioForType("pdf");

        // 使用智能分割器，针对PDF优化分割策略
        SmartTextSplitter pdfSplitter = new SmartTextSplitter(chunkSize,
            processingConfig.getSmartSplitter().getMinChunkSize(),
            processingConfig.getSmartSplitter().getMaxChunkSize(),
            overlapRatio);

        List<Document> splitDocuments = pdfSplitter.splitDocuments(documents, "pdf");

        log.debug("PDF文档智能分割完成，分割后块数: {}", splitDocuments.size());
        return splitDocuments;
    }

    @Override
    public List<Document> enhanceMetadata(List<Document> documents, Map<String, Object> additionalMetadata) {
        if (documents == null || documents.isEmpty()) {
            return new ArrayList<>();
        }

        log.debug("开始PDF文档元数据增强，文档数: {}", documents.size());

        // 添加PDF特有的元数据
        Map<String, Object> pdfMetadata = new HashMap<>(additionalMetadata != null ? additionalMetadata : new HashMap<>());
        pdfMetadata.put("processor_type", "pdf_smart");
        pdfMetadata.put("processing_strategy", "page_aware_splitting");

        List<Document> enhancedDocuments = metadataEnhancer.enhanceDocumentsMetadata(documents, "pdf", pdfMetadata);

        log.debug("PDF文档元数据增强完成");
        return enhancedDocuments;
    }

    @Override
    public List<Document> validateQuality(List<Document> documents) {
        if (documents == null || documents.isEmpty()) {
            return new ArrayList<>();
        }

        log.debug("开始PDF文档质量验证，文档数: {}", documents.size());

        List<Document> validDocuments = new ArrayList<>();
        int filteredCount = 0;

        for (Document document : documents) {
            // 检查文档内容质量
            if (documentCleaningService.isValidContent(document.getText())) {
                // 检查质量分数
                Object qualityScore = document.getMetadata().get("overall_quality_score");
                if (qualityScore instanceof Double && (Double) qualityScore >= processingConfig.getQuality().getMinOverallQualityScore()) {
                    validDocuments.add(document);
                } else {
                    filteredCount++;
                    log.debug("过滤低质量PDF文档块，质量分数: {}", qualityScore);
                }
            } else {
                filteredCount++;
                log.debug("过滤无效PDF文档块，内容长度: {}", document.getText().length());
            }
        }

        log.debug("PDF文档质量验证完成，有效文档: {}, 过滤文档: {}", validDocuments.size(), filteredCount);
        return validDocuments;
    }

    @Override
    public List<String> getSupportedFileTypes() {
        return List.of("pdf");
    }

    @Override
    public String getProcessorName() {
        return "智能PDF文档处理器";
    }

    @Override
    public String getProcessorVersion() {
        return "2.0.0-smart";
    }

    @Override
    public boolean supportsSmartProcessing() {
        return true;
    }

    @Override
    public String getConfigurationInfo() {
        return String.format("SmartPdfProcessor[chunk_size=%d, overlap=%.2f, cleaning=%s, metadata=%s]",
            processingConfig.getChunkSizeForType("pdf"),
            processingConfig.getOverlapRatioForType("pdf"),
            processingConfig.getCleaning().isEnabled(),
            processingConfig.getMetadata().isEnabled());
    }

}
