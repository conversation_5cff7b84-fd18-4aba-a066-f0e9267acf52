/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.ai.controller.platform;

import cn.dev33.satoken.annotation.SaIgnore;
import cn.hutool.core.util.StrUtil;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.tenant.helper.TenantHelper;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.vectorstore.QuestionAnswerAdvisor;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.document.Document;
import org.springframework.ai.vectorstore.SearchRequest;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.filter.Filter;
import org.springframework.ai.vectorstore.filter.FilterExpressionBuilder;
import org.dromara.common.core.domain.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Flux;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/ai")
@SaIgnore
@Slf4j
public class RagController {

	private final VectorStore vectorStore;

	private final ChatClient chatClient;

	public RagController(VectorStore vectorStore, ChatClient.Builder chatClient) {
		this.vectorStore = vectorStore;
		this.chatClient = chatClient.build();
	}

	// 历史消息列表
	private static List<Message> historyMessage = new ArrayList<>();

	// 历史消息列表的最大长度
	private final static int maxLen = 10;

	@GetMapping(value = "/chat")
	public Flux<String> generation(@RequestParam("prompt") String userInput,
									@RequestParam(value = "knowledgeId", required = false) String knowledgeId,
									HttpServletResponse response) {

		response.setCharacterEncoding("UTF-8");

		// 构建基于租户和知识库的搜索请求
		Filter.Expression filterExpression = buildTenantFilterExpression(knowledgeId);
		SearchRequest searchRequest = SearchRequest.builder()
			.filterExpression(filterExpression)
			.topK(5)
			.build();

		// 发起聊天请求并处理响应
		Flux<String> resp = chatClient.prompt()
			.messages(historyMessage)
			.user(userInput)
			.advisors(QuestionAnswerAdvisor
					.builder(vectorStore)
					.searchRequest(searchRequest)
					.build()
			)
			.stream()
			.content();

		// 用户输入的文本是 UserMessage
		historyMessage.add(new UserMessage(userInput));

		// 发给 AI 前对历史消息对列的长度进行检查
		if (historyMessage.size() > maxLen) {
			historyMessage = historyMessage.subList(historyMessage.size() - maxLen - 1, historyMessage.size());
		}

		return resp;
	}

	/**
	 * 向量数据查询测试
	 */
	@GetMapping("/select")
	public R<Map<String, Object>> search(@RequestParam(value = "query", defaultValue = "SpringAIAlibaba") String query,
										  @RequestParam(value = "knowledgeId", required = false) String knowledgeId,
										  @RequestParam(value = "debug", defaultValue = "false") boolean debug) {

		// 构建基于租户和知识库的搜索请求
		Filter.Expression filterExpression = buildTenantFilterExpression(knowledgeId);

		SearchRequest searchRequest = SearchRequest.builder()
			.query(query)
			.topK(Math.max(query.length(), 5))
			.filterExpression(filterExpression)
			.build();

		List<Document> documents = vectorStore.similaritySearch(searchRequest);

		if (debug) {
			// 调试模式：返回详细信息
			Map<String, Object> result = new HashMap<>();
			result.put("query", query);
			result.put("tenantId", TenantHelper.getTenantId());
			result.put("knowledgeId", StrUtil.isBlank(knowledgeId) ? "1" : knowledgeId);
			result.put("filterExpression", filterExpression.toString());
			result.put("documentCount", documents.size());
			result.put("documents", documents);

			log.info("搜索完成 - 查询: {}, 租户: {}, 知识库: {}, 结果数: {}",
				query, result.get("tenantId"), result.get("knowledgeId"), documents.size());

			return R.ok(result);
		} else {
			// 普通模式：只返回文档列表
			Map<String, Object> result = new HashMap<>();
			result.put("documents", documents);
			return R.ok(result);
		}
	}

	/**
	 * 构建基于租户和知识库的过滤表达式
	 */
	private Filter.Expression buildTenantFilterExpression(String knowledgeId) {
		// 获取当前租户ID
		String currentTenantId = TenantHelper.getTenantId();
		log.info("原始租户ID: {}", currentTenantId);

		// 如果没有获取到租户ID，使用默认值
		if (StrUtil.isBlank(currentTenantId)) {
			currentTenantId = "000000"; // 默认租户ID
			log.warn("未获取到租户ID，使用默认值: {}", currentTenantId);
		}

		// 如果没有指定知识库ID，使用默认值
		if (StrUtil.isBlank(knowledgeId)) {
			knowledgeId = "1930533243142770689"; // 默认知识库ID
			log.info("未指定知识库ID，使用默认值: {}", knowledgeId);
		}

		log.info("构建过滤条件 - 租户ID: {}, 知识库ID: {}", currentTenantId, knowledgeId);

		// 构建过滤表达式
		FilterExpressionBuilder filterBuilder = new FilterExpressionBuilder();
		Filter.Expression filterExpression = filterBuilder.and(
			filterBuilder.eq("tenant_id", currentTenantId),
			filterBuilder.eq("knowledge_id", knowledgeId)
		).build();

		log.info("过滤表达式构建完成: {}", filterExpression);
		return filterExpression;
	}

	/**
	 * 租户隔离验证接口 - 调试专用
	 */
	@GetMapping("/debug/tenant-isolation")
	public R<Map<String, Object>> debugTenantIsolation(@RequestParam(value = "knowledgeId", required = false) String knowledgeId) {
		Map<String, Object> result = new HashMap<>();

		// 获取当前租户信息
		String currentTenantId = TenantHelper.getTenantId();
		result.put("originalTenantId", currentTenantId);
		result.put("finalTenantId", StrUtil.isBlank(currentTenantId) ? "194338" : currentTenantId);
		result.put("knowledgeId", StrUtil.isBlank(knowledgeId) ? "1" : knowledgeId);

		// 构建过滤表达式
		Filter.Expression filterExpression = buildTenantFilterExpression(knowledgeId);
		result.put("filterExpression", filterExpression.toString());

		// 执行搜索测试
		SearchRequest searchRequest = SearchRequest.builder()
			.query("SpringAI")
			.topK(10)
			.filterExpression(filterExpression)
			.build();

		List<Document> documents = vectorStore.similaritySearch(searchRequest);
		result.put("documentCount", documents.size());

		// 提取文档内容和元数据用于验证
		List<Map<String, Object>> documentDetails = new ArrayList<>();
		for (Document doc : documents) {
			Map<String, Object> docInfo = new HashMap<>();
			docInfo.put("content", doc.getText());
			docInfo.put("metadata", doc.getMetadata());
			documentDetails.add(docInfo);
		}
		result.put("documents", documentDetails);

		log.info("租户隔离验证完成 - 租户ID: {}, 知识库ID: {}, 查询到文档数: {}",
			result.get("finalTenantId"), result.get("knowledgeId"), documents.size());

		return R.ok(result);
	}

	/**
	 * 无过滤条件的查询接口 - 用于对比验证
	 */
	@GetMapping("/debug/no-filter")
	public R<Map<String, Object>> debugNoFilter() {
		Map<String, Object> result = new HashMap<>();

		// 不使用任何过滤条件的搜索
		SearchRequest searchRequest = SearchRequest.builder()
			.query("SpringAI")
			.topK(100)
			.build();

		List<Document> documents = vectorStore.similaritySearch(searchRequest);
		result.put("documentCount", documents.size());

		// 按租户统计文档数量
		Map<String, Integer> tenantStats = new HashMap<>();
		Map<String, Integer> knowledgeStats = new HashMap<>();

		for (Document doc : documents) {
			String tenantId = (String) doc.getMetadata().get("tenant_id");
			String knowledgeId = (String) doc.getMetadata().get("knowledge_id");

			tenantStats.put(tenantId, tenantStats.getOrDefault(tenantId, 0) + 1);
			knowledgeStats.put(knowledgeId, knowledgeStats.getOrDefault(knowledgeId, 0) + 1);
		}

		result.put("tenantStats", tenantStats);
		result.put("knowledgeStats", knowledgeStats);

		// 提取所有文档的详细信息
		List<Map<String, Object>> documentDetails = new ArrayList<>();
		for (Document doc : documents) {
			Map<String, Object> docInfo = new HashMap<>();
			docInfo.put("content", doc.getText());
			docInfo.put("metadata", doc.getMetadata());
			documentDetails.add(docInfo);
		}
		result.put("documents", documentDetails);

		log.info("无过滤查询完成 - 总文档数: {}, 租户统计: {}", documents.size(), tenantStats);

		return R.ok(result);
	}

}
